'use strict';

/**
 * 性能监控中间件
 * 监控请求响应时间、内存使用、热加载响应时间等
 */

module.exports = (options = {}) => {
  return async function performanceMiddleware(ctx, next) {
    const startTime = Date.now();
    const startMemory = process.memoryUsage();
    
    // 记录请求开始
    const requestId = `${Date.now()}-${Math.random().toString(36).substr(2, 9)}`;
    
    // 如果是开发环境且启用了详细日志
    const isVerbose = options.verbose && ctx.app.config.env !== 'prod';
    
    if (isVerbose) {
      console.log(`🔍 [${requestId}] ${ctx.method} ${ctx.url} - 开始处理`);
    }
    
    try {
      // 执行下一个中间件
      await next();
      
      // 计算响应时间
      const responseTime = Date.now() - startTime;
      const endMemory = process.memoryUsage();
      
      // 计算内存变化
      const memoryDelta = {
        rss: endMemory.rss - startMemory.rss,
        heapUsed: endMemory.heapUsed - startMemory.heapUsed,
        heapTotal: endMemory.heapTotal - startMemory.heapTotal,
        external: endMemory.external - startMemory.external,
      };
      
      // 设置响应头
      ctx.set('X-Response-Time', `${responseTime}ms`);
      ctx.set('X-Request-ID', requestId);
      
      // 记录性能数据
      const performanceData = {
        requestId,
        method: ctx.method,
        url: ctx.url,
        status: ctx.status,
        responseTime,
        memoryDelta,
        timestamp: new Date().toISOString(),
      };
      
      // 如果响应时间超过阈值，记录慢请求
      const slowThreshold = options.slowThreshold || 1000; // 默认1秒
      if (responseTime > slowThreshold) {
        console.warn(`🐌 [慢请求] ${ctx.method} ${ctx.url} - ${responseTime}ms`);
        
        // 记录到慢请求日志
        if (global.performanceMonitor) {
          if (!global.performanceMonitor.slowRequests) {
            global.performanceMonitor.slowRequests = [];
          }
          global.performanceMonitor.slowRequests.push(performanceData);
          
          // 只保留最近50个慢请求
          if (global.performanceMonitor.slowRequests.length > 50) {
            global.performanceMonitor.slowRequests = global.performanceMonitor.slowRequests.slice(-50);
          }
        }
      }
      
      // 详细日志输出
      if (isVerbose) {
        console.log(`✅ [${requestId}] ${ctx.method} ${ctx.url} - ${responseTime}ms (${ctx.status})`);
        if (memoryDelta.heapUsed > 1024 * 1024) { // 超过1MB内存变化
          console.log(`   💾 内存变化: ${Math.round(memoryDelta.heapUsed / 1024 / 1024 * 100) / 100}MB`);
        }
      }
      
      // 记录到全局性能监控器
      if (global.performanceMonitor) {
        if (!global.performanceMonitor.requests) {
          global.performanceMonitor.requests = [];
        }
        global.performanceMonitor.requests.push(performanceData);
        
        // 只保留最近100个请求
        if (global.performanceMonitor.requests.length > 100) {
          global.performanceMonitor.requests = global.performanceMonitor.requests.slice(-100);
        }
      }
      
    } catch (error) {
      const responseTime = Date.now() - startTime;
      
      // 记录错误请求
      console.error(`❌ [${requestId}] ${ctx.method} ${ctx.url} - ${responseTime}ms - 错误: ${error.message}`);
      
      // 设置响应头
      ctx.set('X-Response-Time', `${responseTime}ms`);
      ctx.set('X-Request-ID', requestId);
      
      throw error;
    }
  };
};

/**
 * 获取性能统计信息
 */
function getPerformanceStats() {
  if (!global.performanceMonitor) {
    return null;
  }
  
  const { requests = [], slowRequests = [] } = global.performanceMonitor;
  
  if (requests.length === 0) {
    return {
      totalRequests: 0,
      averageResponseTime: 0,
      slowRequestsCount: 0,
    };
  }
  
  const responseTimes = requests.map(req => req.responseTime);
  const totalResponseTime = responseTimes.reduce((sum, time) => sum + time, 0);
  
  return {
    totalRequests: requests.length,
    averageResponseTime: Math.round(totalResponseTime / requests.length),
    minResponseTime: Math.min(...responseTimes),
    maxResponseTime: Math.max(...responseTimes),
    slowRequestsCount: slowRequests.length,
    recentRequests: requests.slice(-10), // 最近10个请求
    slowRequests: slowRequests.slice(-10), // 最近10个慢请求
  };
}

module.exports.getPerformanceStats = getPerformanceStats;
