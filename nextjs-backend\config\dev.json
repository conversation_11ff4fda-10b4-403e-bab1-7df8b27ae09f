{"server": {"port": 3000, "hostname": "127.0.0.1"}, "database": {"mysql": {"connectionLimit": 5, "acquireTimeout": 60000, "timeout": 60000}}, "redis": {"connectTimeout": 10000, "maxRetriesPerRequest": 3, "lazyConnect": true}, "websocket": {"port": 3001}, "logging": {"level": "DEBUG", "dir": "./logs/dev"}, "cluster": {"workers": 1}, "watcher": {"enabled": true, "directories": ["src/app", "src/lib", "config"], "ignore": ["**/node_modules/**", "**/*.log", "**/logs/**"]}, "lazyLoading": {"enabled": true, "heavyModules": ["mysql2", "i<PERSON>is", "ws", "sharp"]}}