/**
 * 粉笔评论获取API
 * 迁移自egg项目的fb.js pinglun方法
 */

const { NextResponse } = require('next/server');
const { app } = require('../../../../lib/app');
const axios = require('axios');

// 确保应用已初始化
let appInitialized = false;

async function ensureAppInitialized() {
  if (!appInitialized) {
    await app.init();
    appInitialized = true;
  }
}

async function GET(request) {
  try {
    await ensureAppInitialized();
    
    const { searchParams } = new URL(request.url);
    const id = searchParams.get('id') || '5911268';
    const type = searchParams.get('type') || 'xingce';
    
    console.log('📝 获取评论信息 - ID:', id, 'Type:', type);
    
    // 获取cookie
    const cookie = await app.redis.get('fbcookie');
    if (!cookie) {
      return NextResponse.json({ 
        success: false, 
        message: '未找到fbcookie', 
        code: 401 
      }, { status: 401 });
    }
    
    // 设置请求头
    const headers = {
      Cookie: cookie,
      'Content-Type': 'application/x-www-form-urlencoded',
      'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/119.0.0.0 Safari/537.36 Edg/119.0.0.0',
    };
    
    // 第一步：请求问题集信息
    const infoUrl = 'https://ke.fenbi.com/api/gwy/v3/episodes/question_episodes_with_multi_type';
    
    try {
      console.log('🔍 请求问题集信息...');
      const infoResponse = await axios.get(infoUrl, {
        headers,
        params: { 
          question_ids: id, 
          tiku_prefix: type 
        },
      });
      
      console.log('📊 问题集信息响应:', {
        status: infoResponse.status,
        dataKeys: Object.keys(infoResponse.data || {}),
        hasData: !!infoResponse.data?.data
      });
      
      // 获取评论ID
      const plid = infoResponse.data?.data?.[id]?.[0]?.id;
      
      if (plid) {
        console.log('✅ 找到评论ID:', plid);
        
        // 第二步：请求评论信息
        const commentUrl = `https://ke.fenbi.com/api/gwy/v3/comments/episodes/${plid}`;
        
        console.log('💬 请求评论信息...');
        const commentResponse = await axios.get(commentUrl, {
          headers,
          params: { 
            len: 300, 
            start: 0 
          },
        });
        
        console.log('📝 评论信息响应:', {
          status: commentResponse.status,
          dataKeys: Object.keys(commentResponse.data || {}),
          commentCount: commentResponse.data?.data?.length || 0
        });
        
        // 返回评论数据
        return NextResponse.json(commentResponse.data);
        
      } else {
        console.log('❌ 未找到对应的评论ID');
        return NextResponse.json({ 
          success: false, 
          message: '未找到对应题目或评论', 
          code: 200 
        });
      }
      
    } catch (error) {
      console.error('❌ 请求失败:', error.message);
      console.error('错误详情:', {
        status: error.response?.status,
        statusText: error.response?.statusText,
        url: error.config?.url,
        params: error.config?.params,
      });
      
      return NextResponse.json({ 
        success: false, 
        message: '请求失败', 
        code: 200, 
        error: error.message 
      });
    }
    
  } catch (error) {
    console.error('❌ pinglun API错误:', error.message);
    return NextResponse.json({ 
      success: false,
      message: '服务器内部错误',
      error: error.message,
      timestamp: new Date().toISOString()
    }, { status: 500 });
  }
}

module.exports = { GET };
