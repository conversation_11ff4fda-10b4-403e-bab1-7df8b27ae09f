/**
 * 设置题目为空值API
 * 迁移自egg项目的fb.js setnull方法
 */

const { NextResponse } = require('next/server');
const { app } = require('../../../../lib/app');

// 确保应用已初始化
let appInitialized = false;

async function ensureAppInitialized() {
  if (!appInitialized) {
    await app.init();
    appInitialized = true;
  }
}

async function GET(request) {
  try {
    await ensureAppInitialized();
    
    const { searchParams } = new URL(request.url);
    const id = searchParams.get('id');
    const type = searchParams.get('type') || 'choice';
    const biao = searchParams.get('biao') || 'fbsy';
    
    console.log('🗑️ 设置空值 - 参数:', { id, type, biao });
    
    if (!id) {
      return NextResponse.json({ 
        error: '缺少id参数' 
      }, { status: 400 });
    }
    
    // 根据类型设置不同的字段为空
    let updateData = {};
    
    switch (type) {
      case 'choice':
        updateData.choice = null;
        break;
      case 'userAnswer':
        updateData.userAnswer = null;
        break;
      case 'all':
        updateData.choice = null;
        updateData.userAnswer = null;
        break;
      default:
        updateData.choice = null;
    }
    
    try {
      // 首先检查题目是否存在
      const existingTimu = await app.mysql.get(biao, { id: parseInt(id) });
      
      if (!existingTimu) {
        return NextResponse.json({ 
          error: '题目不存在' 
        }, { status: 404 });
      }
      
      // 执行更新
      const updateResult = await app.mysql.update(
        biao,
        updateData,
        { where: { id: parseInt(id) } }
      );
      
      console.log('✅ 空值设置成功:', {
        id,
        type,
        affectedRows: updateResult.affectedRows,
        updateData
      });
      
      // 获取更新后的数据
      const updatedTimu = await app.mysql.get(biao, { id: parseInt(id) });
      
      return NextResponse.json({
        success: true,
        message: '空值设置成功',
        data: {
          id: parseInt(id),
          type,
          updateData,
          affectedRows: updateResult.affectedRows,
          updatedTimu
        }
      });
      
    } catch (dbError) {
      console.error('❌ 数据库操作失败:', dbError.message);
      return NextResponse.json({ 
        error: '数据库操作失败: ' + dbError.message 
      }, { status: 500 });
    }
    
  } catch (error) {
    console.error('❌ setnull API错误:', error.message);
    return NextResponse.json({ 
      error: error.message,
      timestamp: new Date().toISOString()
    }, { status: 500 });
  }
}

module.exports = { GET };
