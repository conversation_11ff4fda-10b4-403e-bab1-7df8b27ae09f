// This file is created by egg-ts-helper@2.1.1
// Do not modify this file!!!!!!!!!
/* eslint-disable */

import 'egg';
import ExportAi = require('../../../app/controller/ai');
import ExportAione = require('../../../app/controller/aione');
import ExportAir = require('../../../app/controller/air');
import ExportAli = require('../../../app/controller/ali');
import ExportDd = require('../../../app/controller/dd');
import ExportDeploy = require('../../../app/controller/deploy');
import ExportFb = require('../../../app/controller/fb');
import ExportFeishu = require('../../../app/controller/feishu');
import ExportHome = require('../../../app/controller/home');
import ExportLog = require('../../../app/controller/log');
import ExportLogin = require('../../../app/controller/login');
import ExportMin = require('../../../app/controller/min');
import ExportNews = require('../../../app/controller/news');
import ExportNotification = require('../../../app/controller/notification');
import ExportPc = require('../../../app/controller/pc');
import ExportSchedule = require('../../../app/controller/schedule');
import ExportSw = require('../../../app/controller/sw');
import ExportSydw = require('../../../app/controller/sydw');
import ExportTele = require('../../../app/controller/tele');
import ExportTx = require('../../../app/controller/tx');
import ExportTz = require('../../../app/controller/tz');
import ExportWecom = require('../../../app/controller/wecom');
import ExportXrnew = require('../../../app/controller/xrnew');
import ExportXrplus = require('../../../app/controller/xrplus');

declare module 'egg' {
  interface IController {
    ai: ExportAi;
    aione: ExportAione;
    air: ExportAir;
    ali: ExportAli;
    dd: ExportDd;
    deploy: ExportDeploy;
    fb: ExportFb;
    feishu: ExportFeishu;
    home: ExportHome;
    log: ExportLog;
    login: ExportLogin;
    min: ExportMin;
    news: ExportNews;
    notification: ExportNotification;
    pc: ExportPc;
    schedule: ExportSchedule;
    sw: ExportSw;
    sydw: ExportSydw;
    tele: ExportTele;
    tx: ExportTx;
    tz: ExportTz;
    wecom: ExportWecom;
    xrnew: ExportXrnew;
    xrplus: ExportXrplus;
  }
}
