/**
 * 配置管理工具
 * 支持多环境配置加载和合并
 */

const fs = require('fs');
const path = require('path');

// 配置缓存
let configCache = null;

/**
 * 获取当前环境
 * @returns {string} 环境名称
 */
function getEnvironment() {
  return process.env.NODE_ENV || 'development';
}

/**
 * 加载JSON配置文件
 * @param {string} filePath 配置文件路径
 * @returns {object|null} 配置对象或null
 */
function loadConfigFile(filePath) {
  try {
    if (fs.existsSync(filePath)) {
      const content = fs.readFileSync(filePath, 'utf8');
      return JSON.parse(content);
    }
  } catch (error) {
    console.error(`Failed to load config file ${filePath}:`, error.message);
  }
  return null;
}

/**
 * 深度合并配置对象
 * @param {object} target 目标对象
 * @param {object} source 源对象
 * @returns {object} 合并后的对象
 */
function deepMerge(target, source) {
  const result = { ...target };
  
  for (const key in source) {
    if (source.hasOwnProperty(key)) {
      if (
        typeof source[key] === 'object' && 
        source[key] !== null && 
        !Array.isArray(source[key]) &&
        typeof target[key] === 'object' && 
        target[key] !== null && 
        !Array.isArray(target[key])
      ) {
        result[key] = deepMerge(target[key], source[key]);
      } else {
        result[key] = source[key];
      }
    }
  }
  
  return result;
}

/**
 * 加载配置
 * @param {boolean} forceReload 是否强制重新加载
 * @returns {object} 配置对象
 */
function loadConfig(forceReload = false) {
  if (configCache && !forceReload) {
    return configCache;
  }

  const env = getEnvironment();
  const configDir = path.join(process.cwd(), 'config');
  
  // 加载默认配置
  const defaultConfig = loadConfigFile(path.join(configDir, 'default.json')) || {};
  
  // 加载环境特定配置
  let envConfig = {};
  const envConfigFile = env === 'production' ? 'prod.json' : 
                       env === 'development' ? 'dev.json' : 
                       `${env}.json`;
  
  envConfig = loadConfigFile(path.join(configDir, envConfigFile)) || {};
  
  // 合并配置
  const finalConfig = deepMerge(defaultConfig, envConfig);
  
  // 添加环境信息
  finalConfig.env = env;
  finalConfig.isDevelopment = env === 'development';
  finalConfig.isProduction = env === 'production';
  
  // 缓存配置
  configCache = finalConfig;
  
  return finalConfig;
}

/**
 * 获取配置项
 * @param {string} key 配置键，支持点号分隔的路径
 * @param {any} defaultValue 默认值
 * @returns {any} 配置值
 */
function getConfig(key, defaultValue = undefined) {
  const config = loadConfig();
  
  if (!key) {
    return config;
  }
  
  const keys = key.split('.');
  let value = config;
  
  for (const k of keys) {
    if (value && typeof value === 'object' && k in value) {
      value = value[k];
    } else {
      return defaultValue;
    }
  }
  
  return value;
}

/**
 * 重新加载配置
 */
function reloadConfig() {
  configCache = null;
  return loadConfig(true);
}

/**
 * 验证必需的配置项
 * @param {string[]} requiredKeys 必需的配置键数组
 * @throws {Error} 如果缺少必需配置
 */
function validateConfig(requiredKeys = []) {
  const config = loadConfig();
  const missing = [];
  
  for (const key of requiredKeys) {
    if (getConfig(key) === undefined) {
      missing.push(key);
    }
  }
  
  if (missing.length > 0) {
    throw new Error(`Missing required configuration: ${missing.join(', ')}`);
  }
}

module.exports = {
  getEnvironment,
  loadConfig,
  getConfig,
  reloadConfig,
  validateConfig,
  deepMerge
};
