{"terminal.integrated.profiles.windows": {"PowerShell Enhanced": {"source": "PowerShell", "args": ["-NoExit", "-Command", "& {", "Set-PSReadLineOption -PredictionSource History;", "Set-PSReadLineOption -PredictionViewStyle ListView;", "Set-PSReadLineOption -EditMode Windows;", "Set-PSReadLineKeyHandler -Key Tab -Function Complete;", "function prompt { Write-Host '🚀 ' -NoNewline -ForegroundColor Cyan; Write-Host (Get-Location) -ForegroundColor Yellow; Write-Host '❯ ' -NoNewline -ForegroundColor Green; return ' ' };", "Clear-Host;", "Write-Host '🎨 Enhanced Terminal for Egg.js Development' -ForegroundColor Magenta;", "Write-Host '💡 Available commands:' -ForegroundColo<PERSON>;", "Write-Host '  pnpm dev:all      - 启动前后端 (标准)' -ForegroundColor White;", "Write-Host '  pnpm dev:colorful - 启动前后端 (彩色)' -ForegroundColor White;", "Write-Host '  pnpm dev:fancy    - 启动前后端 (炫酷)' -ForegroundColor White;", "Write-Host '  pnpm dev:enhanced - 启动前后端 (增强)' -ForegroundColor White;", "Write-Host '';", "}"]}, "Git Bash Enhanced": {"path": "C:\\Program Files\\Git\\bin\\bash.exe", "args": ["--login", "-i", "-c", "echo -e '\\033[1;35m🎨 Enhanced Git Bash for Development\\033[0m'; echo -e '\\033[1;36m💡 Available commands:\\033[0m'; echo -e '  \\033[1;32mpnpm dev:all\\033[0m      - 启动前后端 (标准)'; echo -e '  \\033[1;33mpnpm dev:colorful\\033[0m - 启动前后端 (彩色)'; echo -e '  \\033[1;34mpnpm dev:fancy\\033[0m    - 启动前后端 (炫酷)'; echo -e '  \\033[1;35mpnpm dev:enhanced\\033[0m - 启动前后端 (增强)'; echo ''; exec bash"]}}, "terminal.integrated.defaultProfile.windows": "PowerShell Enhanced", "terminal.integrated.fontSize": 14, "terminal.integrated.fontFamily": "'JetBrains Mono', 'Fira Code', 'Cascadia Code', Consolas, monospace", "terminal.integrated.cursorBlinking": true, "terminal.integrated.cursorStyle": "line", "terminal.integrated.scrollback": 10000, "terminal.integrated.enableMultiLinePasteWarning": false, "terminal.integrated.rightClickBehavior": "copyPaste", "terminal.integrated.tabs.enabled": true, "terminal.integrated.tabs.showActions": "always", "terminal.integrated.smoothScrolling": true}