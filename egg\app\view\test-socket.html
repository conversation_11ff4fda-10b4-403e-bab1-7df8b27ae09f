<!DOCTYPE html>
<html>
<head>
    <title>Socket测试</title>
    <script src="/socket.io/socket.io.js"></script>
</head>
<body>
    <h1>Socket连接测试</h1>
    <div id="status">连接状态: 未连接</div>
    <div id="messages"></div>
    
    <script>
        const socket = io('/test');
        
        socket.on('connect', () => {
            document.getElementById('status').textContent = '连接状态: 已连接 - ' + socket.id;
            console.log('✅ 连接到test命名空间');
        });
        
        socket.on('disconnect', () => {
            document.getElementById('status').textContent = '连接状态: 已断开';
            console.log('❌ 断开连接');
        });
        
        socket.on('test', (data) => {
            console.log('📨 收到测试消息:', data);
            const messagesDiv = document.getElementById('messages');
            messagesDiv.innerHTML += '<p>' + JSON.stringify(data) + '</p>';
        });
        
        socket.on('error', (error) => {
            console.error('❌ Socket错误:', error);
            document.getElementById('status').textContent = '连接状态: 错误 - ' + error;
        });
    </script>
</body>
</html> 