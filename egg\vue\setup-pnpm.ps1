# PowerShell script to setup pnpm and prevent npm/yarn usage
Write-Host "Setting up pnpm configuration..." -ForegroundColor Green

# Check if pnpm is installed
if (!(Get-Command pnpm -ErrorAction SilentlyContinue)) {
    Write-Host "pnpm is not installed. Please install it first:" -ForegroundColor Red
    Write-Host "npm install -g pnpm" -ForegroundColor Yellow
    exit 1
}

# Install only-allow to prevent npm/yarn usage
Write-Host "Installing only-allow to prevent npm/yarn usage..." -ForegroundColor Blue
pnpm add -D only-allow

# Verify pnpm installation
Write-Host "Verifying pnpm setup..." -ForegroundColor Blue
pnpm --version

Write-Host "✅ pnpm setup completed!" -ForegroundColor Green
Write-Host "Now you can only use pnpm commands in this project." -ForegroundColor Green
Write-Host "Try running: pnpm install" -ForegroundColor Yellow
