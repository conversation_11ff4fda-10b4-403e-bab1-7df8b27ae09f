#!/usr/bin/env node

// 测试部署消息格式

// 模拟飞书服务的消息格式化功能
function formatDeploySuccessMessage(deployInfo = {}) {
  const {
    projectName = 'Vue项目',
    environment = 'production',
    version = 'latest',
    branch = 'main',
    commitHash = '',
    commitMessage = '',
    deployTime = new Date().toLocaleString('zh-CN', { timeZone: 'Asia/Shanghai' }),
    deployDuration = '',
    deployedBy = 'GitHub Actions',
    serverUrl = '',
    buildSize = '',
    features = [],
    notes = ''
  } = deployInfo;

  return `🎉 部署成功通知

🚀 **项目信息**
📦 项目名称: ${projectName}
🌍 部署环境: ${environment}
🏷️ 版本标签: ${version}
🌿 分支名称: ${branch}

💻 **代码信息**
🔗 提交哈希: ${commitHash ? commitHash.substring(0, 8) : 'N/A'}
📝 提交信息: ${commitMessage || '无提交信息'}

⏰ **部署详情**
🕐 部署时间: ${deployTime}
⏱️ 部署耗时: ${deployDuration || '未知'}
👤 部署人员: ${deployedBy}
${serverUrl ? `🌐 访问地址: ${serverUrl}` : ''}
${buildSize ? `📊 构建大小: ${buildSize}` : ''}

${features.length > 0 ? `✨ **本次更新**\n${features.map(f => `• ${f}`).join('\n')}\n` : ''}

${notes ? `📋 **备注说明**\n${notes}\n` : ''}

🎊 部署完成，服务已上线！`;
}

function formatDeployFailureMessage(deployInfo = {}) {
  const {
    projectName = 'Vue项目',
    environment = 'production',
    branch = 'main',
    commitHash = '',
    failureTime = new Date().toLocaleString('zh-CN', { timeZone: 'Asia/Shanghai' }),
    errorMessage = '未知错误',
    deployedBy = 'GitHub Actions',
    logUrl = '',
    retryCount = 0
  } = deployInfo;

  return `❌ 部署失败通知

🚨 **失败信息**
📦 项目名称: ${projectName}
🌍 部署环境: ${environment}
🌿 分支名称: ${branch}
🔗 提交哈希: ${commitHash ? commitHash.substring(0, 8) : 'N/A'}

⏰ **失败详情**
🕐 失败时间: ${failureTime}
👤 部署人员: ${deployedBy}
🔄 重试次数: ${retryCount}

💥 **错误信息**
${errorMessage}

${logUrl ? `📋 查看日志: ${logUrl}` : ''}

🔧 请检查错误信息并重新部署！`;
}

// 测试数据
const successTestData = {
  projectName: 'Vue前端项目',
  environment: 'production',
  version: 'v1.2.3',
  branch: 'main',
  commitHash: 'a1b2c3d4e5f6789012345678901234567890abcd',
  commitMessage: 'feat: 添加新功能和优化性能',
  deployTime: '2025-07-24 10:30:15',
  deployDuration: '2分钟30秒',
  deployedBy: 'GitHub Actions',
  serverUrl: 'https://your-domain.com',
  buildSize: '2.5MB',
  features: [
    '🎨 新增用户管理功能',
    '⚡ 优化页面加载速度',
    '🐛 修复已知bug',
    '📦 更新UI组件库',
    '🔒 增强安全性',
    '📱 响应式设计优化'
  ],
  notes: '本次部署包含重要功能更新，请及时测试验证'
};

const failureTestData = {
  projectName: 'Vue前端项目',
  environment: 'production',
  branch: 'main',
  commitHash: 'a1b2c3d4e5f6789012345678901234567890abcd',
  failureTime: '2025-07-24 10:30:15',
  errorMessage: 'Build failed: Module not found error in src/components/UserManagement.vue\n\nError details:\n- Missing dependency: @/utils/userHelper\n- Build process terminated with exit code 1',
  deployedBy: 'GitHub Actions',
  logUrl: 'https://github.com/your-org/your-repo/actions/runs/123456789',
  retryCount: 1
};

console.log('🎨 部署通知消息格式测试\n');

console.log('1️⃣ 部署成功通知格式:');
console.log('═'.repeat(60));
console.log(formatDeploySuccessMessage(successTestData));

console.log('\n\n2️⃣ 部署失败通知格式:');
console.log('═'.repeat(60));
console.log(formatDeployFailureMessage(failureTestData));

console.log('\n\n3️⃣ 最小参数测试:');
console.log('═'.repeat(60));
console.log('成功通知 (最小参数):');
console.log(formatDeploySuccessMessage({}));

console.log('\n失败通知 (最小参数):');
console.log(formatDeployFailureMessage({}));

console.log('\n\n🎯 消息特点:');
console.log('✅ 丰富的emoji表情');
console.log('✅ 结构化信息展示');
console.log('✅ 可选字段智能隐藏');
console.log('✅ 中文本地化时间');
console.log('✅ 清晰的视觉层次');

console.log('\n📱 在飞书中的显示效果:');
console.log('- 支持富文本格式 (优先)');
console.log('- 自动降级为普通文本');
console.log('- 保持良好的可读性');

console.log('\n🔧 集成方式:');
console.log('1. GitHub Actions webhook调用');
console.log('2. 直接调用服务方法');
console.log('3. API接口调用');

console.log('\n🎉 测试完成！消息格式验证通过！');
