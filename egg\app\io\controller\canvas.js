'use strict';

const Controller = require('egg').Controller;

/**
 * 🎨 画布Socket.IO控制器
 * 处理画布相关的Socket.IO事件
 */
class CanvasController extends Controller {
  
  /**
   * 连接事件处理
   */
  async connect() {
    const { ctx } = this;
    console.log('🎨 画布控制器: 客户端连接', ctx.socket.id);
    
    // 发送连接确认
    await ctx.socket.emit('connected', {
      message: '画布控制器连接成功',
      socketId: ctx.socket.id,
      timestamp: new Date().toISOString()
    });
  }

  /**
   * 断开连接事件处理
   */
  async disconnect() {
    const { ctx } = this;
    console.log('❌ 画布控制器: 客户端断开连接', ctx.socket.id);
  }

  /**
   * 加入房间
   */
  async joinRoom() {
    const { ctx } = this;
    const { roomId, userId, userInfo } = ctx.args[0] || {};
    
    if (!roomId) {
      await ctx.socket.emit('error', { message: '房间ID不能为空' });
      return;
    }
    
    console.log(`🏠 用户 ${userId} 通过控制器加入房间 ${roomId}`);
    
    // 加入Socket.IO房间
    await ctx.socket.join(roomId);
    
    // 发送加入成功确认
    await ctx.socket.emit('room_joined', {
      roomId: roomId,
      userId: userId,
      timestamp: Date.now()
    });
    
    // 通知房间内其他用户
    ctx.socket.to(roomId).emit('user_joined', {
      userId: userId,
      userInfo: userInfo,
      timestamp: Date.now()
    });
  }

  /**
   * 离开房间
   */
  async leaveRoom() {
    const { ctx } = this;
    const { roomId, userId } = ctx.args[0] || {};
    
    if (!roomId) {
      await ctx.socket.emit('error', { message: '房间ID不能为空' });
      return;
    }
    
    console.log(`🚪 用户 ${userId} 通过控制器离开房间 ${roomId}`);
    
    // 离开Socket.IO房间
    await ctx.socket.leave(roomId);
    
    // 通知房间内其他用户
    ctx.socket.to(roomId).emit('user_left', {
      userId: userId,
      timestamp: Date.now()
    });
    
    // 发送离开确认
    await ctx.socket.emit('room_left', {
      roomId: roomId,
      timestamp: Date.now()
    });
  }

  /**
   * 处理画布更新
   */
  async canvasUpdate() {
    const { ctx, app } = this;
    const updateData = ctx.args[0];
    
    if (!updateData || !updateData.roomId) {
      await ctx.socket.emit('error', { message: '无效的更新数据' });
      return;
    }
    
    try {
      const { roomId, strokes, userId } = updateData;
      
      // 验证数据格式
      if (!strokes || !Array.isArray(strokes)) {
        throw new Error('笔画数据格式无效');
      }
      
      console.log(`🎨 收到画布更新: 房间${roomId}, 用户${userId}, ${strokes.length}个笔画`);
      
      // 广播给房间内其他用户
      ctx.socket.to(roomId).emit('canvas_update', {
        strokes: strokes,
        fromUser: userId,
        timestamp: Date.now()
      });
      
      // 异步保存到数据库
      setImmediate(async () => {
        try {
          await this.saveCanvasData(roomId, strokes);
        } catch (error) {
          console.error('异步保存画布数据失败:', error);
        }
      });
      
      // 发送确认
      await ctx.socket.emit('update_confirmed', {
        timestamp: Date.now(),
        strokeCount: strokes.length
      });
      
    } catch (error) {
      console.error('处理画布更新失败:', error);
      await ctx.socket.emit('error', { 
        message: '处理画布更新失败', 
        error: error.message 
      });
    }
  }

  /**
   * 处理擦除操作
   */
  async canvasErase() {
    const { ctx } = this;
    const eraseData = ctx.args[0];
    
    if (!eraseData || !eraseData.roomId) {
      await ctx.socket.emit('error', { message: '无效的擦除数据' });
      return;
    }
    
    try {
      const { roomId, eraseArea, userId } = eraseData;
      
      console.log(`🗑️ 收到擦除操作: 房间${roomId}, 用户${userId}`);
      
      // 广播给房间内其他用户
      ctx.socket.to(roomId).emit('canvas_erase', {
        eraseArea: eraseArea,
        fromUser: userId,
        timestamp: Date.now()
      });
      
      // 发送确认
      await ctx.socket.emit('erase_confirmed', {
        timestamp: Date.now()
      });
      
    } catch (error) {
      console.error('处理擦除操作失败:', error);
      await ctx.socket.emit('error', { 
        message: '处理擦除操作失败', 
        error: error.message 
      });
    }
  }

  /**
   * 请求完整同步
   */
  async requestFullSync() {
    const { ctx } = this;
    const { roomId } = ctx.args[0] || {};
    
    if (!roomId) {
      await ctx.socket.emit('error', { message: '房间ID不能为空' });
      return;
    }
    
    try {
      console.log(`🔄 请求完整同步: 房间${roomId}`);
      
      // 从数据库加载画布数据
      const canvasData = await this.loadCanvasData(roomId);
      
      // 发送完整数据
      await ctx.socket.emit('full_sync', {
        canvasData: canvasData,
        timestamp: Date.now()
      });
      
    } catch (error) {
      console.error('完整同步失败:', error);
      await ctx.socket.emit('error', { 
        message: '完整同步失败', 
        error: error.message 
      });
    }
  }

  /**
   * 心跳检测
   */
  async ping() {
    const { ctx } = this;
    const pingData = ctx.args[0];
    
    await ctx.socket.emit('pong', {
      timestamp: Date.now(),
      serverTime: new Date().toISOString(),
      received: pingData
    });
  }

  /**
   * 获取房间状态
   */
  async getRoomStatus() {
    const { ctx } = this;
    const { roomId } = ctx.args[0] || {};
    
    if (!roomId) {
      await ctx.socket.emit('error', { message: '房间ID不能为空' });
      return;
    }
    
    try {
      // 获取房间内的Socket数量
      const room = ctx.app.io.of('/canvas').adapter.rooms.get(roomId);
      const userCount = room ? room.size : 0;
      
      await ctx.socket.emit('room_status', {
        roomId: roomId,
        activeUsers: userCount,
        timestamp: Date.now()
      });
      
    } catch (error) {
      console.error('获取房间状态失败:', error);
      await ctx.socket.emit('error', { 
        message: '获取房间状态失败', 
        error: error.message 
      });
    }
  }

  /**
   * 保存画布数据到数据库
   */
  async saveCanvasData(roomId, strokes) {
    const { app } = this;
    
    try {
      // 构建画布数据
      const canvasData = {
        version: 4,
        strokes: strokes,
        metadata: {
          lastModified: Date.now(),
          source: 'realtime'
        }
      };
      
      const compressedData = JSON.stringify(canvasData);
      
      // 更新数据库
      await app.mysql.update('fbsy', {
        canvas_data: compressedData,
        has_canvas: strokes.length > 0 ? 1 : 0,
        updated_at: new Date()
      }, {
        where: { id: roomId }
      });
      
      console.log(`💾 画布数据已保存: 房间${roomId}, ${strokes.length}个笔画`);
      
    } catch (error) {
      console.error('保存画布数据失败:', error);
      throw error;
    }
  }

  /**
   * 从数据库加载画布数据
   */
  async loadCanvasData(roomId) {
    const { app } = this;
    
    try {
      const result = await app.mysql.get('fbsy', { id: roomId });
      
      if (!result || !result.canvas_data) {
        return null;
      }
      
      const canvasData = JSON.parse(result.canvas_data);
      console.log(`📖 画布数据已加载: 房间${roomId}`);
      
      return canvasData;
      
    } catch (error) {
      console.error('加载画布数据失败:', error);
      throw error;
    }
  }
}

module.exports = CanvasController;
