/**
 * 显示IP API路由
 * 迁移自egg项目的home.js showip方法
 */

const { NextResponse } = require('next/server');

/**
 * 获取客户端真实IP地址
 */
function getClientIP(request) {
  // 检查各种可能的IP头
  const forwarded = request.headers.get('x-forwarded-for');
  const realIP = request.headers.get('x-real-ip');
  const cfConnectingIP = request.headers.get('cf-connecting-ip');
  
  if (forwarded) {
    // x-forwarded-for 可能包含多个IP，取第一个
    return forwarded.split(',')[0].trim();
  }
  
  if (realIP) {
    return realIP;
  }
  
  if (cfConnectingIP) {
    return cfConnectingIP;
  }
  
  // 如果都没有，返回默认值
  return '127.0.0.1';
}

async function GET(request) {
  try {
    const clientIP = getClientIP(request);
    
    console.log('📍 客户端IP请求:', clientIP);
    
    // 返回客户端IP地址，与原egg项目保持一致
    return new Response(clientIP, {
      status: 200,
      headers: {
        'Content-Type': 'text/plain; charset=utf-8',
      },
    });
  } catch (error) {
    console.error('❌ ShowIP API错误:', error.message);
    return NextResponse.json({ 
      error: error.message,
      timestamp: new Date().toISOString()
    }, { status: 500 });
  }
}

module.exports = { GET };
