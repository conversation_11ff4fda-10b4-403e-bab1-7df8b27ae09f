<!doctype html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width,initial-scale=1.0"/>
    <meta name="viewport"
          content="width=device-width, user-scalable=no, initial-scale=1.0, maximum-scale=1.0, minimum-scale=1.0">
    <meta http-equiv="X-UA-Compatible" content="ie=edge">
    <script src="https://egg.wcy9.com/js/jquery.js"></script>
    <!-- 最新版本的 Bootstrap 核心 CSS 文件 -->
    <link rel="stylesheet" href="https://egg.wcy9.com/public/bootstrap.min.css">

    <!-- 可选的 Bootstrap 主题文件（一般不用引入） -->
    <link rel="stylesheet" href="https://egg.wcy9.com/public/bootstrap-theme.min.css" >

    <!-- 最新的 Bootstrap 核心 JavaScript 文件 -->
    <script src="https://egg.wcy9.com/public/bootstrap.min.js" ></script>
    <title>Document</title>
</head>
<body>
<div class="con">
    <div class="lb"></div>
    <div class="wp" id="wp">

        {% for item in data %}

        {% if (item.materialContent) %}
        <div>{{item.materialContent|safe}}</div>
        {% endif %}
        <div>
            {{item.content|safe}}
        </div>
        <div>
            <br>
            <p class="an_a"
            style="color:red;"
            >{{item.answer|safe}}</p>
        </div>
        <div class="answer">
            {% if (showanswer) %}
            <div style="color:#b3e19d;width: 100%">======</div>
            {% if (onlyan === "") %}
            {{item.referenceAnalysis|safe}}
            {% endif %}
        </br>
            {% endif %}
        </div>
        <p style="color:#0000ff;width: 100%">======</p>
        {% endfor %}
    </div>
    <div class="rb"></div>
</div>
</body>
<script>

    let perValue = document.querySelector('.per').value;
    let pageValue = document.querySelector('.page').value;
    let moduleName = document.querySelector('.m').value;
    let firstKnowledgeName = document.querySelector('.f').value;
    let secondKnowledgeName = document.querySelector('.s').value;
    let showanswer = document.querySelector('.showanswer').value;


</script>
<style>
    body{
        font-size: 18px;
    }
    .con {
        display: flex;
        justify-content: space-between;
    }
    .lb, .rb {
        flex-basis: calc((100% - 960px) / 2);
    }

    .wp {
        width: 960px;
    }
    @media screen and (max-width: 600px) {
        .wp {
            width: 100%;
            box-sizing: border-box; /* Ensure padding and border are included in the width */
            padding: 0 10px; /* Optional: Add some padding to the sides */
        }
    }

    .answer {
        color: red;
    }

    .an {
        color: red;
    }
</style>

</html>
