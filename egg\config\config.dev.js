/* eslint valid-jsdoc: "off" */

'use strict';

/**
 * 开发环境专用配置 - 优化启动速度
 * @param {Egg.EggAppInfo} appInfo app info
 */

const path = require('path');

module.exports = (appInfo) => {
  const config = (exports = {});

  // 启动优化：开发环境配置
  config.development = {
    // 关闭不必要的功能以加快启动
    watchDirs: ['app/controller', 'app/service', 'config'], // 只监听核心目录
    ignoreDirs: ['app/public', 'app/view', 'logs', 'run', 'typings'], // 忽略静态资源
  };

  // 启动优化：简化中间件，添加性能监控
  config.middleware = ['robot', 'performance']; // 开发环境添加性能监控中间件

  // 性能监控中间件配置
  config.performance = {
    verbose: true, // 开发环境启用详细日志
    slowThreshold: 500, // 开发环境降低慢请求阈值到500ms
  };

  // 文件监听优化配置
  config.watcher = {
    directories: [
      'app/controller',
      'app/service',
      'app/middleware',
      'app/extend',
      'app/router.js',
      'config',
    ],
    ignore: [
      '**/node_modules/**',
      '**/*.log',
      '**/logs/**',
      '**/run/**',
      '**/typings/**',
      'app/public/**',
      'app/view/**',
      '**/.git/**',
      '**/coverage/**',
      '**/test/**',
      '**/*.d.ts', // 忽略TypeScript声明文件
      '**/*.map', // 忽略source map文件
    ],
    // chokidar特定配置 - 优化响应速度
    chokidar: {
      usePolling: false, // 使用原生事件
      interval: 50, // 减少轮询间隔
      binaryInterval: 150, // 减少二进制文件轮询间隔
      awaitWriteFinish: {
        stabilityThreshold: 50, // 减少文件稳定等待时间
        pollInterval: 25, // 减少轮询间隔
      },
    },
  };

  // 启动优化：禁用开发环境不需要的插件
  config.schedule = {
    disable: true, // 开发环境完全禁用定时任务
  };

  // 启动优化：使用统一配置模块
  const { getDevelopmentMySQLConfig } = require('./database');
  const { getDevelopmentRedisConfig } = require('./redis');

  // 数据库配置 - 开发环境优化
  exports.mysql = getDevelopmentMySQLConfig();

  // Redis配置 - 开发环境优化
  exports.redis = getDevelopmentRedisConfig();

  // 启动优化：日志配置
  exports.logger = {
    level: 'WARN', // 开发环境只显示警告和错误
    consoleLevel: 'WARN',
    disableConsoleAfterReady: false,
    dir: path.join(appInfo.root, 'logs'),
    // 减少日志文件数量
    coreLogger: {
      level: 'WARN',
    },
    errorLogger: {
      level: 'ERROR',
    },
  };

  // 启动优化：CORS简化配置
  exports.cors = {
    origin: '*',
    allowMethods: 'GET,HEAD,PUT,POST,DELETE,PATCH',
  };

  // 启动优化：Socket.IO简化配置
  config.io = {
    init: {
      cors: {
        origin: '*',
        methods: ['GET', 'POST'],
      },
    },
    // 开发环境namespace配置
    namespace: {
      '/test': {
        connectionMiddleware: [],
        packetMiddleware: [],
      },
      '/canvas': {
        connectionMiddleware: [], // 🎨 画布实时传输命名空间
        packetMiddleware: [],
      },
    },
  };

  // 启动优化：视图引擎配置
  config.view = {
    defaultViewEngine: 'nunjucks',
    mapping: {
      html: 'nunjucks',
    },
    cache: false, // 开发环境禁用缓存
  };

  // 启动优化：安全配置简化
  config.security = {
    csrf: {
      enable: false,
    },
    domainWhiteList: ['*'], // 开发环境允许所有域名
  };

  return {
    ...config,
  };
};
