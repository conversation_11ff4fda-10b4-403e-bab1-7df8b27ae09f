#!/bin/bash

# 强制重启脚本 - 确保所有进程都被杀死
echo "🔥 强制重启应用..."

# 1. 停止PM2应用
echo "🛑 停止PM2应用..."
pm2 stop egg 2>/dev/null || true
pm2 delete egg 2>/dev/null || true

# 2. 杀死所有相关Node进程
echo "💀 杀死残留进程..."
pkill -f "egg-server-example" 2>/dev/null || true
pkill -f "egg-cluster" 2>/dev/null || true
pkill -f "egg-scripts" 2>/dev/null || true

# 等待进程完全退出
sleep 3

# 3. 检查端口是否释放
echo "🔍 检查端口状态..."
if netstat -tlnp | grep :7001 > /dev/null; then
    echo "⚠️ 端口7001仍被占用，强制释放..."
    fuser -k 7001/tcp 2>/dev/null || true
    sleep 2
fi

# 4. 确认没有残留进程
REMAINING=$(ps aux | grep -E "(egg-server|egg-cluster|egg-scripts)" | grep -v grep | wc -l)
if [ $REMAINING -gt 0 ]; then
    echo "⚠️ 仍有 $REMAINING 个残留进程，强制杀死..."
    ps aux | grep -E "(egg-server|egg-cluster|egg-scripts)" | grep -v grep | awk '{print $2}' | xargs kill -9 2>/dev/null || true
    sleep 2
fi

# 5. 重新启动
echo "🚀 重新启动应用..."
cd /home/<USER>
pm2 start npm --name "egg" -- start

# 6. 等待启动
echo "⏳ 等待应用启动..."
sleep 5

# 7. 检查状态
echo "📊 检查应用状态..."
pm2 status

# 8. 测试连接
echo "🧪 测试应用连接..."
if curl -f http://localhost:7001/health >/dev/null 2>&1; then
    echo "✅ 应用启动成功！"
else
    echo "❌ 应用启动失败，请检查日志"
    pm2 logs egg --lines 10
fi

echo "🎉 强制重启完成！"
