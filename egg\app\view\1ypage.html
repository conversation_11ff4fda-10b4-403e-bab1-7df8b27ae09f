<html>
<head>
    <meta charset="UTF-8"/>
    <meta name="viewport" content="width=device-width,initial-scale=1.0"/>
    <script src="/public/vue/vue.min.js"></script>
    <script src="/public/element/index.js"></script>
    <link rel="stylesheet" href="/public/element/index.css">
    <script src="/public/js/axios.min.js"></script>

    <title>Element Plus demo</title>
</head>
<body>
<div>
</div>
<div id="app">
    <div>
        <p v-for="item in imgs">
            <img :src="item">
        </p>
    </div>
</div>

<script>
    new Vue({
        el: '#app',
        data: function () {
            return {
                tableData: null,
                imgs: []
            }
        },
        created() {
        },
        mounted() {
            this.getData();
        },
        methods: {
            getData() {
                let url = "{{url}}";
                axios.get('/1ypageinfo?url=' + url).then(res => {
                    this.tableData = res.data;
                    let imgs1 = res.data;
                    for (item of imgs1) {
                        axios.get('/1yinfo?url=' + item).then(
                            res => {
                                for (i of res.data) {
                                    this.imgs.push(i)
                                }
                            }
                        )
                    }
                    this.tableData = this.imgs;
                })
            }
        }
    })


</script>

<style>
    img {
        width: auto;
        height: auto;
        max-width: 100%;
        max-height: 100%;
    }
</style>
</body>
</html>
