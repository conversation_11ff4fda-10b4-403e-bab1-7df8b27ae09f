<html>
<head>
    <meta charset="UTF-8"/>
    <meta name="viewport" content="width=device-width,initial-scale=1.0"/>
    <script src="/public/vue/vue.min.js"></script>
    <script src="/public/element/index.js"></script>
    <link rel="stylesheet" href="/public/element/index.css">
    <script src="/public/js/axios.min.js"></script>
    <title>Element Plus demo</title>
</head>
<body>
<div>
    {{jzsj}}
</div>
<div id="app">
    <el-alert
            title="{{time}}"
            type="success"
            center>
    </el-alert>
    <el-table
            :data="tableData"
            style="width: 100%"
            :row-class-name="tableRowClassName"
            height="100%"
            border
            :default-sort="{prop: 'total', order: 'descending'}"
    >
        <el-table-column
                prop="epUnitName"
                label="单位"
                sortable
                fixed
                width="180">
        </el-table-column>
        <el-table-column
                prop="epUnitCode"
                label="岗位代码"
                sortable
                width="180">
        </el-table-column>
        <el-table-column
                label="招聘岗位"
                width="180">
            <template #default="scope">
                <div style="display: flex; align-items: center">
                    <el-link v-text="scope.row.epPostName" :href="scope.row.link" target="_blank" type="primary"></el-link>
                </div>
            </template>
        </el-table-column>
        <el-table-column
                prop="epRequiredSpecialty"
                label="要求专业"
                sortable
                width="180">
        </el-table-column>
        <el-table-column
                prop="epHousehold"
                label="户籍"
                sortable
                width="180">
        </el-table-column>
        <el-table-column
                prop="examineCount"
                label="待审核人数"
                sortable
                width="180">
        </el-table-column>
        <el-table-column
                prop="passCount"
                label="	审核通过人数"
                sortable
                width="180"
        >
        </el-table-column>
        <el-table-column
                prop="notPassCount"
                label="审核不通过人数"
                sortable
                width="180">
        </el-table-column>
        <el-table-column
                prop="sjrs"
                label="实际总人数"
                sortable
                width="180">
        </el-table-column>
        <el-table-column
                prop="allCount"
                label="总人数"
                sortable
                width="180">
        </el-table-column>
        <el-table-column
                prop="epRemarks"
                label="备注"
                width="180">
        </el-table-column>
    </el-table>
</div>

<script>
    new Vue({
        el: '#app',
        data: function () {
            return {
                tableData: null,
                link: "wcnm"
            }
        },
        created() {

        },
        mounted() {   //自动触发写入的函数
            this.getData();
        },
        methods: {
            formatter(row, column) {
                return (row.dshrs + row.shtgyjf) / 1;
                //:formatter="formatter"
            },
            formatterdate(row, column) {
                if (row.uptime) {
                    let DateString = row.uptime;
                    let date = new Date(DateString);
                    let year = date.getFullYear();
                    let month = date.getMonth() + 1;
                    let day = date.getDate();
                    let Hours = date.getHours();
                    let Minutes = date.getMinutes();
                    let Seconds = date.getSeconds();
                    if (month < 10) {
                        month = '0' + month;
                    }
                    if (day < 10) {
                        day = '0' + day;
                    }
                    let s_createtime = year + '-' + month + '-' + day + ' ' + Hours + ':' + Minutes + ':' + Seconds;
                    return s_createtime;
                }
            },
            tableRowClassName({row, rowIndex}) {
                if (row.epId === 2779) {
                    return 'success-row';
                } else if (row.epId === 2772
                ) {
                    return 'warning-row';
                }
                return '';
            },
            getData() {
                axios.get('/zzsydw').then(res => {
                    this.tableData = res.data;
                })
            }
        }
    })


</script>

<style>
    .el-table .warning-row {
        background: oldlace;
    }

    .el-table .success-row {
        background: #f0f9eb;
    }
</style>
</body>
</html>
