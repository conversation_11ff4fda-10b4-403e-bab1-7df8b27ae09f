const Service = require('egg').Service;
const axios = require('axios');
const crypto = require('crypto');
const FormData = require('form-data');
const fs = require('fs');
const { Buffer } = require('buffer');
const { log } = require('console');
const path = require('path');

class WecomService extends Service {
  async qywx(text) {
    const url =
      'https://qyapi.weixin.qq.com/cgi-bin/webhook/send?key=63d15b88-1d4e-40b3-bb16-8d891f5ffc7f';
    const data = {
      msgtype: 'text',
      text: {
        content: text,
      },
    };
    try {
      const response = await axios.post(url, data);
      return response.data;
    } catch (error) {
      console.error(error);
      throw error;
    }
  }

  async zyjqywx(text) {
    const url =
      'https://qyapi.weixin.qq.com/cgi-bin/webhook/send?key=83055a0c-5d94-4a97-86b7-0c5f53b93597';
    const data = {
      msgtype: 'text',
      text: {
        content: text,
      },
    };
    try {
      const response = await axios.post(url, data);
      return response.data;
    } catch (error) {
      console.error(error);
      throw error;
    }
  }

  async qywximg(imagePath) {
    const url =
      'https://qyapi.weixin.qq.com/cgi-bin/webhook/send?key=63d15b88-1d4e-40b3-bb16-8d891f5ffc7f';
    const imageData = fs.readFileSync(imagePath);
    const imageBase64 = Buffer.from(imageData).toString('base64');
    const md5sum = crypto.createHash('md5');
    md5sum.update(imageData);
    const imageMD5 = md5sum.digest('hex');
    const headers = { 'Content-Type': 'application/json' };
    const requestData = {
      msgtype: 'image',
      image: {
        base64: imageBase64,
        md5: imageMD5,
      },
    };
    const response = await axios.post(url, requestData, { headers });
    return response.data;
  }
}

module.exports = WecomService;
