<!doctype html>
<html lang="en">
<head>
  <meta charset="UTF-8">
  <meta name="viewport" content="width=device-width,initial-scale=1.0" />
  <meta name="viewport"
        content="width=device-width, user-scalable=no, initial-scale=1.0, maximum-scale=1.0, minimum-scale=1.0">
  <meta http-equiv="X-UA-Compatible" content="ie=edge">
  <script src="https://egg.wcy9.com/public/js/jquery.js"></script>
  <!-- 最新版本的 Bootstrap 核心 CSS 文件 -->
  <link rel="stylesheet" href="https://egg.wcy9.com/public/bootstrap.min.css">

  <!-- 可选的 Bootstrap 主题文件（一般不用引入） -->
  <link rel="stylesheet" href="https://egg.wcy9.com/public/bootstrap-theme.min.css">

  <!-- 最新的 Bootstrap 核心 JavaScript 文件 -->
  <script src="https://egg.wcy9.com/public/bootstrap.min.js"></script>
  <title>Document</title>
</head>
<body>
<div class="con">
  <div class="lb"></div>
  <div class="wp" id="wp">
    {% if (per === 6) %}
    <button class="prev" onclick="prev()">上一页</button>
    <input type="hidden" value="{[per]}" class="per">
    <input type="hidden" value="{[page]}" class="page">
    <input type="hidden" value="{[moduleName]}" class="m">
    <input type="hidden" value="{[firstKnowledgeName]}" class="f">
    <input type="hidden" value="{[secondKnowledgeName]}" class="s">
    <input type="hidden" value="{[showanswer]}" class="showanswer">
    <button class="next" onclick="next()" style="float: right">下一页</button>
    <button class="showan" style="float: right">显示答案</button>
    {% endif %}
    {% for item in data %}

    {% if (item.materialContent) %}
    <div>{[item.materialContent|safe]}</div>
    {% endif %}
    <div>
      {[item.content|safe]}
    </div>
    <div>
      <p class="an_a"
         style="color:red;"
      >{[item.zhengque|safe]}</p>
    </div>

    <p style="color:#0000ff;width: 100%">======</p>

    {% endfor %}
  </div>
  <div class="rb"></div>
</div>
</body>
<script>

  let perValue = document.querySelector(".per").value;
  let pageValue = document.querySelector(".page").value;
  let moduleName = document.querySelector(".m").value;
  let firstKnowledgeName = document.querySelector(".f").value;
  let secondKnowledgeName = document.querySelector(".s").value;
  let showanswer = document.querySelector(".showanswer").value;

  function prev() {
    pageValue = parseInt(pageValue) - 1;
    if (moduleName) {
      window.location.href = "/mintimu?m=" + moduleName + "&per=" + perValue + "&page=" + pageValue + "&a=" + showanswer + "&z=1";
    }
    if (firstKnowledgeName) {
      window.location.href = "/mintimu?f=" + firstKnowledgeName + "&per=" + perValue + "&page=" + pageValue + "&a=" + showanswer + "&z=1";
    }
    if (secondKnowledgeName) {
      window.location.href = "/mintimu?s=" + secondKnowledgeName + "&per=" + perValue + "&page=" + pageValue + "&a=" + showanswer + "&z=1";
    }
  }

  function next() {
    pageValue = parseInt(pageValue) + 1;
    if (moduleName) {
      window.location.href = "/mintimu?m=" + moduleName + "&per=" + perValue + "&page=" + pageValue + "&a=" + showanswer + "&z=1";
    }
    if (firstKnowledgeName) {
      window.location.href = "/mintimu?f=" + firstKnowledgeName + "&per=" + perValue + "&page=" + pageValue + "&a=" + showanswer + "&z=1";
    }
    if (secondKnowledgeName) {
      window.location.href = "/mintimu?s=" + secondKnowledgeName + "&per=" + perValue + "&page=" + pageValue + "&a=" + showanswer + "&z=1";
    }
  }

  // 获取需要操作的元素
  let prevBtn = document.querySelector(".prev");
  let nextBtn = document.querySelector(".next");
  let answerDiv = document.querySelector(".an");
  let showBtn = document.querySelector(".showan");
  // 绑定键盘按键事件处理程序
  document.addEventListener("keydown", function(event) {
    if (
      event.key === "ArrowLeft" ||
      event.key === "z" ||
      event.key === "q") { // 左方向键
      prev();
    } else if (
      event.key === "ArrowRight" ||
      event.key === "c" ||
      event.key === "e") { // 右方向键
      next();
    }
  });

  $(".lb").click(function() {
    prev();
  });
  $(".rb").click(function() {
    next();
  });
  // 绑定点击事件处理程序
  showBtn.addEventListener("click", toggleAnswer);
  // 绑定键盘按键事件处理程序
  document.addEventListener("keydown", function(event) {
    if (event.key === " " ||
      event.key === "Spacebar" ||
      event.key === "w" ||
      event.key === "x" ||
      event.key === "ArrowUp" ||// 空格键
      event.key === "ArrowDown") { // 空格键
      toggleAnswer();
    }
  });

  // 定义 toggleAnswer 函数
  function toggleAnswer() {
    if (answerDiv.style.display === "none") { // 答案元素当前为隐藏状态
      answerDiv.style.display = "block"; // 显示答案元素
      showBtn.textContent = "隐藏答案"; // 修改按钮文本
    } else { // 答案元素当前为显示状态
      answerDiv.style.display = "none"; // 隐藏答案元素
      showBtn.textContent = "显示答案"; // 修改按钮文本
    }
  }

  $("p").click(function() {
    toggleAnswer();
  });
</script>
<style>
    body {
        font-size: 18px;
    }

    .con {
        display: flex;
        justify-content: space-between;
    }

    .lb, .rb {
        flex-basis: calc((100% - 960px) / 2);
    }

    .wp {
        width: 960px;
    }

    @media screen and (max-width: 600px) {
        .wp {
            width: 100%;
            box-sizing: border-box; /* Ensure padding and border are included in the width */
            padding: 0 10px; /* Optional: Add some padding to the sides */
        }
    }

    .answer {
        color: red;
    }

    .an {
        color: red;
    }
</style>

</html>
