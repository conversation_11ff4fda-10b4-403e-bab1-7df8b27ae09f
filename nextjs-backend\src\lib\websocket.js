/**
 * WebSocket服务器
 * 提供类似egg项目的socket.io多namespace支持
 */

const { WebSocketServer } = require('ws');
const { parse } = require('url');
const { getConfig } = require('./config');

// WebSocket服务器实例和连接管理
let wsServer = null;
const namespaces = new Map(); // namespace -> Set<connections>
const connections = new Map(); // connection -> { namespace, id, metadata }

/**
 * 初始化WebSocket服务器
 */
function initWebSocketServer(httpServer) {
  if (wsServer) {
    return wsServer;
  }

  const config = getConfig('websocket');
  
  // 创建WebSocket服务器
  wsServer = new WebSocketServer({ 
    server: httpServer,
    path: '/socket.io/', // 兼容socket.io路径
  });

  // 初始化命名空间
  config.namespaces.forEach(namespace => {
    namespaces.set(namespace, new Set());
    console.log(`✅ 初始化WebSocket命名空间: ${namespace}`);
  });

  // 处理连接
  wsServer.on('connection', (ws, request) => {
    handleConnection(ws, request);
  });

  console.log('✅ WebSocket服务器初始化成功');
  return wsServer;
}

/**
 * 处理WebSocket连接
 */
function handleConnection(ws, request) {
  const { pathname, query } = parse(request.url, true);
  
  // 解析命名空间（从路径中提取）
  let namespace = '/';
  if (pathname && pathname !== '/socket.io/') {
    namespace = pathname.replace('/socket.io', '') || '/';
  }
  
  // 如果URL中有namespace参数，优先使用
  if (query.namespace) {
    namespace = query.namespace;
  }

  // 检查命名空间是否存在
  if (!namespaces.has(namespace)) {
    console.log(`❌ 未知的命名空间: ${namespace}`);
    ws.close(1008, `Unknown namespace: ${namespace}`);
    return;
  }

  // 生成连接ID
  const connectionId = generateConnectionId();
  
  // 存储连接信息
  const connectionInfo = {
    namespace,
    id: connectionId,
    clientId: query.clientId || 'anonymous',
    connectedAt: new Date().toISOString(),
    lastActivity: Date.now()
  };
  
  connections.set(ws, connectionInfo);
  namespaces.get(namespace).add(ws);

  console.log(`✅ 客户端连接到命名空间 ${namespace}:`, connectionId);

  // 发送连接成功消息
  sendMessage(ws, 'connected', {
    message: `已连接到${namespace}命名空间`,
    socketId: connectionId,
    namespace,
    timestamp: new Date().toISOString()
  });

  // 处理消息
  ws.on('message', (data) => {
    handleMessage(ws, data);
  });

  // 处理断开连接
  ws.on('close', () => {
    handleDisconnection(ws);
  });

  // 处理错误
  ws.on('error', (error) => {
    console.error(`❌ WebSocket错误 (${connectionInfo.namespace}):`, error.message);
  });

  // 心跳检测
  ws.isAlive = true;
  ws.on('pong', () => {
    ws.isAlive = true;
    connectionInfo.lastActivity = Date.now();
  });
}

/**
 * 处理WebSocket消息
 */
function handleMessage(ws, data) {
  const connectionInfo = connections.get(ws);
  if (!connectionInfo) return;

  try {
    const message = JSON.parse(data.toString());
    const { event, data: messageData } = message;

    console.log(`📨 收到消息 (${connectionInfo.namespace}):`, event);

    // 更新活动时间
    connectionInfo.lastActivity = Date.now();

    // 根据命名空间和事件类型处理消息
    switch (connectionInfo.namespace) {
      case '/thinkprocess':
        handleThinkprocessMessage(ws, event, messageData);
        break;
      case '/button':
        handleButtonMessage(ws, event, messageData);
        break;
      case '/test':
        handleTestMessage(ws, event, messageData);
        break;
      case '/canvas':
        handleCanvasMessage(ws, event, messageData);
        break;
      default:
        // 默认处理：回显消息
        sendMessage(ws, 'echo', { event, data: messageData });
    }
  } catch (error) {
    console.error(`❌ 消息解析错误 (${connectionInfo.namespace}):`, error.message);
    sendMessage(ws, 'error', { message: '消息格式错误' });
  }
}

/**
 * 处理thinkprocess命名空间消息
 */
function handleThinkprocessMessage(ws, event, data) {
  const connectionInfo = connections.get(ws);
  
  switch (event) {
    case 'ping':
      console.log('收到ping消息:', data);
      sendMessage(ws, 'pong', `收到ping: ${data}`);
      break;
      
    case 'restart-mock':
      console.log('🔄 收到重新开始模拟请求');
      handleRestartMock(ws, data);
      break;
      
    case 'clear-mock-data':
      console.log('🗑️ 收到清空所有模拟数据请求');
      handleClearMockData(ws);
      break;
      
    default:
      sendMessage(ws, 'unknown-event', { event, message: '未知事件类型' });
  }
}

/**
 * 处理button命名空间消息
 */
function handleButtonMessage(ws, event, data) {
  switch (event) {
    case 'sendMsg':
      const key = data?.key;
      console.log('received key:', key);
      sendMessage(ws, 'key', key);
      break;
      
    default:
      sendMessage(ws, 'button-response', { event, data });
  }
}

/**
 * 处理test命名空间消息
 */
function handleTestMessage(ws, event, data) {
  sendMessage(ws, 'test-response', {
    event,
    data,
    timestamp: new Date().toISOString()
  });
}

/**
 * 处理canvas命名空间消息
 */
function handleCanvasMessage(ws, event, data) {
  // 广播画布数据到同一命名空间的其他客户端
  broadcastToNamespace('/canvas', 'canvas-update', data, ws);
}

/**
 * 处理重新开始模拟
 */
async function handleRestartMock(ws, data) {
  const { app } = require('./app');
  
  // 模拟思考数据
  const mockThinkingData = "🧠 嗯，用户这次又发来了一道行测类比推理题，要求我用小学生都能理解的生动方式讲解秒杀技巧。题目是'双肩包：斜挎包：手提包'找同类选项，正确答案是C。\n\n这道题的关键在于发现题干三个词都是按使用方式分类的背包类型。题干破绽很明显：其他选项要么分类标准混乱（如A项按材质和功能混搭），要么包含错误类别（如B项的金星木星根本不是恒星）。C项三个工程机械都是按功能分类，完美匹配。";
  
  const mockAnswerData = "🎉 解题完成！最终答案：C选项（类比推理题）";
  
  // 发送思考数据
  console.log('🧠 发送模拟思考数据（一次性）');
  sendMessage(ws, 'thinking', {
    content: mockThinkingData,
    timestamp: new Date().toISOString()
  });
  
  // 保存到Redis
  if (app.redis) {
    await app.redis.set('thinking_process', JSON.stringify({
      content: mockThinkingData,
      time: Date.now()
    }));
  }
  
  // 3秒后发送答案
  setTimeout(async () => {
    console.log('💭 发送模拟答案数据（一次性）');
    sendMessage(ws, 'answer', {
      content: mockAnswerData,
      timestamp: new Date().toISOString()
    });
    
    // 保存答案到Redis
    if (app.redis) {
      await app.redis.set('thinking_answer', JSON.stringify({
        content: mockAnswerData,
        time: Date.now()
      }));
    }
  }, 3000);
}

/**
 * 处理清空模拟数据
 */
async function handleClearMockData(ws) {
  const { app } = require('./app');
  
  // 清空Redis缓存
  if (app.redis) {
    await app.redis.del(['thinking_process', 'thinking_answer']);
  }
  
  console.log('✅ 已清空所有模拟数据');
  sendMessage(ws, 'clear-complete', { message: '模拟数据已清空' });
}

/**
 * 处理断开连接
 */
function handleDisconnection(ws) {
  const connectionInfo = connections.get(ws);
  if (!connectionInfo) return;

  console.log(`❌ 客户端断开连接 (${connectionInfo.namespace}):`, connectionInfo.id);

  // 从命名空间中移除连接
  const namespaceConnections = namespaces.get(connectionInfo.namespace);
  if (namespaceConnections) {
    namespaceConnections.delete(ws);
  }

  // 移除连接信息
  connections.delete(ws);
}

/**
 * 发送消息到指定连接
 */
function sendMessage(ws, event, data) {
  if (ws.readyState === ws.OPEN) {
    const message = JSON.stringify({ event, data });
    ws.send(message);
  }
}

/**
 * 广播消息到指定命名空间
 */
function broadcastToNamespace(namespace, event, data, excludeWs = null) {
  const namespaceConnections = namespaces.get(namespace);
  if (!namespaceConnections) return;

  const message = JSON.stringify({ event, data });
  
  namespaceConnections.forEach(ws => {
    if (ws !== excludeWs && ws.readyState === ws.OPEN) {
      ws.send(message);
    }
  });
}

/**
 * 发送消息到所有连接
 */
function broadcastToAll(event, data, excludeNamespace = null) {
  namespaces.forEach((connections, namespace) => {
    if (namespace !== excludeNamespace) {
      broadcastToNamespace(namespace, event, data);
    }
  });
}

/**
 * 生成连接ID
 */
function generateConnectionId() {
  return 'ws_' + Math.random().toString(36).substr(2, 9) + '_' + Date.now();
}

/**
 * 心跳检测
 */
function startHeartbeat() {
  const interval = setInterval(() => {
    wsServer.clients.forEach(ws => {
      if (!ws.isAlive) {
        console.log('💔 心跳检测失败，断开连接');
        return ws.terminate();
      }
      
      ws.isAlive = false;
      ws.ping();
    });
  }, 30000); // 30秒心跳检测

  return interval;
}

/**
 * 获取连接统计信息
 */
function getStats() {
  const stats = {
    totalConnections: connections.size,
    namespaces: {}
  };

  namespaces.forEach((connections, namespace) => {
    stats.namespaces[namespace] = connections.size;
  });

  return stats;
}

/**
 * 健康检查
 */
function healthCheck() {
  return {
    status: wsServer ? 'healthy' : 'unhealthy',
    stats: getStats(),
    timestamp: new Date().toISOString()
  };
}

/**
 * 关闭WebSocket服务器
 */
function closeWebSocketServer() {
  if (wsServer) {
    wsServer.close();
    wsServer = null;
    namespaces.clear();
    connections.clear();
    console.log('✅ WebSocket服务器已关闭');
  }
}

module.exports = {
  initWebSocketServer,
  broadcastToNamespace,
  broadcastToAll,
  sendMessage,
  getStats,
  healthCheck,
  closeWebSocketServer
};
