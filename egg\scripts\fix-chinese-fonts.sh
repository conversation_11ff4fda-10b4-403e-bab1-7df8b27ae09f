#!/bin/bash

# 修复 wkhtmltoimage 中文字体显示问题
# 适用于 Debian/Ubuntu 系统

echo "🔤 开始修复中文字体显示问题..."

# 检查是否为root用户
if [ "$EUID" -ne 0 ]; then
    echo "❌ 请使用root权限运行此脚本"
    echo "使用: sudo bash $0"
    exit 1
fi

# 更新包管理器
echo "📦 更新包管理器..."
apt-get update

# 安装中文字体包
echo "🔤 安装中文字体包..."
apt-get install -y \
    fonts-dejavu \
    fonts-liberation \
    fonts-noto-cjk \
    fonts-noto-cjk-extra \
    fonts-wqy-zenhei \
    fonts-wqy-microhei \
    fonts-arphic-ukai \
    fonts-arphic-uming \
    ttf-wqy-zenhei \
    ttf-wqy-microhei

# 安装额外的中文字体
echo "📝 安装额外中文字体..."
apt-get install -y \
    fonts-droid-fallback \
    fonts-noto-color-emoji \
    fonts-opensymbol

# 创建字体目录
echo "📁 创建字体目录..."
mkdir -p /usr/share/fonts/truetype/chinese
mkdir -p /home/<USER>/fonts

# 下载并安装常用中文字体
echo "⬇️ 下载常用中文字体..."

# 创建临时目录
TEMP_DIR="/tmp/chinese-fonts"
mkdir -p "$TEMP_DIR"
cd "$TEMP_DIR"

# 下载思源黑体（如果网络允许）
echo "正在尝试下载思源黑体..."
if wget -q --timeout=10 "https://github.com/adobe-fonts/source-han-sans/releases/download/2.004R/SourceHanSansCN.zip" -O SourceHanSansCN.zip 2>/dev/null; then
    echo "✅ 思源黑体下载成功"
    unzip -q SourceHanSansCN.zip
    find . -name "*.otf" -exec cp {} /usr/share/fonts/truetype/chinese/ \;
else
    echo "⚠️ 思源黑体下载失败，跳过"
fi

# 清理临时文件
cd /
rm -rf "$TEMP_DIR"

# 更新字体缓存
echo "🔄 更新字体缓存..."
fc-cache -fv

# 检查字体安装情况
echo "🔍 检查中文字体安装情况..."
echo "可用的中文字体:"
fc-list | grep -i -E "(chinese|zh|cjk|wqy|noto|source)" | head -10

# 创建测试HTML文件
echo "📝 创建中文字体测试文件..."
cat > /tmp/chinese-test.html << 'EOF'
<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>中文字体测试</title>
    <style>
        body {
            font-family: "Noto Sans CJK SC", "WenQuanYi Zen Hei", "WenQuanYi Micro Hei", "Source Han Sans CN", "Microsoft YaHei", "SimHei", Arial, sans-serif;
            line-height: 1.6;
            margin: 20px;
            background: white;
            color: black;
        }
        .test-section {
            margin: 20px 0;
            padding: 15px;
            border: 1px solid #ccc;
            border-radius: 5px;
        }
        h1 { color: #333; }
        h2 { color: #666; }
        .font-test {
            font-size: 16px;
            margin: 10px 0;
        }
    </style>
</head>
<body>
    <h1>中文字体显示测试</h1>
    
    <div class="test-section">
        <h2>基础中文测试</h2>
        <p class="font-test">这是一个中文字体测试页面。</p>
        <p class="font-test">测试内容包括：简体中文、繁體中文。</p>
        <p class="font-test">数字测试：1234567890</p>
        <p class="font-test">英文测试：Hello World! ABCDEFGHIJKLMNOPQRSTUVWXYZ</p>
    </div>
    
    <div class="test-section">
        <h2>题目示例测试</h2>
        <p class="font-test"><strong>题目：</strong>下列哪个选项是正确的？</p>
        <p class="font-test">A. 选项一</p>
        <p class="font-test">B. 选项二</p>
        <p class="font-test">C. 选项三</p>
        <p class="font-test">D. 选项四</p>
        <p class="font-test"><strong>解析：</strong>这是题目的详细解析内容。</p>
    </div>
    
    <div class="test-section">
        <h2>特殊字符测试</h2>
        <p class="font-test">标点符号：，。！？；：""''（）【】</p>
        <p class="font-test">数学符号：+ - × ÷ = ≠ > < ≥ ≤</p>
        <p class="font-test">其他符号：@ # $ % & * ^ ~ ` | \ / _</p>
    </div>
</body>
</html>
EOF

# 测试字体渲染
echo "🧪 测试中文字体渲染..."
wkhtmltoimage \
    --width 800 \
    --height 600 \
    --format png \
    --quality 90 \
    --disable-javascript \
    --quiet \
    /tmp/chinese-test.html \
    /tmp/chinese-test.png

if [ -f /tmp/chinese-test.png ]; then
    echo "✅ 中文字体测试图片生成成功: /tmp/chinese-test.png"
    echo "图片大小: $(du -h /tmp/chinese-test.png | cut -f1)"
    
    # 检查图片是否包含中文内容（简单检查文件大小）
    SIZE=$(stat -c%s /tmp/chinese-test.png)
    if [ $SIZE -gt 10000 ]; then
        echo "✅ 图片大小正常，可能包含中文内容"
    else
        echo "⚠️ 图片大小较小，可能仍有字体问题"
    fi
else
    echo "❌ 中文字体测试失败！"
fi

# 创建字体配置文件
echo "⚙️ 创建字体配置文件..."
cat > /etc/fonts/local.conf << 'EOF'
<?xml version="1.0"?>
<!DOCTYPE fontconfig SYSTEM "fonts.dtd">
<fontconfig>
    <!-- 中文字体优先级配置 -->
    <alias>
        <family>sans-serif</family>
        <prefer>
            <family>Noto Sans CJK SC</family>
            <family>WenQuanYi Zen Hei</family>
            <family>WenQuanYi Micro Hei</family>
            <family>Source Han Sans CN</family>
            <family>DejaVu Sans</family>
            <family>Liberation Sans</family>
        </prefer>
    </alias>
    
    <alias>
        <family>serif</family>
        <prefer>
            <family>Noto Serif CJK SC</family>
            <family>AR PL UMing CN</family>
            <family>AR PL UKai CN</family>
            <family>DejaVu Serif</family>
            <family>Liberation Serif</family>
        </prefer>
    </alias>
    
    <alias>
        <family>monospace</family>
        <prefer>
            <family>Noto Sans Mono CJK SC</family>
            <family>WenQuanYi Zen Hei Mono</family>
            <family>DejaVu Sans Mono</family>
            <family>Liberation Mono</family>
        </prefer>
    </alias>
</fontconfig>
EOF

# 再次更新字体缓存
echo "🔄 再次更新字体缓存..."
fc-cache -fv

# 验证字体配置
echo "🔍 验证字体配置..."
echo "默认sans-serif字体:"
fc-match sans-serif

echo "默认中文字体:"
fc-match "sans-serif:lang=zh-cn"

# 创建优化的测试脚本
echo "📝 创建字体测试脚本..."
cat > /usr/local/bin/test-chinese-fonts << 'EOF'
#!/bin/bash

echo "🧪 测试中文字体渲染..."

# 创建简单测试HTML
cat > /tmp/simple-test.html << 'TESTEOF'
<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <style>
        body {
            font-family: "Noto Sans CJK SC", "WenQuanYi Zen Hei", "WenQuanYi Micro Hei", Arial, sans-serif;
            font-size: 16px;
            line-height: 1.5;
            margin: 20px;
            background: white;
            color: black;
        }
    </style>
</head>
<body>
    <h1>中文测试</h1>
    <p>这是中文字体测试内容。</p>
    <p>Test Chinese Font Display</p>
</body>
</html>
TESTEOF

# 渲染测试
wkhtmltoimage \
    --width 400 \
    --height 300 \
    --format png \
    --quality 90 \
    --disable-javascript \
    --quiet \
    /tmp/simple-test.html \
    /tmp/simple-test.png

if [ -f /tmp/simple-test.png ]; then
    echo "✅ 测试成功: /tmp/simple-test.png"
    echo "文件大小: $(du -h /tmp/simple-test.png | cut -f1)"
else
    echo "❌ 测试失败"
fi

# 清理
rm -f /tmp/simple-test.html
EOF

chmod +x /usr/local/bin/test-chinese-fonts

echo ""
echo "🎉 中文字体修复完成！"
echo ""
echo "📋 修复内容:"
echo "  - 安装了多个中文字体包"
echo "  - 配置了字体优先级"
echo "  - 更新了字体缓存"
echo "  - 创建了测试脚本"
echo ""
echo "🧪 测试命令:"
echo "  test-chinese-fonts"
echo ""
echo "🔍 检查字体:"
echo "  fc-list | grep -i chinese"
echo "  fc-match 'sans-serif:lang=zh-cn'"
echo ""
echo "💡 如果仍有问题，请尝试:"
echo "  1. 重启系统"
echo "  2. 检查HTML文件编码是否为UTF-8"
echo "  3. 在CSS中明确指定中文字体"
