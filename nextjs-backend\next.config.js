/** @type {import('next').NextConfig} */
const nextConfig = {
  // 纯后端配置 - 使用新的配置项
  serverExternalPackages: ['mysql2', 'ioredis', 'ws'],

  // 输出配置
  output: 'standalone',

  // 禁用不必要的功能
  images: {
    unoptimized: true,
  },

  // 路由重写 - 兼容eggjs路由
  async rewrites() {
    // 从路由配置模块获取所有路由
    const { getRouteRewrites } = require('./src/config/routes');
    return getRouteRewrites();
  },

  // 实验性功能
  experimental: {
    // 其他实验性配置可以在这里添加
  },
};

module.exports = nextConfig;
