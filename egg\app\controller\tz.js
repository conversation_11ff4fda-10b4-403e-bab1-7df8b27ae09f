const Controller = require('egg').Controller;
const axios = require('axios');
const fs = require('fs');
let urlx = [
  'http://152.67.207.220:1369/stat',
  'http://107.173.85.180:8080/stat',
  'http://176.113.83.21:8080/stat',
  'http://139.99.3.37:1036/stat',
  'http://192.3.51.139:1036/stat',
];

let urljson = [
  {
    name: 'sg',
    url: 'http://139.99.3.37:1036/stat',
  },
  {
    name: 'krarm',
    url: 'http://152.67.207.220:1369/stat',
  },
  {
    name: 'ru',
    url: 'http://176.113.83.21:8080/stat',
  },
  {
    name: 'sj',
    url: 'http://192.3.51.139:1036/stat',
  },
  {
    name: 'ny',
    url: 'http://107.173.85.180:8080/stat',
  },
  {
    name: 'hk101',
    url: 'http://*************:1036/stat',
  },
];
class TzController extends Controller {
  async index() {
    const ctx = this.ctx;
    let url = await this.geturl();
    await ctx.render('status/stats.html', { node: url });
  }

  async login() {
    const ctx = this.ctx;
    await ctx.render('status/login.html');
  }

  async geturl() {
    return [
      {
        name: 'hk101',
        url: 'http://*************:1036/stat',
      },
      {
        name: 'LA231',
        url: 'http://***************:1036/stat',
      },
    ];

    // return await (
    //     axios.get('https://ip.wcy9.com/urlstat.php')
    //         .then(function (response) {
    //             return response.data;
    //         })
    // );
  }

  async getStat() {
    const ctx = this.ctx;
    let url = await this.geturl();
    let vps = [];
    for (var geturl of url) {
      vps.push(await this.getStat1(geturl.url));
    }
    ctx.body = vps;
  }

  async getStat1(url) {
    return await axios
      .get(url)
      .then(function (response) {
        return response.data;
      })
      .catch(function (err) {
        return {};
      });
  }

  async getStat2() {
    const ctx = this.ctx;
    let url = ctx.params.ip;
    url = 'http://' + url + '/stat';
    let data = await this.getStat1(url);
    ctx.body = data;
  }

  async getStat3() {
    const ctx = this.ctx;
    let user = this.ctx.request.body;
    let url = 'http://' + user.url + '/stat';
    let data = await this.getStat1(url);
    ctx.body = data;
  }

  async getStat4() {
    const ctx = this.ctx;
    let res = await ctx.service.xr.select('stats');
    ctx.body = {
      code: 20000,
      data: res,
    };
  }

  async getStat5() {
    const ctx = this.ctx;
    let user = this.ctx.request.body;
    let res = await ctx.service.xr.find('stats', {
      id: user.id,
    });
    ctx.body = {
      code: 20000,
      data: res,
    };
  }

  async add() {
    const ctx = this.ctx;
    let data = this.ctx.request.body;
    let res = await ctx.service.xr.create('stats', data);
    ctx.body = ctx.body = {
      code: 20000,
      data: res,
    };
  }

  async del() {
    const ctx = this.ctx;
    let data = this.ctx.request.body;
    let res = await ctx.service.xr.del('stats', data);
    ctx.body = ctx.body = {
      code: 20000,
      data: res,
    };
  }

  async update() {
    const ctx = this.ctx;
    let data = this.ctx.request.body;
    let res = await ctx.service.xr.update('stats', data);
    ctx.body = ctx.body = {
      code: 20000,
      data: res,
    };
  }
}

module.exports = TzController;
