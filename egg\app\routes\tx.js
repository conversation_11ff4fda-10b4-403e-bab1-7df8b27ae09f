'use strict';

/**
 * @param {Egg.Application} app - egg application
 */
module.exports = (app) => {
  const { router, controller } = app;

  router.get('/tx', controller.tx['index']);
  router.get('/tx/hkll', controller.tx['hkllcx']);
  router.get('/tx/gzll', controller.tx['gzllcx']);

  router.get('/tx/hksnp', controller.tx['hksnp']);
  router.get('/tx/hkcresnp', controller.tx['hkcresnp']);
  router.get('/tx/hkdelsnp', controller.tx['hkdelsnp']);

  router.get('/tx/gzsnp', controller.tx['gzsnp']);
  router.get('/tx/gzcresnp', controller.tx['gzcresnp']);
  router.get('/tx/gzdelsnp', controller.tx['gzdelsnp']);
  router.get('/tx/updateip', controller.tx['updateip']);
  router.get('/ali/updateip', controller.ali['updateip']);

  router.get('/tx/sms', controller.tx['sms']);

  router.post('/ocr', controller.tx['ocr']);
  router.post('/mathocr', controller.tx['mathocr']);
};
