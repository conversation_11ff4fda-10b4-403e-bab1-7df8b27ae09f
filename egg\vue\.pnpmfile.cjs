// .pnpmfile.cjs - pnpm hooks
module.exports = {
  hooks: {
    readPackage(pkg, context) {
      // 检查是否有人试图使用 npm 或 yarn
      if (process.env.npm_execpath && process.env.npm_execpath.includes('npm')) {
        console.error('\n❌ This project only allows pnpm!');
        console.error('Please use "pnpm" instead of "npm"');
        console.error('Example: pnpm install, pnpm run dev, etc.\n');
        process.exit(1);
      }
      
      if (process.env.npm_execpath && process.env.npm_execpath.includes('yarn')) {
        console.error('\n❌ This project only allows pnpm!');
        console.error('Please use "pnpm" instead of "yarn"');
        console.error('Example: pnpm install, pnpm add package-name, etc.\n');
        process.exit(1);
      }
      
      return pkg;
    }
  }
};
