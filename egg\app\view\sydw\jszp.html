<html>

<head>
  <meta charset="UTF-8" />
  <meta name="viewport" content="width=device-width,initial-scale=1.0" />
  <script src="/public/vue/vue.min.js"></script>
  <script src="/public/element/index.js"></script>
  <link rel="stylesheet" href="/public/element/index.css">
  <script src="/public/js/axios.min.js"></script>
  <title>Element Plus demo</title>
</head>

<body>
<div>
  {[jzsj]}
</div>
<div id="app">
  <el-alert :title="this.time" type="success" center></el-alert>
  <el-table :data="tableData" :row-class-name="tableRowClassName" border
            :default-sort="{prop: 'total', order: 'descending'}">
    <el-table-column label="序号" type="index" :index="indexMethod">
    </el-table-column>
    </el-table-column>
    <el-table-column prop="administrative" label="总人数" sortable width="160%">
    </el-table-column>
    <el-table-column prop="recruitment" label="	审核通过人数" sortable width="260%">
    </el-table-column>
    <el-table-column prop="count" label="待定人数" sortable width="100%">
    </el-table-column>
  </el-table>
</div>

<script>
  new Vue({
    el: "#app",
    data: function() {
      return {
        tableData: null,
        link: "wcnm",
        updatetime: "",
        time: ""
      };
    },
    created() {

    },
    mounted() {   //自动触发写入的函数
      this.getData();
    },
    methods: {
      formatter(row, column) {
        return row.zbmrs - 0;
        //:formatter="formatter"
      },
      formatterdate(row, column) {
        if (row.update) {
          let DateString = row.update;
          let date = new Date(DateString);
          let year = date.getFullYear();
          let month = date.getMonth() + 1;
          let day = date.getDate();
          let Hours = date.getHours();
          let Minutes = date.getMinutes();
          let Seconds = date.getSeconds();
          if (month < 10) {
            month = "0" + month;
          }
          if (day < 10) {
            day = "0" + day;
          }
          return year + "-" + month + "-" + day + " " + Hours + ":" + Minutes + ":" + Seconds;
        }
      },
      tableRowClassName({ row, rowIndex }) {
        console.log('row',row,rowIndex);
        if (
          (+rowIndex === 5)
        ) {
          return "warning-row";
        } else if (

          (+rowIndex === 4)
        ) {
          return "success-row";
        } else if (
          (+rowIndex === 14)
        ) {
          return "error-row";
        }
        return "";
      },
      getData() {
        axios.get("/jszp?data=1").then(res => {
          this.tableData = res.data.list;
          this.time = res.data.time;
          console.log(this.time);
        });
      },
      indexMethod(index) {
        return index * 1 + 1;
      }

    }
  });


</script>

<style>

    body {
        display: inline-block;
    }

    .el-table .warning-row {
        background: oldlace;
    }

    .el-table .success-row {
        background: #f0f9eb;
    }

    .el-table .error-row {
        background: #fff2be;
    }

    .el-table .man-row {
        background: #ffff90;
    }

    .blue-text {
        color: skyblue;
    }
</style>
</body>

</html>
