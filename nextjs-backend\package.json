{"name": "nextjs-backend", "version": "0.1.0", "private": true, "engines": {"node": ">=18.0.0", "pnpm": ">=8.0.0"}, "packageManager": "pnpm@8.15.0", "scripts": {"preinstall": "npx only-allow pnpm", "dev": "powershell -ExecutionPolicy Bypass -File ./kill-port.ps1 -Port 3000 -Name Nextjs && node server.js", "dev:next": "next dev --turbopack", "build": "next build", "start": "NODE_ENV=production node server.js", "start:next": "next start", "lint": "next lint"}, "dependencies": {"axios": "^1.11.0", "ioredis": "^5.6.1", "mysql2": "^3.14.3", "next": "15.4.5", "react": "19.1.0", "react-dom": "19.1.0", "ws": "^8.18.3"}, "devDependencies": {"@eslint/eslintrc": "^3", "@tailwindcss/postcss": "^4", "@types/node": "24.1.0", "@types/react": "19.1.9", "eslint": "^9", "eslint-config-next": "15.4.5", "tailwindcss": "^4", "typescript": "5.8.3"}}