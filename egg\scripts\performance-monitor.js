#!/usr/bin/env node

/**
 * 性能监控和报告脚本
 * 分析启动性能、生成性能趋势报告、监控热加载响应时间
 */

const fs = require('fs');
const path = require('path');
const os = require('os');

// 性能报告文件路径
const REPORT_PATH = path.join(__dirname, '..', 'run', 'performance-report.json');
const HOT_RELOAD_LOG_PATH = path.join(__dirname, '..', 'run', 'hot-reload-log.json');

/**
 * 读取性能报告
 */
function readPerformanceReports() {
  if (!fs.existsSync(REPORT_PATH)) {
    return [];
  }
  
  try {
    return JSON.parse(fs.readFileSync(REPORT_PATH, 'utf8'));
  } catch (error) {
    console.error('❌ 读取性能报告失败:', error.message);
    return [];
  }
}

/**
 * 分析启动性能趋势
 */
function analyzeStartupPerformance() {
  const reports = readPerformanceReports();
  
  if (reports.length === 0) {
    console.log('📊 暂无启动性能数据');
    return;
  }
  
  console.log('📈 启动性能分析报告');
  console.log('='.repeat(50));
  
  // 最新启动时间
  const latest = reports[reports.length - 1];
  console.log(`🚀 最新启动时间: ${latest.totalStartupTime}ms`);
  console.log(`📅 启动时间: ${new Date(latest.timestamp).toLocaleString('zh-CN')}`);
  
  if (reports.length > 1) {
    // 计算平均启动时间
    const avgStartupTime = Math.round(
      reports.reduce((sum, report) => sum + report.totalStartupTime, 0) / reports.length
    );
    
    // 计算最快和最慢启动时间
    const startupTimes = reports.map(r => r.totalStartupTime);
    const minStartupTime = Math.min(...startupTimes);
    const maxStartupTime = Math.max(...startupTimes);
    
    console.log(`📊 平均启动时间: ${avgStartupTime}ms`);
    console.log(`⚡ 最快启动时间: ${minStartupTime}ms`);
    console.log(`🐌 最慢启动时间: ${maxStartupTime}ms`);
    
    // 性能趋势分析
    const recent5 = reports.slice(-5);
    const older5 = reports.slice(-10, -5);
    
    if (older5.length > 0) {
      const recentAvg = recent5.reduce((sum, r) => sum + r.totalStartupTime, 0) / recent5.length;
      const olderAvg = older5.reduce((sum, r) => sum + r.totalStartupTime, 0) / older5.length;
      const improvement = olderAvg - recentAvg;
      
      if (improvement > 0) {
        console.log(`📈 性能提升: ${Math.round(improvement)}ms (${Math.round(improvement / olderAvg * 100)}%)`);
      } else {
        console.log(`📉 性能下降: ${Math.round(-improvement)}ms (${Math.round(-improvement / olderAvg * 100)}%)`);
      }
    }
  }
  
  // 分析各阶段耗时
  console.log('\n🔍 各阶段耗时分析:');
  const phaseStats = {};
  
  reports.forEach(report => {
    Object.entries(report.phases).forEach(([phase, time]) => {
      if (!phaseStats[phase]) {
        phaseStats[phase] = [];
      }
      phaseStats[phase].push(time);
    });
  });
  
  Object.entries(phaseStats).forEach(([phase, times]) => {
    const avg = Math.round(times.reduce((sum, time) => sum + time, 0) / times.length);
    const min = Math.min(...times);
    const max = Math.max(...times);
    console.log(`   ${phase}: 平均${avg}ms (${min}ms - ${max}ms)`);
  });
}

/**
 * 监控热加载响应时间
 */
function monitorHotReload() {
  console.log('\n🔥 热加载监控');
  console.log('='.repeat(50));
  
  if (!fs.existsSync(HOT_RELOAD_LOG_PATH)) {
    console.log('📊 暂无热加载数据');
    return;
  }
  
  try {
    const hotReloadLogs = JSON.parse(fs.readFileSync(HOT_RELOAD_LOG_PATH, 'utf8'));
    
    if (hotReloadLogs.length === 0) {
      console.log('📊 暂无热加载数据');
      return;
    }
    
    const reloadTimes = hotReloadLogs.map(log => log.reloadTime);
    const avgReloadTime = Math.round(reloadTimes.reduce((sum, time) => sum + time, 0) / reloadTimes.length);
    const minReloadTime = Math.min(...reloadTimes);
    const maxReloadTime = Math.max(...reloadTimes);
    
    console.log(`🔄 热加载次数: ${hotReloadLogs.length}`);
    console.log(`⚡ 平均重载时间: ${avgReloadTime}ms`);
    console.log(`🚀 最快重载时间: ${minReloadTime}ms`);
    console.log(`🐌 最慢重载时间: ${maxReloadTime}ms`);
    
    // 最近的热加载记录
    const recent = hotReloadLogs.slice(-5);
    console.log('\n📋 最近5次热加载:');
    recent.forEach((log, index) => {
      console.log(`   ${index + 1}. ${log.reloadTime}ms - ${new Date(log.timestamp).toLocaleString('zh-CN')}`);
      if (log.changedFiles) {
        console.log(`      变更文件: ${log.changedFiles.join(', ')}`);
      }
    });
    
  } catch (error) {
    console.error('❌ 读取热加载日志失败:', error.message);
  }
}

/**
 * 生成系统性能概览
 */
function generateSystemOverview() {
  console.log('\n💻 系统性能概览');
  console.log('='.repeat(50));
  
  const memUsage = process.memoryUsage();
  const cpuUsage = process.cpuUsage();
  
  console.log(`🧠 内存使用:`);
  console.log(`   RSS: ${Math.round(memUsage.rss / 1024 / 1024)}MB`);
  console.log(`   堆内存: ${Math.round(memUsage.heapUsed / 1024 / 1024)}MB / ${Math.round(memUsage.heapTotal / 1024 / 1024)}MB`);
  console.log(`   外部内存: ${Math.round(memUsage.external / 1024 / 1024)}MB`);
  
  console.log(`⚡ CPU信息:`);
  console.log(`   核心数: ${os.cpus().length}`);
  console.log(`   架构: ${os.arch()}`);
  console.log(`   平台: ${os.platform()}`);
  
  console.log(`💾 系统内存:`);
  console.log(`   总内存: ${Math.round(os.totalmem() / 1024 / 1024 / 1024)}GB`);
  console.log(`   可用内存: ${Math.round(os.freemem() / 1024 / 1024 / 1024)}GB`);
  console.log(`   内存使用率: ${Math.round((1 - os.freemem() / os.totalmem()) * 100)}%`);
}

/**
 * 生成性能优化建议
 */
function generateOptimizationSuggestions() {
  console.log('\n💡 性能优化建议');
  console.log('='.repeat(50));
  
  const reports = readPerformanceReports();
  
  if (reports.length === 0) {
    console.log('📊 需要更多数据来生成建议');
    return;
  }
  
  const latest = reports[reports.length - 1];
  const suggestions = [];
  
  // 启动时间建议
  if (latest.totalStartupTime > 5000) {
    suggestions.push('🚀 启动时间较长，建议检查模块加载和数据库连接配置');
  }
  
  // 各阶段分析
  Object.entries(latest.phases).forEach(([phase, time]) => {
    if (phase.includes('插件') && time > 2000) {
      suggestions.push('🔌 插件加载时间较长，建议优化插件配置或按需加载');
    }
    if (phase.includes('数据库') && time > 1000) {
      suggestions.push('🗄️ 数据库连接时间较长，建议检查网络和连接池配置');
    }
  });
  
  // 内存使用建议
  const memUsage = process.memoryUsage();
  if (memUsage.heapUsed > 512 * 1024 * 1024) { // 超过512MB
    suggestions.push('💾 堆内存使用较高，建议检查内存泄漏和优化数据结构');
  }
  
  if (suggestions.length === 0) {
    console.log('✅ 当前性能表现良好，无特殊优化建议');
  } else {
    suggestions.forEach((suggestion, index) => {
      console.log(`   ${index + 1}. ${suggestion}`);
    });
  }
}

/**
 * 主函数
 */
function main() {
  console.log('📊 EggJS 性能监控报告');
  console.log('生成时间:', new Date().toLocaleString('zh-CN'));
  console.log('='.repeat(70));
  
  analyzeStartupPerformance();
  monitorHotReload();
  generateSystemOverview();
  generateOptimizationSuggestions();
  
  console.log('\n' + '='.repeat(70));
  console.log('✅ 性能监控报告生成完成');
}

// 如果直接运行脚本
if (require.main === module) {
  main();
}

module.exports = {
  analyzeStartupPerformance,
  monitorHotReload,
  generateSystemOverview,
  generateOptimizationSuggestions,
  main
};
