/**
 * 粉笔页面状态恢复API
 * 迁移自egg项目的fb.js fbrecoverpage方法
 */

const { NextResponse } = require('next/server');
const { app } = require('../../../../lib/app');

// 确保应用已初始化
let appInitialized = false;

async function ensureAppInitialized() {
  if (!appInitialized) {
    await app.init();
    appInitialized = true;
  }
}

async function GET(request) {
  try {
    await ensureAppInitialized();
    
    const { searchParams } = new URL(request.url);
    const cateid = searchParams.get('cateid');
    const kjid = searchParams.get('kjid');
    const type = searchParams.get('type');
    
    console.log('🔄 恢复页面状态 - 参数:', { cateid, kjid, type });
    
    if (!kjid || !type) {
      return NextResponse.json({ 
        error: '缺少必要参数 (kjid, type)' 
      }, { status: 400 });
    }
    
    let stateData = null;
    
    try {
      // 首先尝试从Redis获取
      const stateKey = `page_state:${kjid}:${type}${cateid ? `:${cateid}` : ''}`;
      const redisData = await app.redis.get(stateKey);
      
      if (redisData) {
        stateData = JSON.parse(redisData);
        console.log('✅ 从Redis恢复页面状态:', stateKey);
      } else {
        // 如果没有找到特定状态，尝试获取最后访问状态
        const lastStateKey = `last_state:${kjid}`;
        const lastStateData = await app.redis.get(lastStateKey);
        
        if (lastStateData) {
          stateData = JSON.parse(lastStateData);
          console.log('✅ 从Redis恢复最后访问状态:', lastStateKey);
        }
      }
      
    } catch (redisError) {
      console.error('❌ Redis获取失败:', redisError.message);
    }
    
    // 如果Redis没有数据，尝试从数据库获取
    if (!stateData) {
      try {
        const dbState = await app.mysql.get('page_states', {
          kjid,
          type,
          cateid: cateid || null
        });
        
        if (dbState) {
          stateData = {
            cateid: dbState.cateid,
            kjid: dbState.kjid,
            type: dbState.type,
            page: dbState.page,
            timestamp: dbState.timestamp,
            lastAccess: dbState.lastAccess
          };
          console.log('✅ 从数据库恢复页面状态');
        }
        
      } catch (dbError) {
        console.error('❌ 数据库获取失败:', dbError.message);
      }
    }
    
    // 如果都没有找到，返回默认状态
    if (!stateData) {
      stateData = {
        cateid: cateid || null,
        kjid,
        type,
        page: 1, // 默认第一页
        timestamp: new Date().toISOString(),
        lastAccess: Date.now(),
        isDefault: true
      };
      console.log('📄 使用默认页面状态');
    }
    
    // 更新最后访问时间
    try {
      const updatedStateData = {
        ...stateData,
        lastAccess: Date.now(),
        recoveredAt: new Date().toISOString()
      };
      
      // 更新Redis中的访问时间
      const stateKey = `page_state:${kjid}:${type}${cateid ? `:${cateid}` : ''}`;
      await app.redis.setex(
        stateKey,
        86400,
        JSON.stringify(updatedStateData)
      );
      
      stateData = updatedStateData;
      
    } catch (updateError) {
      console.error('❌ 更新访问时间失败:', updateError.message);
      // 不影响主流程
    }
    
    // 返回与egg项目一致的格式: { page: number }
    return NextResponse.json({
      page: stateData?.page || 1
    });
    
  } catch (error) {
    console.error('❌ fbrecoverpage API错误:', error.message);
    return NextResponse.json({ 
      success: false,
      error: error.message,
      timestamp: new Date().toISOString()
    }, { status: 500 });
  }
}

module.exports = { GET };
