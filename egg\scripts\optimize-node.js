#!/usr/bin/env node

/**
 * Node.js V8引擎参数优化脚本
 * 根据系统环境和硬件配置动态设置最优的V8参数
 */

const os = require('os');
const fs = require('fs');
const path = require('path');

// 获取系统信息
const totalMemory = os.totalmem();
const freeMemory = os.freemem();
const cpuCount = os.cpus().length;
const platform = os.platform();

// 内存单位转换 (字节转MB)
const totalMemoryMB = Math.round(totalMemory / 1024 / 1024);
const freeMemoryMB = Math.round(freeMemory / 1024 / 1024);

console.log('🔧 Node.js V8引擎参数优化');
console.log('================================');
console.log(`💻 系统平台: ${platform}`);
console.log(`🧠 总内存: ${totalMemoryMB}MB`);
console.log(`💾 可用内存: ${freeMemoryMB}MB`);
console.log(`⚡ CPU核心数: ${cpuCount}`);

// 根据环境变量确定运行模式
const nodeEnv = process.env.NODE_ENV || 'development';
const eggEnv = process.env.EGG_SERVER_ENV || 'local';

console.log(`🌍 运行环境: ${nodeEnv} (${eggEnv})`);

// 计算最优内存配置
function calculateOptimalMemory() {
  let maxOldSpaceSize;
  let maxSemiSpaceSize;
  
  if (nodeEnv === 'production') {
    // 生产环境：使用更多内存，但保留系统内存
    maxOldSpaceSize = Math.min(
      Math.floor(totalMemoryMB * 0.7), // 最多使用70%的总内存
      8192 // 最大8GB
    );
    maxSemiSpaceSize = Math.min(
      Math.floor(maxOldSpaceSize / 64), // 新生代内存为老生代的1/64
      128 // 最大128MB
    );
  } else {
    // 开发环境：使用较少内存，为其他开发工具留空间
    maxOldSpaceSize = Math.min(
      Math.floor(totalMemoryMB * 0.4), // 最多使用40%的总内存
      4096 // 最大4GB
    );
    maxSemiSpaceSize = Math.min(
      Math.floor(maxOldSpaceSize / 64),
      64 // 最大64MB
    );
  }
  
  // 确保最小值
  maxOldSpaceSize = Math.max(maxOldSpaceSize, 512); // 最小512MB
  maxSemiSpaceSize = Math.max(maxSemiSpaceSize, 16); // 最小16MB
  
  return { maxOldSpaceSize, maxSemiSpaceSize };
}

// 生成优化参数
const { maxOldSpaceSize, maxSemiSpaceSize } = calculateOptimalMemory();

// 构建NODE_OPTIONS
const nodeOptions = [
  `--max-old-space-size=${maxOldSpaceSize}`,
  `--max-semi-space-size=${maxSemiSpaceSize}`,
  '--optimize-for-size', // 优化内存使用
  '--gc-interval=100', // 垃圾回收间隔
];

// 根据环境添加额外参数
if (nodeEnv === 'development') {
  nodeOptions.push('--trace-warnings'); // 开发环境显示警告堆栈
}

if (nodeEnv === 'production') {
  nodeOptions.push('--no-deprecation'); // 生产环境隐藏废弃警告
}

const nodeOptionsString = nodeOptions.join(' ');

console.log('🚀 推荐的V8参数:');
console.log(`   --max-old-space-size=${maxOldSpaceSize} (老生代堆内存)`);
console.log(`   --max-semi-space-size=${maxSemiSpaceSize} (新生代堆内存)`);
console.log(`   完整参数: ${nodeOptionsString}`);

// 保存配置到文件
const configPath = path.join(__dirname, '..', 'run', 'node-options.json');
const configDir = path.dirname(configPath);

// 确保目录存在
if (!fs.existsSync(configDir)) {
  fs.mkdirSync(configDir, { recursive: true });
}

const config = {
  timestamp: new Date().toISOString(),
  environment: nodeEnv,
  eggEnvironment: eggEnv,
  systemInfo: {
    platform,
    totalMemoryMB,
    freeMemoryMB,
    cpuCount
  },
  nodeOptions: {
    maxOldSpaceSize,
    maxSemiSpaceSize,
    full: nodeOptionsString
  }
};

fs.writeFileSync(configPath, JSON.stringify(config, null, 2));
console.log(`📝 配置已保存到: ${configPath}`);

// 输出环境变量设置建议
console.log('\n💡 使用建议:');
console.log(`   export NODE_OPTIONS="${nodeOptionsString}"`);
console.log('   或在package.json中使用cross-env设置');

console.log('\n✅ V8参数优化完成!');

// 如果是直接运行脚本，设置环境变量
if (require.main === module) {
  process.env.NODE_OPTIONS = nodeOptionsString;
  console.log('🔧 已设置当前进程的NODE_OPTIONS');
}

module.exports = {
  calculateOptimalMemory,
  getNodeOptions: () => nodeOptionsString,
  config
};
