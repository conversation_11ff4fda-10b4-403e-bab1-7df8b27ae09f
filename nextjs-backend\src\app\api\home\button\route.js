/**
 * 按钮操作API路由
 * 迁移自egg项目的home.js button方法
 */

const { NextResponse } = require('next/server');
const { app } = require('../../../../lib/app');

// 确保应用已初始化
let appInitialized = false;

async function ensureAppInitialized() {
  if (!appInitialized) {
    await app.init();
    appInitialized = true;
  }
}

async function GET(request) {
  try {
    await ensureAppInitialized();
    
    // 原egg项目的button方法是空的，这里保持一致
    // 可以在这里添加按钮相关的逻辑
    
    return NextResponse.json({
      success: true,
      message: 'Button API endpoint',
      timestamp: new Date().toISOString()
    });
  } catch (error) {
    console.error('❌ Button API错误:', error.message);
    return NextResponse.json({ 
      error: error.message,
      timestamp: new Date().toISOString()
    }, { status: 500 });
  }
}

async function POST(request) {
  try {
    await ensureAppInitialized();
    
    const body = await request.json();
    
    // 处理按钮操作
    console.log('🔘 按钮操作请求:', body);
    
    return NextResponse.json({
      success: true,
      message: 'Button operation completed',
      data: body,
      timestamp: new Date().toISOString()
    });
  } catch (error) {
    console.error('❌ Button POST API错误:', error.message);
    return NextResponse.json({ 
      error: error.message,
      timestamp: new Date().toISOString()
    }, { status: 500 });
  }
}

module.exports = { GET, POST };
