#!/bin/bash

# 清除Egg.js进程和释放7001端口的高性能脚本
# 使用方法: bash clear_eggjs.sh [-f|--force] [-q|--quiet]

# 颜色定义
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[0;33m'
NC='\033[0m' # 无颜色

# 默认选项
FORCE=0
QUIET=0

# 解析命令行参数
while [[ $# -gt 0 ]]; do
  case $1 in
    -f|--force)
      FORCE=1
      shift
      ;;
    -q|--quiet)
      QUIET=1
      shift
      ;;
    *)
      echo -e "${RED}错误: 未知参数 $1${NC}" >&2
      exit 1
      ;;
  esac
done

# 日志函数
log() {
  if [[ $QUIET -eq 0 ]]; then
    echo -e "$@"
  fi
}

# 错误处理函数
handle_error() {
  log "${RED}执行失败: $1${NC}"
  if [[ $2 -ne 0 ]]; then
    exit $2
  fi
}

# 查找Egg.js进程
find_eggjs_processes() {
  pgrep -f 'egg-cluster|egg-scripts|egg-server|node.*egg'
}

# 查找7001端口占用进程
find_port_7001_processes() {
  lsof -t -i:7001
}

# 优雅停止进程
graceful_stop() {
  local pids="$1"
  local process_name="$2"
  
  if [[ -n "$pids" ]]; then
    log "${YELLOW}正在优雅停止${process_name}进程:${NC} $pids"
    kill $pids
    sleep 2  # 等待2秒让进程有时间优雅退出
    
    # 检查进程是否还在运行
    local remaining_pids=$(echo "$pids" | xargs -I{} sh -c "ps -p {} >/dev/null 2>&1 && echo {}")
    if [[ -n "$remaining_pids" ]]; then
      log "${YELLOW}部分${process_name}进程仍在运行:${NC} $remaining_pids"
      return 1
    else
      log "${GREEN}${process_name}进程已优雅停止${NC}"
      return 0
    fi
  else
    log "${GREEN}没有找到${process_name}进程${NC}"
    return 0
  fi
}

# 强制终止进程
force_kill() {
  local pids="$1"
  local process_name="$2"
  
  if [[ -n "$pids" ]]; then
    log "${RED}正在强制终止${process_name}进程:${NC} $pids"
    kill -9 $pids
    log "${GREEN}${process_name}进程已强制终止${NC}"
  else
    log "${GREEN}没有找到${process_name}进程${NC}"
  fi
}

# 主执行流程
log "${YELLOW}===== 开始清除Egg.js进程和7001端口占用 ====${NC}"

# 首先尝试优雅停止egg-scripts
log "${YELLOW}尝试优雅停止egg-scripts...${NC}"
pnpm exec egg-scripts stop --title=egg 2>/dev/null || true

# 等待一下让进程有时间停止
sleep 2

# 查找Egg.js进程
EGG_PIDS=$(find_eggjs_processes)

# 查找7001端口占用进程
PORT_PIDS=$(find_port_7001_processes)

# 停止Egg.js进程
if [[ $FORCE -eq 1 ]]; then
  force_kill "$EGG_PIDS" "Egg.js"
else
  graceful_stop "$EGG_PIDS" "Egg.js" || force_kill "$(find_eggjs_processes)" "Egg.js"
fi

# 释放7001端口
if [[ $FORCE -eq 1 ]]; then
  force_kill "$PORT_PIDS" "端口7001占用"
else
  graceful_stop "$PORT_PIDS" "端口7001占用" || force_kill "$(find_port_7001_processes)" "端口7001占用"
fi

# 额外清理：查找所有可能的node进程
log "${YELLOW}清理其他可能的node进程...${NC}"
OTHER_PIDS=$(pgrep -f "node.*7001" 2>/dev/null || true)
if [[ -n "$OTHER_PIDS" ]]; then
  log "${YELLOW}发现其他7001相关进程: $OTHER_PIDS${NC}"
  if [[ $FORCE -eq 1 ]]; then
    force_kill "$OTHER_PIDS" "其他7001相关"
  else
    graceful_stop "$OTHER_PIDS" "其他7001相关" || force_kill "$OTHER_PIDS" "其他7001相关"
  fi
fi

# 验证清理结果
FINAL_EGG_PIDS=$(find_eggjs_processes)
FINAL_PORT_PIDS=$(find_port_7001_processes)

if [[ -z "$FINAL_EGG_PIDS" && -z "$FINAL_PORT_PIDS" ]]; then
  log "${GREEN}===== 清理完成: 所有Egg.js进程和7001端口占用已清除 ====${NC}"
  exit 0
else
  log "${RED}===== 清理不完全 ====${NC}"
  [[ -n "$FINAL_EGG_PIDS" ]] && log "${RED}仍有Egg.js进程在运行:${NC} $FINAL_EGG_PIDS"
  [[ -n "$FINAL_PORT_PIDS" ]] && log "${RED}端口7001仍被占用:${NC} $FINAL_PORT_PIDS"
  exit 1
fi