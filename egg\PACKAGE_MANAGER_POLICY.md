# 包管理器使用政策

## 🚫 禁止使用的包管理器

本项目**严格禁止**使用以下包管理器：
- ❌ `npm`
- ❌ `yarn`

## ✅ 强制使用的包管理器

本项目**必须使用**：
- ✅ `pnpm` (版本 >= 8.0.0)

## 📋 常用命令对照表

| 操作 | npm | yarn | **pnpm (使用这个)** |
|------|-----|------|---------------------|
| 安装依赖 | `npm install` | `yarn install` | `pnpm install` |
| 添加依赖 | `npm install <pkg>` | `yarn add <pkg>` | `pnpm add <pkg>` |
| 移除依赖 | `npm uninstall <pkg>` | `yarn remove <pkg>` | `pnpm remove <pkg>` |
| 运行脚本 | `npm run <script>` | `yarn <script>` | `pnpm <script>` |
| 全局安装 | `npm install -g <pkg>` | `yarn global add <pkg>` | `pnpm add -g <pkg>` |

## 🛡️ 强制措施

项目已配置以下强制措施：

1. **`.npmrc`** - 配置引擎严格模式
2. **`package.json`** - 设置 engines 字段禁止 npm/yarn
3. **`preinstall` 脚本** - 运行时检查包管理器
4. **`.yarnrc.yml`** - 禁用 yarn 配置
5. **检查脚本** - 自动检测并阻止错误的包管理器

## 🚨 错误处理

如果尝试使用 npm 或 yarn，会看到以下错误信息：

```
❌ 错误：禁止使用 npm/yarn！
请使用 pnpm 代替：
  pnpm install
  pnpm add <package>
  pnpm remove <package>
```

## 📦 安装 pnpm

如果还没有安装 pnpm，请使用以下命令：

```bash
# 使用 npm 安装 pnpm (仅此一次)
npm install -g pnpm

# 或使用官方安装脚本
curl -fsSL https://get.pnpm.io/install.sh | sh -

# Windows PowerShell
iwr https://get.pnpm.io/install.ps1 -useb | iex
```

## 🔧 为什么使用 pnpm？

1. **磁盘空间效率** - 通过硬链接共享依赖
2. **安装速度快** - 并行安装和缓存机制
3. **严格的依赖管理** - 避免幽灵依赖
4. **Monorepo 支持** - 原生支持工作空间
5. **兼容性好** - 与 npm 生态系统完全兼容
