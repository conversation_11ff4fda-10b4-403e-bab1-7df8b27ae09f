/**
 * 数据库连接管理
 * 提供类似egg项目的app.mysql接口
 */

const mysql = require('mysql2/promise');
const { getConfig } = require('./config');

// 连接池实例
let pool = null;

/**
 * 初始化数据库连接池
 */
function initDatabase() {
  if (pool) {
    return pool;
  }

  const config = getConfig('database.mysql');
  
  pool = mysql.createPool({
    host: config.host,
    port: config.port,
    user: config.user,
    password: config.password,
    database: config.database,
    charset: config.charset || 'utf8mb4',
    connectionLimit: config.connectionLimit || 10,
    acquireTimeout: config.acquireTimeout || 60000,
    timeout: config.timeout || 60000,
    reconnect: config.reconnect !== false,
    // 连接池配置
    queueLimit: 0,
    // 空闲连接超时时间
    idleTimeout: 300000, // 5分钟
    // 连接最大生存时间
    maxLifetime: 1800000, // 30分钟
    // 启用多语句查询
    multipleStatements: true,
    // 时区设置
    timezone: '+08:00'
  });

  console.log('✅ MySQL连接池初始化成功');
  return pool;
}

/**
 * 获取数据库连接池
 */
function getPool() {
  if (!pool) {
    return initDatabase();
  }
  return pool;
}

/**
 * 执行SQL查询
 * @param {string} sql SQL语句
 * @param {Array} params 参数
 * @returns {Promise<Array>} 查询结果
 */
async function query(sql, params = []) {
  const pool = getPool();
  try {
    const [rows] = await pool.execute(sql, params);
    return rows;
  } catch (error) {
    console.error('❌ SQL查询错误:', error.message);
    console.error('SQL:', sql);
    console.error('参数:', params);
    throw error;
  }
}

/**
 * 查询单条记录 (类似egg的app.mysql.get)
 * @param {string} table 表名
 * @param {Object} where 查询条件
 * @returns {Promise<Object|null>} 查询结果
 */
async function get(table, where = {}) {
  const whereClause = Object.keys(where).map(key => `${key} = ?`).join(' AND ');
  const values = Object.values(where);
  
  const sql = whereClause 
    ? `SELECT * FROM ${table} WHERE ${whereClause} LIMIT 1`
    : `SELECT * FROM ${table} LIMIT 1`;
    
  const rows = await query(sql, values);
  return rows.length > 0 ? rows[0] : null;
}

/**
 * 查询多条记录 (类似egg的app.mysql.select)
 * @param {string} table 表名
 * @param {Object} options 查询选项
 * @returns {Promise<Array>} 查询结果
 */
async function select(table, options = {}) {
  const { where = {}, orders = [], limit, offset, columns = '*' } = options;
  
  let sql = `SELECT ${Array.isArray(columns) ? columns.join(', ') : columns} FROM ${table}`;
  const values = [];
  
  // WHERE条件
  if (Object.keys(where).length > 0) {
    const whereClause = Object.keys(where).map(key => `${key} = ?`).join(' AND ');
    sql += ` WHERE ${whereClause}`;
    values.push(...Object.values(where));
  }
  
  // ORDER BY
  if (orders.length > 0) {
    const orderClause = orders.map(order => {
      if (typeof order === 'string') return order;
      if (Array.isArray(order)) return `${order[0]} ${order[1] || 'ASC'}`;
      return `${order.column || order.field} ${order.order || 'ASC'}`;
    }).join(', ');
    sql += ` ORDER BY ${orderClause}`;
  }
  
  // LIMIT
  if (limit) {
    sql += ` LIMIT ${limit}`;
    if (offset) {
      sql += ` OFFSET ${offset}`;
    }
  }
  
  return await query(sql, values);
}

/**
 * 插入记录 (类似egg的app.mysql.insert)
 * @param {string} table 表名
 * @param {Object} data 插入数据
 * @returns {Promise<Object>} 插入结果
 */
async function insert(table, data) {
  const keys = Object.keys(data);
  const values = Object.values(data);
  const placeholders = keys.map(() => '?').join(', ');
  
  const sql = `INSERT INTO ${table} (${keys.join(', ')}) VALUES (${placeholders})`;
  const result = await query(sql, values);
  
  return {
    affectedRows: result.affectedRows,
    insertId: result.insertId,
    warningCount: result.warningCount
  };
}

/**
 * 更新记录 (类似egg的app.mysql.update)
 * @param {string} table 表名
 * @param {Object} data 更新数据
 * @param {Object} options 更新选项
 * @returns {Promise<Object>} 更新结果
 */
async function update(table, data, options = {}) {
  const { where = {} } = options;
  
  const setClause = Object.keys(data).map(key => `${key} = ?`).join(', ');
  const setValues = Object.values(data);
  
  const whereClause = Object.keys(where).map(key => `${key} = ?`).join(' AND ');
  const whereValues = Object.values(where);
  
  let sql = `UPDATE ${table} SET ${setClause}`;
  const values = [...setValues];
  
  if (whereClause) {
    sql += ` WHERE ${whereClause}`;
    values.push(...whereValues);
  }
  
  const result = await query(sql, values);
  
  return {
    affectedRows: result.affectedRows,
    changedRows: result.changedRows,
    warningCount: result.warningCount
  };
}

/**
 * 删除记录 (类似egg的app.mysql.delete)
 * @param {string} table 表名
 * @param {Object} where 删除条件
 * @returns {Promise<Object>} 删除结果
 */
async function deleteRecord(table, where = {}) {
  const whereClause = Object.keys(where).map(key => `${key} = ?`).join(' AND ');
  const values = Object.values(where);
  
  if (!whereClause) {
    throw new Error('删除操作必须提供WHERE条件');
  }
  
  const sql = `DELETE FROM ${table} WHERE ${whereClause}`;
  const result = await query(sql, values);
  
  return {
    affectedRows: result.affectedRows,
    warningCount: result.warningCount
  };
}

/**
 * 开始事务
 */
async function beginTransaction() {
  const connection = await getPool().getConnection();
  await connection.beginTransaction();
  return connection;
}

/**
 * 提交事务
 */
async function commit(connection) {
  await connection.commit();
  connection.release();
}

/**
 * 回滚事务
 */
async function rollback(connection) {
  await connection.rollback();
  connection.release();
}

/**
 * 健康检查
 */
async function healthCheck() {
  try {
    await query('SELECT 1');
    return { status: 'healthy', timestamp: new Date().toISOString() };
  } catch (error) {
    return { status: 'unhealthy', error: error.message, timestamp: new Date().toISOString() };
  }
}

/**
 * 关闭连接池
 */
async function closePool() {
  if (pool) {
    await pool.end();
    pool = null;
    console.log('✅ MySQL连接池已关闭');
  }
}

// 创建类似egg的app.mysql对象
const mysql_client = {
  query,
  get,
  select,
  insert,
  update,
  delete: deleteRecord,
  beginTransaction,
  commit,
  rollback,
  healthCheck,
  closePool,
  // 获取原始连接池
  getPool
};

module.exports = {
  initDatabase,
  mysql: mysql_client,
  healthCheck,
  closePool
};
