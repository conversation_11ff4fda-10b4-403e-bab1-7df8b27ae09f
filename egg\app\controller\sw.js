'use strict';

const Controller = require('egg').Controller;

class SwController extends Controller {
  // 显示开关管理页面
  async index() {
    const { ctx } = this;
    
    try {
      // 获取所有开关数据
      const switches = await ctx.service.xr.query('SELECT * FROM sw ORDER BY id ASC');
      
      // 渲染页面
      await ctx.render('sw.html', {
        title: '开关管理',
        switches: switches || [],
        timestamp: new Date().toLocaleString('zh-CN')
      });
      
    } catch (error) {
      ctx.logger.error('获取开关数据失败:', error);
      await ctx.render('sw.html', {
        title: '开关管理',
        switches: [],
        error: '获取开关数据失败，请检查数据库连接',
        timestamp: new Date().toLocaleString('zh-CN')
      });
    }
  }

  // 获取开关数据API
  async getSwitches() {
    const { ctx } = this;
    
    try {
      const switches = await ctx.service.xr.query('SELECT * FROM sw ORDER BY id ASC');
      
      ctx.body = {
        success: true,
        data: switches || [],
        timestamp: new Date().toISOString()
      };
      
    } catch (error) {
      ctx.logger.error('获取开关数据失败:', error);
      ctx.status = 500;
      ctx.body = {
        success: false,
        message: '获取开关数据失败',
        error: error.message
      };
    }
  }

  // 更新开关状态
  async updateSwitch() {
    const { ctx } = this;
    const { id, sw } = ctx.request.body;
    
    try {
      // 验证参数
      if (!id || (sw !== 0 && sw !== 1)) {
        ctx.status = 400;
        ctx.body = {
          success: false,
          message: '参数错误：id必须提供，sw必须为0或1'
        };
        return;
      }

      // 更新开关状态
      const result = await ctx.service.xr.query(
        'UPDATE sw SET sw = ? WHERE id = ?',
        [sw, id]
      );

      if (result.affectedRows > 0) {
        // 获取更新后的数据
        const updatedSwitch = await ctx.service.xr.query(
          'SELECT * FROM sw WHERE id = ?',
          [id]
        );

        ctx.logger.info(`开关更新成功: ID=${id}, 状态=${sw ? '开启' : '关闭'}`);
        
        ctx.body = {
          success: true,
          message: `开关${sw ? '开启' : '关闭'}成功`,
          data: updatedSwitch[0] || null
        };
      } else {
        ctx.status = 404;
        ctx.body = {
          success: false,
          message: '开关不存在或更新失败'
        };
      }
      
    } catch (error) {
      ctx.logger.error('更新开关状态失败:', error);
      ctx.status = 500;
      ctx.body = {
        success: false,
        message: '更新开关状态失败',
        error: error.message
      };
    }
  }

  // 创建新开关
  async createSwitch() {
    const { ctx } = this;
    const { name, sw = 0 } = ctx.request.body;
    
    try {
      // 验证参数
      if (!name || name.trim() === '') {
        ctx.status = 400;
        ctx.body = {
          success: false,
          message: '开关名称不能为空'
        };
        return;
      }

      // 检查名称是否已存在
      const existing = await ctx.service.xr.query(
        'SELECT id FROM sw WHERE name = ?',
        [name.trim()]
      );

      if (existing.length > 0) {
        ctx.status = 409;
        ctx.body = {
          success: false,
          message: '开关名称已存在'
        };
        return;
      }

      // 创建新开关
      const result = await ctx.service.xr.query(
        'INSERT INTO sw (name, sw) VALUES (?, ?)',
        [name.trim(), sw ? 1 : 0]
      );

      if (result.insertId) {
        // 获取新创建的开关数据
        const newSwitch = await ctx.service.xr.query(
          'SELECT * FROM sw WHERE id = ?',
          [result.insertId]
        );

        ctx.logger.info(`新开关创建成功: ${name}, ID=${result.insertId}`);
        
        ctx.body = {
          success: true,
          message: '开关创建成功',
          data: newSwitch[0] || null
        };
      } else {
        ctx.status = 500;
        ctx.body = {
          success: false,
          message: '创建开关失败'
        };
      }
      
    } catch (error) {
      ctx.logger.error('创建开关失败:', error);
      ctx.status = 500;
      ctx.body = {
        success: false,
        message: '创建开关失败',
        error: error.message
      };
    }
  }

  // 删除开关
  async deleteSwitch() {
    const { ctx } = this;
    const { id } = ctx.params;
    
    try {
      // 验证参数
      if (!id) {
        ctx.status = 400;
        ctx.body = {
          success: false,
          message: '开关ID不能为空'
        };
        return;
      }

      // 获取要删除的开关信息
      const switchToDelete = await ctx.service.xr.query(
        'SELECT * FROM sw WHERE id = ?',
        [id]
      );

      if (switchToDelete.length === 0) {
        ctx.status = 404;
        ctx.body = {
          success: false,
          message: '开关不存在'
        };
        return;
      }

      // 删除开关
      const result = await ctx.service.xr.query(
        'DELETE FROM sw WHERE id = ?',
        [id]
      );

      if (result.affectedRows > 0) {
        ctx.logger.info(`开关删除成功: ${switchToDelete[0].name}, ID=${id}`);
        
        ctx.body = {
          success: true,
          message: '开关删除成功',
          data: switchToDelete[0]
        };
      } else {
        ctx.status = 500;
        ctx.body = {
          success: false,
          message: '删除开关失败'
        };
      }
      
    } catch (error) {
      ctx.logger.error('删除开关失败:', error);
      ctx.status = 500;
      ctx.body = {
        success: false,
        message: '删除开关失败',
        error: error.message
      };
    }
  }
}

module.exports = SwController;
