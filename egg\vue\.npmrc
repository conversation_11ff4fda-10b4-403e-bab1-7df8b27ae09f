# 强制使用 pnpm 作为包管理器
engine-strict=true

# 禁用其他包管理器
# 如果尝试使用 npm 或 yarn 会报错
package-manager-strict=true

# pnpm 配置
shamefully-hoist=false
strict-peer-dependencies=false
auto-install-peers=true

# 设置 registry
registry=https://registry.npmjs.org/

# 缓存配置
store-dir=~/.pnpm-store

# 禁用 npm 和 yarn
# 这些设置会让 npm 和 yarn 命令失败
npm-command=echo "❌ Use pnpm instead of npm!" && exit 1
yarn-command=echo "❌ Use pnpm instead of yarn!" && exit 1

# 强制失败的配置
ignore-scripts=false
