const axios = require('axios');

module.exports = {
  schedule: {
    interval: '81s', // 81秒执行一次
    type: 'all', // 指定所有的 worker 都需要执行
    env: ['prod'], // 只在生产环境执行
    immediate: true, // 应用启动后立即执行一次
    enabled: false,
  },

  async task(ctx) {
    const startTime = Date.now();
    ctx.logger.info('=== 开始执行81秒定时任务 ===');

    try {
      // 从配置文件获取服务器信息
      const config = ctx.app.config;
      const serverConfig = config.server || {};

      // 检查是否启用定时任务
      if (!serverConfig.enableSchedule) {
        ctx.logger.info('81s任务：定时任务未启用，跳过执行');
        return;
      }

      // 片源相关任务
      if (serverConfig.features?.dataSync) {
        ctx.logger.info('81s任务：开始执行片源同步任务');

        // 取消注释以启用实际任务
        // await axios.get('http://127.0.0.1:7001/pianyuan');

        ctx.logger.info('81s任务：片源同步任务执行完成');
      }

      // 其他81秒周期任务可以在这里添加
      // 例如：数据清理、缓存更新等

      const duration = Date.now() - startTime;
      ctx.logger.info(`=== 81秒定时任务执行完成，耗时: ${duration}ms ===`);
    } catch (error) {
      const duration = Date.now() - startTime;
      ctx.logger.error(`81s任务执行失败，耗时: ${duration}ms`, error);

      // 可以发送告警通知
      // await ctx.service.feishu.fs('81秒定时任务执行异常: ' + error.message);
    }
  },
};
