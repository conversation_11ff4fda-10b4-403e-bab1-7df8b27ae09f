// 启动时间监控 - 记录应用启动开始时间
const startTime = Date.now();
const performanceMonitor = {
  startTime,
  phases: {},
  markPhase: function (phase) {
    this.phases[phase] = Date.now() - this.startTime;
    console.log(`⏱️ [性能监控] ${phase}: ${this.phases[phase]}ms`);
  },
  getReport: function () {
    const totalTime = Date.now() - this.startTime;
    return {
      totalStartupTime: totalTime,
      phases: this.phases,
      timestamp: new Date().toISOString(),
    };
  },
};

// 将性能监控器挂载到全局，供其他模块使用
global.performanceMonitor = performanceMonitor;

module.exports = (app) => {
  // 记录应用初始化阶段
  performanceMonitor.markPhase('应用初始化开始');

  // 热加载监控
  if (app.config.env !== 'prod') {
    const fs = require('fs');
    const path = require('path');
    let lastReloadTime = Date.now();

    // 监听文件变更事件（开发环境）
    app.messenger.on('egg-watcher', (info) => {
      const reloadTime = Date.now() - lastReloadTime;
      lastReloadTime = Date.now();

      console.log(`🔥 [热加载] 文件变更检测到，重载时间: ${reloadTime}ms`);

      // 记录热加载日志
      const hotReloadLog = {
        reloadTime,
        timestamp: new Date().toISOString(),
        changedFiles: info.path ? [info.path] : [],
        type: info.type || 'unknown',
      };

      // 保存热加载日志
      const logPath = path.join(__dirname, 'run', 'hot-reload-log.json');
      const logDir = path.dirname(logPath);

      if (!fs.existsSync(logDir)) {
        fs.mkdirSync(logDir, { recursive: true });
      }

      let logs = [];
      if (fs.existsSync(logPath)) {
        try {
          logs = JSON.parse(fs.readFileSync(logPath, 'utf8'));
        } catch (e) {
          logs = [];
        }
      }

      logs.push(hotReloadLog);

      // 只保留最近50次热加载记录
      if (logs.length > 50) {
        logs = logs.slice(-50);
      }

      fs.writeFileSync(logPath, JSON.stringify(logs, null, 2));
    });
  }

  // 监控插件加载完成
  app.beforeStart(async () => {
    performanceMonitor.markPhase('插件加载完成');
  });

  // 应用启动完成
  app.ready(async () => {
    performanceMonitor.markPhase('应用启动完成');
    try {
      // 检查是否为生产环境
      const isProduction = app.config.env === 'prod';

      if (isProduction) {
        // 获取系统信息
        const os = require('os');
        const packageInfo = require('./package.json');
        // 生产环境启动通知
        const startupMessage = `🎉 Egg应用启动成功！
🌟 环境: ${app.config.env}
🚀 启动时间: ${new Date().toLocaleString('zh-CN', { timeZone: 'Asia/Shanghai' })}
📦 应用版本: ${packageInfo.version || '未知'}
🔧 Node版本: ${process.version}
💻 进程ID: ${process.pid}
🖥️ 系统: ${os.type()} ${os.release()}
💾 内存: ${Math.round(os.totalmem() / 1024 / 1024 / 1024)}GB
📍 运行目录: ${process.cwd()}
🌐 监听端口: ${process.env.PORT || '7001'}
⚡ CPU核心: ${os.cpus().length}个
✨ 所有服务已就绪，系统运行正常！`;

        // 发送启动成功通知到飞书
        const ctx = app.createAnonymousContext();
        await ctx.service.feishu['fs'](startupMessage);
        console.log('✅ 启动成功通知已发送到飞书');
      } else {
        // 开发环境只在控制台输出
        console.log('🎉 Egg应用启动成功 (开发环境)');
      }

      // 生成性能报告
      const performanceReport = performanceMonitor.getReport();
      console.log('\n📊 启动性能报告:');
      console.log(`🚀 总启动时间: ${performanceReport.totalStartupTime}ms`);
      console.log('📈 各阶段耗时:');
      Object.entries(performanceReport.phases).forEach(([phase, time]) => {
        console.log(`   ${phase}: ${time}ms`);
      });

      // 保存性能报告到文件
      const fs = require('fs');
      const path = require('path');
      const reportPath = path.join(__dirname, 'run', 'performance-report.json');

      // 确保目录存在
      const reportDir = path.dirname(reportPath);
      if (!fs.existsSync(reportDir)) {
        fs.mkdirSync(reportDir, { recursive: true });
      }

      // 读取历史报告
      let reports = [];
      if (fs.existsSync(reportPath)) {
        try {
          reports = JSON.parse(fs.readFileSync(reportPath, 'utf8'));
        } catch (e) {
          reports = [];
        }
      }

      // 添加当前报告
      reports.push(performanceReport);

      // 只保留最近10次报告
      if (reports.length > 10) {
        reports = reports.slice(-10);
      }

      // 保存报告
      fs.writeFileSync(reportPath, JSON.stringify(reports, null, 2));
      console.log(`📝 性能报告已保存到: ${reportPath}`);
    } catch (error) {
      console.error('❌ 发送启动通知或生成性能报告失败:', error.message);
    }
  });

  // 格式化运行时长为年月日小时分秒
  function formatUptime(seconds) {
    // 使用更精确的时间计算
    const YEAR_SECONDS = 365.25 * 24 * 3600; // 考虑闰年
    const MONTH_SECONDS = 30.44 * 24 * 3600; // 平均月份天数
    const DAY_SECONDS = 24 * 3600;
    const HOUR_SECONDS = 3600;
    const MINUTE_SECONDS = 60;

    let remaining = seconds;

    const years = Math.floor(remaining / YEAR_SECONDS);
    remaining = remaining % YEAR_SECONDS;

    const months = Math.floor(remaining / MONTH_SECONDS);
    remaining = remaining % MONTH_SECONDS;

    const days = Math.floor(remaining / DAY_SECONDS);
    remaining = remaining % DAY_SECONDS;

    const hours = Math.floor(remaining / HOUR_SECONDS);
    remaining = remaining % HOUR_SECONDS;

    const minutes = Math.floor(remaining / MINUTE_SECONDS);
    const secs = Math.floor(remaining % MINUTE_SECONDS);

    const parts = [];
    if (years > 0) parts.push(`${years}年`);
    if (months > 0) parts.push(`${months}月`);
    if (days > 0) parts.push(`${days}日`);
    if (hours > 0) parts.push(`${hours}小时`);
    if (minutes > 0) parts.push(`${minutes}分钟`);
    if (secs > 0 || parts.length === 0) parts.push(`${secs}秒`);

    return parts.join('');
  }

  // 应用关闭前的回调
  app.beforeClose(async () => {
    try {
      // 释放 redis 锁
      await app.redis.set('zhihuwenda', '2');
      console.log('✅ Redis锁 zhihuwenda 已被释放');

      const isProduction = app.config.env === 'prod';

      if (isProduction) {
        const uptimeSeconds = process.uptime();
        const formattedUptime = formatUptime(uptimeSeconds);

        const shutdownMessage = `🛑 Egg应用正在关闭
⏰ 关闭时间: ${new Date().toLocaleString('zh-CN', { timeZone: 'Asia/Shanghai' })}
💻 进程ID: ${process.pid}
📊 运行时长: ${formattedUptime}
👋 应用即将停止服务`;

        const ctx = app.createAnonymousContext();
        await ctx.service.feishu['fs'](shutdownMessage);
        console.log('✅ 关闭通知已发送到飞书');
      }
    } catch (error) {
      console.error('❌ 发送关闭通知或释放Redis锁失败:', error.message);
    }
  });
};
