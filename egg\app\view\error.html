<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>{{ title || '出错了' }}</title>
    <script src="https://cdn.tailwindcss.com"></script>
    <style>
        .error-animation {
            animation: bounce 2s infinite;
        }
        
        @keyframes bounce {
            0%, 20%, 50%, 80%, 100% {
                transform: translateY(0);
            }
            40% {
                transform: translateY(-10px);
            }
            60% {
                transform: translateY(-5px);
            }
        }

        .gradient-text {
            background: linear-gradient(45deg, #ff6b6b, #4ecdc4, #45b7d1, #96ceb4, #feca57);
            background-size: 300% 300%;
            -webkit-background-clip: text;
            -webkit-text-fill-color: transparent;
            animation: gradient 3s ease infinite;
        }

        @keyframes gradient {
            0% { background-position: 0% 50%; }
            50% { background-position: 100% 50%; }
            100% { background-position: 0% 50%; }
        }

        .floating {
            animation: floating 3s ease-in-out infinite;
        }

        @keyframes floating {
            0% { transform: translate(0, 0px); }
            50% { transform: translate(0, -10px); }
            100% { transform: translate(0, 0px); }
        }
    </style>
</head>
<body class="bg-gradient-to-br from-purple-900 via-blue-900 to-indigo-900 min-h-screen flex items-center justify-center">
    <!-- 背景装饰 -->
    <div class="absolute inset-0 overflow-hidden">
        <div class="absolute -top-40 -right-40 w-80 h-80 bg-purple-500 rounded-full mix-blend-multiply filter blur-xl opacity-70 animate-pulse"></div>
        <div class="absolute -bottom-40 -left-40 w-80 h-80 bg-blue-500 rounded-full mix-blend-multiply filter blur-xl opacity-70 animate-pulse"></div>
        <div class="absolute top-1/2 left-1/2 transform -translate-x-1/2 -translate-y-1/2 w-96 h-96 bg-pink-500 rounded-full mix-blend-multiply filter blur-xl opacity-50 animate-pulse"></div>
    </div>

    <!-- 主要内容 -->
    <div class="relative z-10 text-center px-4 max-w-2xl mx-auto">
        <!-- 错误图标 -->
        <div class="error-animation mb-8">
            {% if status == 404 %}
                <div class="text-8xl mb-4">🔍</div>
            {% elif status == 403 %}
                <div class="text-8xl mb-4">🚫</div>
            {% elif status == 500 %}
                <div class="text-8xl mb-4">⚠️</div>
            {% else %}
                <div class="text-8xl mb-4">❌</div>
            {% endif %}
        </div>

        <!-- 错误代码 -->
        <h1 class="text-9xl font-bold gradient-text mb-6">
            {{ status || '500' }}
        </h1>

        <!-- 错误标题 -->
        <h2 class="text-4xl font-bold text-white mb-4">
            {% if status == 404 %}
                页面未找到
            {% elif status == 403 %}
                访问被拒绝
            {% elif status == 500 %}
                服务器内部错误
            {% else %}
                出现了一些问题
            {% endif %}
        </h2>

        <!-- 错误描述 -->
        <p class="text-xl text-gray-300 mb-8 leading-relaxed">
            {% if status == 404 %}
                抱歉，您访问的页面不存在或已被移动。
            {% elif status == 403 %}
                您没有权限访问此资源。
            {% elif status == 500 %}
                服务器遇到了一个错误，我们正在努力修复。
            {% else %}
                {{ message || '发生了未知错误，请稍后重试。' }}
            {% endif %}
        </p>

        <!-- 详细错误信息 -->
        {% if message and message != '服务器内部错误，请稍后重试' %}
        <div class="bg-white/10 backdrop-blur-lg rounded-xl p-6 mb-8 text-left">
            <h3 class="text-lg font-semibold text-white mb-2">错误详情</h3>
            <p class="text-gray-300 text-sm font-mono">{{ message }}</p>
        </div>
        {% endif %}

        <!-- 操作按钮 -->
        <div class="flex flex-col sm:flex-row gap-4 justify-center items-center">
            <a href="/" class="floating bg-gradient-to-r from-blue-600 to-purple-600 hover:from-blue-700 hover:to-purple-700 text-white px-8 py-4 rounded-xl font-semibold text-lg transition-all duration-300 transform hover:scale-105 shadow-lg">
                🏠 返回首页
            </a>
            
            <button onclick="history.back()" class="floating bg-gradient-to-r from-gray-600 to-gray-700 hover:from-gray-700 hover:to-gray-800 text-white px-8 py-4 rounded-xl font-semibold text-lg transition-all duration-300 transform hover:scale-105 shadow-lg">
                ⬅️ 返回上页
            </button>
            
            <button onclick="location.reload()" class="floating bg-gradient-to-r from-green-600 to-teal-600 hover:from-green-700 hover:to-teal-700 text-white px-8 py-4 rounded-xl font-semibold text-lg transition-all duration-300 transform hover:scale-105 shadow-lg">
                🔄 重新加载
            </button>
        </div>

        <!-- 帮助信息 -->
        <div class="mt-12 bg-white/5 backdrop-blur-lg rounded-xl p-6">
            <h3 class="text-lg font-semibold text-white mb-4">需要帮助？</h3>
            <div class="grid grid-cols-1 md:grid-cols-3 gap-4 text-sm">
                <div class="text-center">
                    <div class="text-2xl mb-2">📧</div>
                    <p class="text-gray-300">联系管理员</p>
                    <p class="text-gray-400"><EMAIL></p>
                </div>
                <div class="text-center">
                    <div class="text-2xl mb-2">📞</div>
                    <p class="text-gray-300">技术支持</p>
                    <p class="text-gray-400">400-123-4567</p>
                </div>
                <div class="text-center">
                    <div class="text-2xl mb-2">💬</div>
                    <p class="text-gray-300">在线客服</p>
                    <p class="text-gray-400">工作日 9:00-18:00</p>
                </div>
            </div>
        </div>

        <!-- 时间戳 -->
        <div class="mt-8 text-sm text-gray-500">
            错误时间: {{ timestamp || new Date().toLocaleString('zh-CN') }}
        </div>
    </div>

    <script>
        // 添加一些交互效果
        document.addEventListener('DOMContentLoaded', function() {
            // 键盘快捷键
            document.addEventListener('keydown', function(e) {
                switch(e.key) {
                    case 'h':
                    case 'H':
                        window.location.href = '/';
                        break;
                    case 'b':
                    case 'B':
                        history.back();
                        break;
                    case 'r':
                    case 'R':
                        if (e.ctrlKey || e.metaKey) {
                            e.preventDefault();
                        }
                        location.reload();
                        break;
                }
            });

            // 显示快捷键提示
            const shortcuts = document.createElement('div');
            shortcuts.className = 'fixed bottom-4 right-4 bg-black/50 text-white text-xs p-3 rounded-lg backdrop-blur-lg';
            shortcuts.innerHTML = `
                <div class="font-semibold mb-1">快捷键:</div>
                <div>H - 返回首页</div>
                <div>B - 返回上页</div>
                <div>R - 重新加载</div>
            `;
            document.body.appendChild(shortcuts);

            // 3秒后隐藏快捷键提示
            setTimeout(() => {
                shortcuts.style.opacity = '0';
                shortcuts.style.transition = 'opacity 0.5s';
                setTimeout(() => {
                    document.body.removeChild(shortcuts);
                }, 500);
            }, 3000);
        });

        // 错误报告功能
        function reportError() {
            const errorData = {
                status: {{ status || 500 }},
                message: '{{ message | safe }}',
                url: window.location.href,
                userAgent: navigator.userAgent,
                timestamp: '{{ timestamp }}',
                referrer: document.referrer
            };

            // 这里可以发送错误报告到服务器
            console.log('Error Report:', errorData);
            
            // 显示报告已发送的提示
            const notification = document.createElement('div');
            notification.className = 'fixed top-4 right-4 bg-green-600 text-white px-4 py-2 rounded-lg shadow-lg z-50';
            notification.textContent = '错误报告已发送';
            document.body.appendChild(notification);
            
            setTimeout(() => {
                document.body.removeChild(notification);
            }, 3000);
        }
    </script>
</body>
</html>
