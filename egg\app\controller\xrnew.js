const Controller = require('egg').Controller;

class XrController extends Controller {
  async show() {
    const ctx = this.ctx;
    let page = ctx.query.page || 1;
    let type = ctx.query.type || 'XiuRen';
    page = parseInt(page);
    const infoList = await ctx.service.xr.xiurenpage(page, type);
    let info = JSON.parse(infoList);
    // const user = await ctx.service.xr.select('xr', {
    //     where: {id: id},
    //     orders: [['id', 'desc']],
    //     limit: 10, // 返回数据量
    //     offset: 0, // 数据偏移量
    // });
    await ctx.render('newxrpage.html', { list: info });
    // ctx.body = info;
  }

  async inshow() {
    const ctx = this.ctx;
    let id = ctx.params.id || 1;
    const data = await ctx.service.xr.select('xrdata', {
      where: { id: id },
    });
    // console.log(data.data);
    // ctx.body = data;
    // return
    if (data) {
      let rx = [];
      let list = data.data;
      for (let urlxx of list) {
        // rx.push(urlxx.url.replace('http://www.xiurenji.cc','https://kr.35551049.xyz'))
        rx.push(urlxx.url.replace('http://', 'https://'));
      }
      await ctx.render('inshow.html', { list: rx });
    } else {
      let down = await ctx.service.xr.down(id);
      await this.saveinxr(id);
      console.log(down);
    }
    // ctx.body = list;
  }

  async down() {
    const ctx = this.ctx;
    let id = ctx.params.id || 1;
    let xrurl = 'http://www.xiurenb.vip/XiuRen/' + id + '.html';

    let page = await ctx.service.xr.page(xrurl);
    let down = await ctx.service.xr.down(id, page);
    // await this.saveinxr(id);
    console.log(down);
  }

  async getinshowstatus() {
    const ctx = this.ctx;
    let id = 8529;
    const st = await ctx.service.xr.select('xr', {
      where: { id: id },
    });
    ctx.body = st.data;
  }

  //获取封面和图片地址
  async xrinfo() {
    const ctx = this.ctx;
    let xrurl = ctx.query.url;
    let urlfg = xrurl.split('/');
    let type = urlfg[3];
    let id = urlfg[4].split('.')[0];
    let page = await ctx.service.xr.page(xrurl);
    let arr = [];
    arr.push('http://www.xiurenji.cc/uploadfile/pic/' + id + '.jpg');
    for (let i = 0; i <= page; i++) {
      let xurl = '';
      if (i === 0) {
        xurl = 'https://www.xiurenji.cc/' + type + '/' + id + '.html';
      } else {
        xurl = 'https://www.xiurenji.cc/' + type + '/' + id + '_' + i + '.html';
      }
      let urld = await ctx.service.xr.list(xurl);
      for (let turl of urld) {
        // turl = turl.replace('/www.xiurenji.cc/','kr.35551049.xyz/').toLowerCase();
        arr.push(turl);
      }
    }
    ctx.body = arr;
    return arr;
  }

  async savexr() {
    const ctx = this.ctx;
    let page = 1;
    let type = 'XiuRen';
    page = parseInt(page);
    const infoList = await ctx.service.xr.xiurenpage(page, type);
    let info = JSON.parse(infoList);
    for (let data of info) {
      let xrurl = data.href;
      let urlfg = xrurl.split('/');
      let type = urlfg[3];
      let id = urlfg[4].split('.')[0];
      let is_save = await ctx.service.xr.find('xr', {
        id: id,
      });
      if (!is_save.data) {
        let savexr = await ctx.service.xr.create('xr', {
          id: id,
          type: type,
        });
      }
    }
    ctx.body = await ctx.service.xr.select('xr');
  }

  async saveorurl() {
    let data = [
      'https://www.xiurenji.cc/XiuRen/8540.html',
      'https://www.xiurenji.cc/XiuRen/8539.html',
      'https://www.xiurenji.cc/XiuRen/8538.html',
      'https://www.xiurenji.cc/XiuRen/8537.html',
      'https://www.xiurenji.cc/XiuRen/8536.html',
      'https://www.xiurenji.cc/XiuRen/8535.html',
      'https://www.xiurenji.cc/XiuRen/8529.html',
      'https://www.xiurenji.cc/XiuRen/8528.html',
      'https://www.xiurenji.cc/XiuRen/8527.html',
      'https://www.xiurenji.cc/XiuRen/8526.html',
      'https://www.xiurenji.cc/XiuRen/8525.html',
      'https://www.xiurenji.cc/XiuRen/8519.html',
      'https://www.xiurenji.cc/XiuRen/8518.html',
      'https://www.xiurenji.cc/XiuRen/8517.html',
      'https://www.xiurenji.cc/XiuRen/8516.html',
      'https://www.xiurenji.cc/XiuRen/8515.html',
      'https://www.xiurenji.cc/XiuRen/8514.html',
      'https://www.xiurenji.cc/XiuRen/8509.html',
      'https://www.xiurenji.cc/XiuRen/8508.html',
      'https://www.xiurenji.cc/XiuRen/8507.html',
    ];
    for (let xurl of data) {
      let urlfg = xurl.split('/');
      let id = urlfg[4].split('.')[0];
      this.saveinxr(id);
    }
  }

  async saveinxr(id) {
    const ctx = this.ctx;
    let xrurl = 'https://www.xiurenji.cc/XiuRen/' + id + '.html';
    let type = 'XiuRen';
    let page = await ctx.service.xr.page(xrurl);
    let arr = [];
    arr.push('http://kr.35551049.xyz/uploadfile/pic/' + id + '.jpg');
    for (let i = 0; i <= page; i++) {
      let xurl = '';
      if (i === 0) {
        xurl = 'https://www.xiurenji.cc/' + type + '/' + id + '.html';
      } else {
        xurl = 'https://www.xiurenji.cc/' + type + '/' + id + '_' + i + '.html';
      }
      let urld = await ctx.service.xr.list(xurl);

      for (let turl of urld) {
        // turl = turl.replace('www.xiurenji.cc', 'kr.35551049.xyz').toLowerCase();
        let savexr = await ctx.service.xr.create('xrdata', {
          id: id,
          type: type,
          url: turl,
        });
        arr.push(turl);
        console.log(savexr);
      }
    }
    ctx.body = arr;
  }
}

module.exports = XrController;
