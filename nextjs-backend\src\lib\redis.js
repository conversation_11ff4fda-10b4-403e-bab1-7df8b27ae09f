/**
 * Redis连接管理
 * 提供类似egg项目的app.redis接口
 */

const Redis = require('ioredis');
const { getConfig } = require('./config');

// Redis客户端实例
let redisClient = null;

/**
 * 初始化Redis连接
 */
function initRedis() {
  if (redisClient) {
    return redisClient;
  }

  const config = getConfig('redis');
  
  // Redis连接配置
  const redisConfig = {
    host: config.host,
    port: config.port,
    password: config.password || undefined,
    db: config.db || 0,
    connectTimeout: config.connectTimeout || 10000,
    maxRetriesPerRequest: config.maxRetriesPerRequest || 3,
    retryDelayOnFailover: config.retryDelayOnFailover || 100,
    enableReadyCheck: config.enableReadyCheck !== false,
    lazyConnect: config.lazyConnect !== false,
    // 连接池配置
    family: 4, // 4 (IPv4) or 6 (IPv6)
    keepAlive: true,
    // 重试配置
    retryDelayOnClusterDown: 300,
    retryDelayOnFailover: 100,
    maxRetriesPerRequest: 3,
    // 自动重连
    autoResubscribe: true,
    autoResendUnfulfilledCommands: true,
    // 命令超时
    commandTimeout: 5000,
    // 键名前缀
    keyPrefix: config.keyPrefix || '',
  };

  redisClient = new Redis(redisConfig);

  // 连接事件监听
  redisClient.on('connect', () => {
    console.log('✅ Redis连接成功');
  });

  redisClient.on('ready', () => {
    console.log('✅ Redis准备就绪');
  });

  redisClient.on('error', (error) => {
    console.error('❌ Redis连接错误:', error.message);
  });

  redisClient.on('close', () => {
    console.log('⚠️ Redis连接关闭');
  });

  redisClient.on('reconnecting', () => {
    console.log('🔄 Redis重新连接中...');
  });

  redisClient.on('end', () => {
    console.log('🔚 Redis连接结束');
  });

  return redisClient;
}

/**
 * 获取Redis客户端
 */
function getRedisClient() {
  if (!redisClient) {
    return initRedis();
  }
  return redisClient;
}

/**
 * 获取值 (类似egg的app.redis.get)
 * @param {string} key 键名
 * @returns {Promise<string|null>} 值
 */
async function get(key) {
  const client = getRedisClient();
  try {
    return await client.get(key);
  } catch (error) {
    console.error('❌ Redis GET错误:', error.message);
    throw error;
  }
}

/**
 * 设置值 (类似egg的app.redis.set)
 * @param {string} key 键名
 * @param {string} value 值
 * @param {number|string} ttl 过期时间（秒）
 * @returns {Promise<string>} 结果
 */
async function set(key, value, ttl) {
  const client = getRedisClient();
  try {
    if (ttl) {
      return await client.setex(key, ttl, value);
    } else {
      return await client.set(key, value);
    }
  } catch (error) {
    console.error('❌ Redis SET错误:', error.message);
    throw error;
  }
}

/**
 * 删除键 (类似egg的app.redis.del)
 * @param {string|Array} keys 键名或键名数组
 * @returns {Promise<number>} 删除的键数量
 */
async function del(keys) {
  const client = getRedisClient();
  try {
    return await client.del(keys);
  } catch (error) {
    console.error('❌ Redis DEL错误:', error.message);
    throw error;
  }
}

/**
 * 检查键是否存在
 * @param {string} key 键名
 * @returns {Promise<number>} 存在返回1，不存在返回0
 */
async function exists(key) {
  const client = getRedisClient();
  try {
    return await client.exists(key);
  } catch (error) {
    console.error('❌ Redis EXISTS错误:', error.message);
    throw error;
  }
}

/**
 * 设置过期时间
 * @param {string} key 键名
 * @param {number} seconds 过期时间（秒）
 * @returns {Promise<number>} 成功返回1，失败返回0
 */
async function expire(key, seconds) {
  const client = getRedisClient();
  try {
    return await client.expire(key, seconds);
  } catch (error) {
    console.error('❌ Redis EXPIRE错误:', error.message);
    throw error;
  }
}

/**
 * 获取剩余过期时间
 * @param {string} key 键名
 * @returns {Promise<number>} 剩余时间（秒），-1表示永不过期，-2表示键不存在
 */
async function ttl(key) {
  const client = getRedisClient();
  try {
    return await client.ttl(key);
  } catch (error) {
    console.error('❌ Redis TTL错误:', error.message);
    throw error;
  }
}

/**
 * 自增
 * @param {string} key 键名
 * @param {number} increment 增量，默认为1
 * @returns {Promise<number>} 自增后的值
 */
async function incr(key, increment = 1) {
  const client = getRedisClient();
  try {
    if (increment === 1) {
      return await client.incr(key);
    } else {
      return await client.incrby(key, increment);
    }
  } catch (error) {
    console.error('❌ Redis INCR错误:', error.message);
    throw error;
  }
}

/**
 * 自减
 * @param {string} key 键名
 * @param {number} decrement 减量，默认为1
 * @returns {Promise<number>} 自减后的值
 */
async function decr(key, decrement = 1) {
  const client = getRedisClient();
  try {
    if (decrement === 1) {
      return await client.decr(key);
    } else {
      return await client.decrby(key, decrement);
    }
  } catch (error) {
    console.error('❌ Redis DECR错误:', error.message);
    throw error;
  }
}

/**
 * 哈希表操作 - 设置字段
 * @param {string} key 键名
 * @param {string} field 字段名
 * @param {string} value 值
 * @returns {Promise<number>} 成功返回1，更新返回0
 */
async function hset(key, field, value) {
  const client = getRedisClient();
  try {
    return await client.hset(key, field, value);
  } catch (error) {
    console.error('❌ Redis HSET错误:', error.message);
    throw error;
  }
}

/**
 * 哈希表操作 - 获取字段
 * @param {string} key 键名
 * @param {string} field 字段名
 * @returns {Promise<string|null>} 字段值
 */
async function hget(key, field) {
  const client = getRedisClient();
  try {
    return await client.hget(key, field);
  } catch (error) {
    console.error('❌ Redis HGET错误:', error.message);
    throw error;
  }
}

/**
 * 哈希表操作 - 获取所有字段
 * @param {string} key 键名
 * @returns {Promise<Object>} 所有字段和值
 */
async function hgetall(key) {
  const client = getRedisClient();
  try {
    return await client.hgetall(key);
  } catch (error) {
    console.error('❌ Redis HGETALL错误:', error.message);
    throw error;
  }
}

/**
 * 列表操作 - 左推入
 * @param {string} key 键名
 * @param {string|Array} values 值
 * @returns {Promise<number>} 列表长度
 */
async function lpush(key, ...values) {
  const client = getRedisClient();
  try {
    return await client.lpush(key, ...values);
  } catch (error) {
    console.error('❌ Redis LPUSH错误:', error.message);
    throw error;
  }
}

/**
 * 列表操作 - 右弹出
 * @param {string} key 键名
 * @returns {Promise<string|null>} 弹出的值
 */
async function rpop(key) {
  const client = getRedisClient();
  try {
    return await client.rpop(key);
  } catch (error) {
    console.error('❌ Redis RPOP错误:', error.message);
    throw error;
  }
}

/**
 * 获取列表范围
 * @param {string} key 键名
 * @param {number} start 开始索引
 * @param {number} stop 结束索引
 * @returns {Promise<Array>} 列表元素
 */
async function lrange(key, start, stop) {
  const client = getRedisClient();
  try {
    return await client.lrange(key, start, stop);
  } catch (error) {
    console.error('❌ Redis LRANGE错误:', error.message);
    throw error;
  }
}

/**
 * 执行原生Redis命令
 * @param {string} command 命令名
 * @param {...any} args 命令参数
 * @returns {Promise<any>} 命令结果
 */
async function sendCommand(command, ...args) {
  const client = getRedisClient();
  try {
    return await client.sendCommand(new Redis.Command(command, args));
  } catch (error) {
    console.error(`❌ Redis ${command}错误:`, error.message);
    throw error;
  }
}

/**
 * Ping测试
 * @returns {Promise<string>} PONG
 */
async function ping() {
  const client = getRedisClient();
  try {
    return await client.ping();
  } catch (error) {
    console.error('❌ Redis PING错误:', error.message);
    throw error;
  }
}

/**
 * 健康检查
 */
async function healthCheck() {
  try {
    await ping();
    return { status: 'healthy', timestamp: new Date().toISOString() };
  } catch (error) {
    return { status: 'unhealthy', error: error.message, timestamp: new Date().toISOString() };
  }
}

/**
 * 关闭Redis连接
 */
async function closeRedis() {
  if (redisClient) {
    await redisClient.quit();
    redisClient = null;
    console.log('✅ Redis连接已关闭');
  }
}

// 创建类似egg的app.redis对象
const redis_client = {
  get,
  set,
  del,
  exists,
  expire,
  ttl,
  incr,
  decr,
  hset,
  hget,
  hgetall,
  lpush,
  rpop,
  lrange,
  sendCommand,
  ping,
  healthCheck,
  // 获取原始客户端
  getClient: getRedisClient
};

module.exports = {
  initRedis,
  redis: redis_client,
  healthCheck,
  closeRedis
};
