// middleware/logger.js
const axios = require('axios');
const os = require('os');

// IP地理位置查询缓存，避免重复查询同一IP
const ipLocationCache = new Map();

// 查询IP地理位置
async function getIPLocation(ip) {
  // 检查缓存
  if (ipLocationCache.has(ip)) {
    return ipLocationCache.get(ip);
  }

  // 跳过内网IP和本地IP
  if (
    ip === '127.0.0.1' ||
    ip.startsWith('192.168.') ||
    ip.startsWith('10.') ||
    ip.startsWith('172.')
  ) {
    const location = '内网';
    ipLocationCache.set(ip, location);
    return location;
  }

  try {
    // 首先尝试使用免费的ip-api.com服务查询IP地理位置
    let response = await axios.get(`http://ip-api.com/json/${ip}?lang=zh-CN`, {
      timeout: 3000, // 3秒超时
    });

    if (response.data && response.data.status === 'success') {
      const { country, regionName, city } = response.data;
      const location = `${country}-${regionName}-${city}`;

      // 缓存结果，避免重复查询
      ipLocationCache.set(ip, location);
      return location;
    }

    // 如果第一个API失败，尝试备用API
    response = await axios.get(`https://ipapi.co/${ip}/json/`, {
      timeout: 3000,
    });

    if (response.data && response.data.country_name) {
      const { country_name, region, city } = response.data;
      const location = `${country_name}-${region || '未知'}-${city || '未知'}`;

      // 缓存结果
      ipLocationCache.set(ip, location);
      return location;
    }

    // 所有API都失败，返回IP本身
    ipLocationCache.set(ip, ip);
    return ip;
  } catch (error) {
    console.error(`IP地理位置查询失败 ${ip}:`, error.message);
    // 查询失败，返回IP本身
    ipLocationCache.set(ip, ip);
    return ip;
  }
}
module.exports = (options, app) => {
  return async function loggerMiddleware(ctx, next) {
    try {
      // 获取服务器 IP
      // const { data: serverIp } = await axios.get('https://ip.qq.com/cnip.php');
      let ua = ctx.get('user-agent');
      // console.log(ua);
      app.logger.info(ua);
      if (!ua || ua === '' || ua === null) {
        return;
      }

      const chromeMatch = ua.match(/Chrome\/([\d\.]+)/);
      if (ua.match('Chrome')) {
        const chromeVersionStr = chromeMatch[1];
        const majorVersion = parseInt(chromeVersionStr.split('.')[0], 10);
        if (majorVersion < 100) {
          // 如果 Chrome 的主版本号小于 100，则执行返回逻辑
          return;
        }
      }
      // 检查是否是日志API请求或EventSource请求，如果是则跳过UA检查
      const isLogAPI = ctx.path.startsWith('/api/logs/');
      const isEventSource = ctx.headers.accept && ctx.headers.accept.includes('text/event-stream');
      const isLogPage = ctx.path.startsWith('/logs');

      if (isLogAPI || isEventSource || isLogPage) {
        ctx.logger.info('跳过UA检查:', {
          path: ctx.path,
          ua: ua,
          reason: isLogAPI ? 'LogAPI' : isEventSource ? 'EventSource' : 'LogPage',
          accept: ctx.headers.accept,
        });
        // 跳过UA检查，直接继续
      } else if (
        !ua.match('Chrome') &&
        !ua.match('AppleWebKit') &&
        !ua.match('xctcc') &&
        !ua.match('Clash') &&
        !ua.match('axios') &&
        !ua.match('Shadowrocket') &&
        !ua.match('Apifox') &&
        !ua.match('Reqable') &&
        !ua.match('curl') &&
        !ua.match('GitHub-Actions') &&
        !ua.match('github-actions') &&
        !ua.match('Mozilla') &&
        !ua.match('Safari') &&
        !ua.match('Edge') &&
        !ua.match('Firefox')
      ) {
        ctx.logger.info('被过滤的UA:', { path: ctx.path, ua: ua });
        return;
      }

      const networkInterfaces = os.networkInterfaces();
      let serverIp = 'Unknown IP';

      // 遍历网卡接口，获取非内网或回环地址的 IP
      for (const interfaceName in networkInterfaces) {
        const addresses = networkInterfaces[interfaceName];
        for (const address of addresses) {
          if (address.family === 'IPv4' && !address.internal) {
            serverIp = address.address;
            break;
          }
        }
        if (serverIp !== 'Unknown IP') break;
      }
      serverIp = serverIp === '********' ? '广州' : serverIp === '********' ? '香港' : serverIp;

      // 先生成基础日志文本（不阻塞）
      const baseLogText = `路由: ${ctx.url}，ua: ${ua}，访客者IP: ${ctx.ip}，服务器IP: ${serverIp.trim()}`;

      // 异步查询IP地理位置，不阻塞主流程
      const logTextPromise = getIPLocation(ctx.ip)
        .then((visitorLocation) => {
          return `路由: ${ctx.url}，ua: ${ua}，访客者IP: ${ctx.ip}(${visitorLocation})，服务器IP: ${serverIp.trim()}`;
        })
        .catch((error) => {
          console.error('IP地理位置查询异步处理失败:', error.message);
          return baseLogText; // 失败时使用基础日志
        });
      // 路由白名单，正则匹配
      const excludeRoutes = [
        /^\/checkip/,
        /^\/showip/,
        /^\/xrsql/,
        /^\/querylog\?limit=\d+$/,
        /gwy/,
        /health/,
        /apsgo/,
        /xrurl/,
        /^\/getjstimu/,
        /^\/cnwebsites\?type=\d+$/,
        /bilibili/,
        /saveipranges/,
        /cnwebsites/,
        /tele\/dd/,
        /^\/$/,
        /^\/api\/deploy/, // 添加部署API白名单
        /^\/api\/logs/, // 添加日志API白名单
        /^\/logs/, // 添加日志页面白名单
      ];

      // 检查当前路由是否在白名单中
      const isExcluded = excludeRoutes.some((pattern) => pattern.test(ctx.url));

      if (!isExcluded && ctx.ip !== '127.0.0.1') {
        // 异步发送日志，不阻塞主流程
        logTextPromise
          .then(async (finalLogText) => {
            try {
              // 调用 Feishu 服务发送日志
              await ctx.service.feishu.fs3(finalLogText);
              if (
                ctx.url === '/MSXzanaUZdwJUZqNbNwrDgVgVivDHNjyVotopTTcSRDGgkEDxDnSHkLZJkPX' ||
                /^\/cconf\?type=.+$/.test(ctx.url) ||
                ctx.url.match(/^\/cconf\?type=.+$/)
              ) {
                await ctx.service.feishu.fs(finalLogText);
              }
            } catch (error) {
              console.error('异步发送日志失败:', error.message);
            }
          })
          .catch((error) => {
            console.error('日志处理异步流程失败:', error.message);
          });
      }
    } catch (err) {
      console.error('Logger middleware error:', err.message);
    }

    await next();
  };
};
