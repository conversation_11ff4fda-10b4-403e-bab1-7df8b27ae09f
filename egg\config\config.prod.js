/* eslint valid-jsdoc: "off" */

'use strict';

/**
 * 生产环境配置 - 优化性能和稳定性
 * @param {Egg.EggAppInfo} appInfo app info
 */

const path = require('path');

module.exports = (appInfo) => {
  const config = (exports = {});

  // 使用统一配置模块
  const { getProductionMySQLConfig } = require('./database');
  const { getProductionRedisConfig } = require('./redis');

  // 生产环境：启用所有中间件
  config.middleware = ['robot', 'logger', 'productionMonitor'];

  // 生产环境监控中间件配置
  config.productionMonitor = {
    enabled: true,
    collectMetrics: true,
    metricsInterval: 60000, // 1分钟收集一次指标
    slowRequestThreshold: 1000, // 慢请求阈值1秒
    memoryLeakDetection: true,
    maxMemoryUsage: totalMemoryGB * 0.8 * 1024 * 1024 * 1024, // 80%内存限制
    maxCpuUsage: 0.9, // 90%CPU使用率限制
  };

  // 生产环境：启用定时任务
  config.schedule = {
    disable: false, // 生产环境启用定时任务
  };

  // 数据库配置 - 生产环境优化
  exports.mysql = getProductionMySQLConfig();

  // Redis配置 - 生产环境优化
  exports.redis = getProductionRedisConfig();

  // 生产环境：日志配置优化
  exports.logger = {
    level: 'INFO',
    consoleLevel: 'INFO',
    disableConsoleAfterReady: true, // 生产环境启动后禁用控制台输出
    dir: path.join(appInfo.root, 'logs'),
    // 生产环境日志优化
    buffer: true, // 启用日志缓冲
    encoding: 'utf8',
    // 错误日志配置
    errorLogger: {
      level: 'ERROR',
    },
    // 核心日志配置
    coreLogger: {
      level: 'INFO',
    },
  };

  // 生产环境：CORS配置
  exports.cors = {
    origin: function (ctx) {
      // 生产环境限制CORS来源
      const allowedOrigins = ['https://yourdomain.com', 'https://www.yourdomain.com'];
      return allowedOrigins.includes(ctx.get('origin')) ? ctx.get('origin') : false;
    },
    allowMethods: 'GET,HEAD,PUT,POST,DELETE,PATCH',
    credentials: true, // 生产环境启用凭证
  };

  // 生产环境：Socket.IO配置
  config.io = {
    init: {
      cors: {
        origin: function (origin, callback) {
          // 生产环境CORS验证
          const allowedOrigins = ['https://yourdomain.com', 'https://www.yourdomain.com'];
          if (!origin || allowedOrigins.includes(origin)) {
            callback(null, true);
          } else {
            callback(new Error('Not allowed by CORS'));
          }
        },
        methods: ['GET', 'POST'],
        credentials: true,
      },
    },
    // 生产环境namespace配置
    namespace: {
      '/test': {
        connectionMiddleware: [],
        packetMiddleware: [],
      },
      '/canvas': {
        connectionMiddleware: [], // 🎨 画布实时传输命名空间
        packetMiddleware: [],
      },
    },
  };

  // 生产环境：视图引擎配置
  config.view = {
    defaultViewEngine: 'nunjucks',
    mapping: {
      html: 'nunjucks',
    },
    cache: true, // 生产环境启用缓存
  };

  // 生产环境：安全配置
  config.security = {
    csrf: {
      enable: true, // 生产环境启用CSRF保护
    },
    domainWhiteList: ['yourdomain.com', 'www.yourdomain.com'],
  };

  // 生产环境：智能集群配置
  const os = require('os');
  const cpuCount = os.cpus().length;
  const totalMemory = os.totalmem();
  const totalMemoryGB = Math.round(totalMemory / 1024 / 1024 / 1024);

  // 根据硬件配置智能计算worker数量
  let optimalWorkers;
  if (totalMemoryGB >= 8) {
    optimalWorkers = Math.min(cpuCount, 8); // 内存充足，最多8个worker
  } else if (totalMemoryGB >= 4) {
    optimalWorkers = Math.min(cpuCount, 4); // 中等内存，最多4个worker
  } else {
    optimalWorkers = Math.min(cpuCount, 2); // 内存较少，最多2个worker
  }

  config.cluster = {
    listen: {
      port: 7001,
      hostname: '0.0.0.0',
    },
    workers: optimalWorkers,
    // 进程管理优化
    sticky: true, // 启用sticky sessions
    startTimeout: 60000, // 启动超时60秒
    workerStartTimeout: 30000, // worker启动超时30秒
    killTimeout: 5000, // 强制杀死前等待5秒
    // 进程重启配置
    refork: true,
    reforkLimit: 10, // 重启次数限制
    reforkInterval: 60000, // 重启间隔1分钟
  };

  // 生产环境：服务器配置
  config.server = {
    location: 'prod',
    enableSchedule: true,
    features: {
      aiTask: true,
      dataSync: true,
      monitoring: true,
    },
  };

  // 生产环境：资源限制和监控
  config.resourceLimits = {
    maxMemoryUsage: totalMemoryGB * 0.8 * 1024 * 1024 * 1024, // 80%内存限制
    maxCpuUsage: 0.9, // 90%CPU使用率限制
    maxConnections: 10000, // 最大连接数
    requestTimeout: 30000, // 请求超时30秒
  };

  // 生产环境：健康检查配置
  config.healthCheck = {
    enabled: true,
    interval: 30000, // 30秒检查一次
    timeout: 5000, // 5秒超时
    endpoints: [
      { path: '/', method: 'GET' },
      { path: '/cluster-stats', method: 'GET' },
    ],
    thresholds: {
      memoryUsage: 0.8,
      responseTime: 2000,
      errorRate: 0.05,
    },
  };

  // 生产环境：优雅关闭配置
  config.gracefulShutdown = {
    enabled: true,
    timeout: 30000, // 30秒优雅关闭超时
    signals: ['SIGTERM', 'SIGINT'],
    beforeClose: ['closeDatabase', 'closeRedis', 'finishPendingRequests'],
  };

  // 生产环境：错误处理和监控
  config.errorHandling = {
    captureUncaughtException: true,
    captureUnhandledRejection: true,
    exitOnError: false, // 生产环境不因错误退出
    errorReporting: {
      enabled: true,
      maxErrors: 100, // 最多记录100个错误
      errorLogPath: 'logs/errors.log',
    },
  };

  // 生产环境：性能监控
  config.performanceMonitoring = {
    enabled: true,
    collectMetrics: true,
    metricsInterval: 60000, // 1分钟收集一次指标
    slowRequestThreshold: 1000, // 慢请求阈值1秒
    memoryLeakDetection: true,
  };

  return {
    ...config,
  };
};
