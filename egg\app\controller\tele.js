const Controller = require('egg').Controller;
const { log } = require('console');
const fs = require('fs');
const axios = require('axios');
// const helper = require('../extend/helper');
const FormData = require('form-data');

class TeleController extends Controller {
  async cong() {
    const ctx = this.ctx;
    let text = ctx.query.text;
    ctx.body = await ctx.service.tele.push(text);
  }

  async dy() {
    const ctx = this.ctx;
    let text = ctx.query.text;
    ctx.body = await ctx.service.tele.push(text, 2);
  }

  async dy1() {
    const ctx = this.ctx;
    let text = ctx.query.text;
    ctx.body = await ctx.service.tele.push(text, 3);
  }

  async dy3() {
    const ctx = this.ctx;
    let file = ctx.request.files[0];
    // let filedata = fs.readFileSync(file.filepath);
    ctx.body = await ctx.service.tele.photo(file.filepath);
  }

  async dy4() {
    const ctx = this.ctx;
    // let text = ctx.query.text;
    // let file = ctx.request;
    let file = ctx.request.files[0];
    let filedata = fs.readFileSync(file.filepath);
    // ctx.body = await ctx.service.tele.photo(filedata,3);
    ctx.body = file;
  }

  async upload() {
    const ctx = this.ctx;
    let file = ctx.request.files[0];
    ctx.body = await ctx.service.tele.upload(file.filepath);
  }

  async upload1() {
    const ctx = this.ctx;
    await ctx.render('tele/upload.html');
  }
}

module.exports = TeleController;
