#!/usr/bin/env node

// 测试关闭消息格式

function formatUptime(seconds) {
  // 使用更精确的时间计算
  const YEAR_SECONDS = 365.25 * 24 * 3600; // 考虑闰年
  const MONTH_SECONDS = 30.44 * 24 * 3600; // 平均月份天数
  const DAY_SECONDS = 24 * 3600;
  const HOUR_SECONDS = 3600;
  const MINUTE_SECONDS = 60;

  let remaining = seconds;
  
  const years = Math.floor(remaining / YEAR_SECONDS);
  remaining = remaining % YEAR_SECONDS;
  
  const months = Math.floor(remaining / MONTH_SECONDS);
  remaining = remaining % MONTH_SECONDS;
  
  const days = Math.floor(remaining / DAY_SECONDS);
  remaining = remaining % DAY_SECONDS;
  
  const hours = Math.floor(remaining / HOUR_SECONDS);
  remaining = remaining % HOUR_SECONDS;
  
  const minutes = Math.floor(remaining / MINUTE_SECONDS);
  const secs = Math.floor(remaining % MINUTE_SECONDS);

  const parts = [];
  if (years > 0) parts.push(`${years}年`);
  if (months > 0) parts.push(`${months}月`);
  if (days > 0) parts.push(`${days}日`);
  if (hours > 0) parts.push(`${hours}小时`);
  if (minutes > 0) parts.push(`${minutes}分钟`);
  if (secs > 0 || parts.length === 0) parts.push(`${secs}秒`);

  return parts.join('');
}

// 模拟不同运行时长的关闭消息
function generateShutdownMessage(uptimeSeconds) {
  const formattedUptime = formatUptime(uptimeSeconds);
  
  return `🛑 Egg应用正在关闭
⏰ 关闭时间: ${new Date().toLocaleString('zh-CN', { timeZone: 'Asia/Shanghai' })}
💻 进程ID: ${process.pid}
📊 运行时长: ${formattedUptime}
👋 应用即将停止服务`;
}

console.log('📱 关闭消息格式预览\n');

// 不同运行时长的示例
const examples = [
  { name: '刚启动就关闭', seconds: 30 },
  { name: '运行几分钟', seconds: 300 },
  { name: '运行1小时', seconds: 3600 },
  { name: '运行半天', seconds: 43200 },
  { name: '运行1天', seconds: 86400 },
  { name: '运行1周', seconds: 604800 },
  { name: '运行1个月', seconds: 2629800 },
  { name: '运行1年', seconds: 31557600 },
  { name: '长期运行', seconds: 34214461 },
];

examples.forEach((example, index) => {
  console.log(`${index + 1}. ${example.name}:`);
  console.log('─'.repeat(50));
  console.log(generateShutdownMessage(example.seconds));
  console.log('');
});

console.log('🎯 实际效果说明:');
console.log('- 这些消息会发送到飞书群');
console.log('- 只在生产环境 (env === "prod") 时发送');
console.log('- 包含详细的运行时长信息');
console.log('- 使用中国时区显示关闭时间');

console.log('\n✨ 优化后的特点:');
console.log('- 📅 年月日小时分秒格式');
console.log('- 🎯 精确的时间计算 (考虑闰年)');
console.log('- 📱 美观的消息格式');
console.log('- 🌏 本地化时间显示');
