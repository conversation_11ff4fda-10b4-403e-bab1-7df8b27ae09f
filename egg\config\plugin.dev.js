'use strict';

/**
 * 开发环境插件配置 - 优化启动速度
 * @type Egg.EggPlugin
 */
module.exports = {
  // 核心插件 - 必须启用
  nunjucks: {
    enable: true,
    package: 'egg-view-nunjucks',
  },

  // 数据库插件 - 按需启用
  mysql: {
    enable: true,
    package: 'egg-mysql',
    env: ['local', 'dev'],
  },

  redis: {
    enable: true,
    package: 'egg-redis',
    env: ['local', 'dev'],
  },

  // 开发必需插件
  cors: {
    enable: true,
    package: 'egg-cors',
  },

  // 开发环境可选插件 - 按需启用
  ejs: {
    enable: false, // 开发环境禁用，减少启动时间
    package: 'egg-view-ejs',
  },

  io: {
    enable: true, // 🎨 启用Socket.IO支持画布实时传输
    package: 'egg-socket.io',
  },
};
