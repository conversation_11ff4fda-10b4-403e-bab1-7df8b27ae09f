# PNPM 使用指南

## 🚨 重要提醒
**此项目只允许使用 pnpm 作为包管理器！**
**禁止使用 npm 和 yarn！**

## 为什么使用 pnpm？
- 🚀 更快的安装速度
- 💾 节省磁盘空间（通过硬链接共享依赖）
- 🔒 更严格的依赖管理
- 🛡️ 避免幽灵依赖问题

## 常用命令对照表

| npm 命令 | yarn 命令 | pnpm 命令 | 说明 |
|---------|----------|----------|------|
| `npm install` | `yarn` | `pnpm install` | 安装所有依赖 |
| `npm install <pkg>` | `yarn add <pkg>` | `pnpm add <pkg>` | 添加依赖 |
| `npm install -D <pkg>` | `yarn add -D <pkg>` | `pnpm add -D <pkg>` | 添加开发依赖 |
| `npm uninstall <pkg>` | `yarn remove <pkg>` | `pnpm remove <pkg>` | 移除依赖 |
| `npm run <script>` | `yarn <script>` | `pnpm <script>` | 运行脚本 |
| `npm update` | `yarn upgrade` | `pnpm update` | 更新依赖 |

## 项目常用命令

```bash
# 安装依赖
pnpm install

# 启动开发服务器
pnpm dev

# 构建项目
pnpm build

# 预览构建结果
pnpm preview

# 代码格式化
pnpm lint

# 清理缓存
pnpm clean
```

## 如果你尝试使用 npm 或 yarn
系统会显示错误信息并阻止执行，请改用 pnpm 命令。

## 安装 pnpm
如果你还没有安装 pnpm，请运行：
```bash
npm install -g pnpm
```

或者使用其他安装方式：https://pnpm.io/installation
