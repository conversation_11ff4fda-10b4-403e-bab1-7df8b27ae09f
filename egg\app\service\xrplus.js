const Service = require('egg').Service;
const cheerio = require('cheerio');
const axios = require('axios');
const iconv = require('iconv-lite');
var fs = require('fs');
var path = require('path');

class Xrpluservice extends Service {
  async clist(url) {
    let data = [];
    await axios.get(url).then(function (response) {
      let odata = cheerio.load(response.data);
      odata('.i_list').each(function (index, item) {
        let fm = odata(item).find('img').attr('src');
        let x = odata(item).children('a').attr('href');
        let v = {};
        v.url = '/xrnei?url=https://www.xr05.xyz' + x;
        v.img = 'https://re.101616.xyz/https://www.xr05.xyz' + fm;
        data.push(v);
      });

      return data;
    });
    return data;
  }

  async cclist(page) {
    page = page ?? 1;
    let pagesize = 10;
    let sql = `select * from xr order by xrid desc limit ${
      pagesize * (page - 1)
    },${pagesize}`;
    return await this.app.mysql.query(sql);
  }

  async xrpluspageinfo(id) {
    let sql = `select * from xrinfo where xrid=` + id;
    return await this.app.mysql.query(sql);
  }

  async query(query) {
    return await this.app.mysql.query(query);
  }

  async sou(url) {
    const { ctx, app } = this;
    console.log(url);
    let data = [];
    let baseurl = await app.redis.get('xrurl');
    let res = await axios.get(url).then(async function (response) {
      let odata = cheerio.load(response.data);
      odata('.sousuo').each(async function (index, item) {
        // let fm = odata(item).find("img").attr('src');
        let x = odata(item).children('a').attr('href');
        let path = x.split('/')[2].split('.');
        // let x = odata(item).attr('value');
        // console.log(path);
        let v = {};
        v.id = path[0];
        v.url = '/xrplusnei?id=' + path[0];
        v.img =
          'https://re.101616.xyz/' +
          baseurl +
          +'/uploadfile/pic/' +
          path[0] +
          '.jpg';
        data.push(v);
      });
      return data;
    });
    return data;
  }

  async page(url) {
    let data = [];
    let res = await axios.get(url).then(function (response) {
      let odata = cheerio.load(response.data);

      odata('.page>a').each(function (index, item) {
        let imgurl = 'https://www.xr05.xyz' + odata(item).attr('href');
        data.push(imgurl);
      });

      // return odata('.content_left' ).eq(0).text();
      return data;
    });
    return data
      .filter(function (c, index) {
        return data.indexOf(c) === index;
      })
      .slice(1);
  }

  async imgs(url) {
    let imgs = [];
    console.log(url);
    let res = await axios.get(url).then(function (response) {
      let odata = cheerio.load(response.data);

      odata('.content')
        .eq(1)
        .children('p')
        .children('img')
        .each(function (index, item) {
          let x = odata(item).attr('src');
          // let x = odata(item).attr('value');
          imgs.push('https://re.101616.xyz/https://www.xr05.xyz/' + x);
        });

      return imgs;
    });
    return res;
  }

  async imgs2(url) {
    let urls = [];
    const baseUrl = 'https://re.101616.xyz/https://www.xr05.xyz';
    // console.log(url);
    return await axios.get(url).then((res) => {
      const $ = cheerio.load(res.data);
      $('.content:eq(1) p img').each(function () {
        // console.log($(this).attr('src'))
        urls.push(baseUrl + $(this).attr('src'));
      });
      return urls;
    });
  }

  async img(url, panduan = true) {
    let imgs = [];
    // console.log(url);
    let res = await axios.get(url).then(function (response) {
      let odata = cheerio.load(response.data);

      odata('img').each(function (index, item) {
        let x = odata(item).attr('src');
        console.log(x);
        // let x = odata(item).attr('value');
        if (x.match(panduan)) {
          imgs.push(x);
        }
      });

      return imgs;
    });
    return res;
  }
}

module.exports = Xrpluservice;
