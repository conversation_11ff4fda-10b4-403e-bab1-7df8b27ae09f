'use strict';

const Controller = require('egg').Controller;

class DeployController extends Controller {
  // 接收部署成功通知
  async success() {
    const { ctx } = this;

    try {
      // 记录原始请求信息用于调试
      ctx.logger.info('部署成功通知请求详情:', {
        method: ctx.method,
        contentType: ctx.headers['content-type'],
        contentLength: ctx.headers['content-length'],
        userAgent: ctx.headers['user-agent'],
        ip: ctx.ip,
      });

      // 获取请求体数据（POST）或查询参数（GET）
      let deployInfo;

      if (ctx.method === 'GET') {
        deployInfo = ctx.query;
      } else {
        // POST请求，检查请求体
        deployInfo = ctx.request.body;

        // 如果解析失败，记录详细错误信息
        if (!deployInfo && ctx.request.rawBody) {
          ctx.logger.error('请求体解析失败:', {
            rawBody: ctx.request.rawBody?.toString().substring(0, 1000),
            bodyLength: ctx.request.rawBody?.length,
          });

          ctx.status = 400;
          ctx.body = {
            success: false,
            message: 'JSON格式错误或请求体解析失败',
            hint: '请检查JSON格式是否正确，特别注意引号和转义字符',
          };
          return;
        }
      }

      // 验证必要字段
      if (!deployInfo || Object.keys(deployInfo).length === 0) {
        ctx.status = 400;
        ctx.body = {
          success: false,
          message: '缺少部署信息',
          receivedData: deployInfo,
        };
        return;
      }

      // 记录部署信息
      ctx.logger.info('收到部署成功通知:', deployInfo);

      // 发送飞书通知
      const result = await ctx.service.feishu.fsDeploySuccess(deployInfo);

      ctx.body = {
        success: true,
        message: '部署成功通知已发送',
        data: result,
      };
    } catch (error) {
      ctx.logger.error('发送部署成功通知失败:', error);
      ctx.status = 500;
      ctx.body = {
        success: false,
        message: '发送通知失败',
        error: error.message,
      };
    }
  }

  // 接收部署失败通知
  async failure() {
    const { ctx } = this;

    try {
      const deployInfo = ctx.request.body;

      if (!deployInfo) {
        ctx.status = 400;
        ctx.body = {
          success: false,
          message: '缺少部署信息',
        };
        return;
      }

      ctx.logger.error('收到部署失败通知:', deployInfo);

      // 发送飞书通知
      const result = await ctx.service.feishu.fsDeployFailure(deployInfo);

      ctx.body = {
        success: true,
        message: '部署失败通知已发送',
        data: result,
      };
    } catch (error) {
      ctx.logger.error('发送部署失败通知失败:', error);
      ctx.status = 500;
      ctx.body = {
        success: false,
        message: '发送通知失败',
        error: error.message,
      };
    }
  }

  // 测试部署通知
  async test() {
    const { ctx } = this;

    try {
      // 模拟部署成功信息
      const mockDeployInfo = {
        projectName: 'Vue前端项目',
        environment: 'production',
        version: 'v1.2.3',
        branch: 'main',
        commitHash: 'a1b2c3d4e5f6789012345678901234567890abcd',
        commitMessage: 'feat: 添加新功能和优化性能',
        deployTime: new Date().toLocaleString('zh-CN', {
          timeZone: 'Asia/Shanghai',
        }),
        deployDuration: '2分钟30秒',
        deployedBy: 'GitHub Actions',
        serverUrl: 'https://your-domain.com',
        buildSize: '2.5MB',
        features: [
          '新增用户管理功能',
          '优化页面加载速度',
          '修复已知bug',
          '更新UI组件库',
        ],
        notes: '本次部署包含重要功能更新，请及时测试验证',
      };

      const result = await ctx.service.feishu.fsDeploySuccess(mockDeployInfo);

      ctx.body = {
        success: true,
        message: '测试部署通知已发送',
        data: result,
        mockData: mockDeployInfo,
      };
    } catch (error) {
      ctx.logger.error('发送测试部署通知失败:', error);
      ctx.status = 500;
      ctx.body = {
        success: false,
        message: '发送测试通知失败',
        error: error.message,
      };
    }
  }

  // 获取部署通知模板
  async template() {
    const { ctx } = this;

    const template = {
      success: {
        url: '/api/deploy/success',
        method: 'POST',
        description: '发送部署成功通知',
        body: {
          projectName: 'Vue项目',
          environment: 'production',
          version: 'v1.0.0',
          branch: 'main',
          commitHash: 'commit hash',
          commitMessage: 'commit message',
          deployTime: '2025-07-24 10:00:00',
          deployDuration: '2分钟30秒',
          deployedBy: 'GitHub Actions',
          serverUrl: 'https://your-domain.com',
          buildSize: '2.5MB',
          features: ['新功能1', '新功能2'],
          notes: '备注信息',
        },
      },
      failure: {
        url: '/api/deploy/failure',
        method: 'POST',
        description: '发送部署失败通知',
        body: {
          projectName: 'Vue项目',
          environment: 'production',
          branch: 'main',
          commitHash: 'commit hash',
          failureTime: '2025-07-24 10:00:00',
          errorMessage: '错误信息',
          deployedBy: 'GitHub Actions',
          logUrl: 'https://github.com/actions/runs/xxx',
          retryCount: 1,
        },
      },
    };

    ctx.body = {
      success: true,
      message: '部署通知API模板',
      data: template,
    };
  }
}

module.exports = DeployController;
