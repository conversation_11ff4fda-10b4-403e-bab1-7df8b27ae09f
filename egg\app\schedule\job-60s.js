const axios = require('axios');

module.exports = {
  schedule: {
    interval: '60s', // 60秒执行一次
    type: 'all', // 指定所有的 worker 都需要执行
    env: ['prod'], // 只在生产环境执行
    immediate: false, // 应用启动后不立即执行
    enabled: false,
  },

  async task(ctx) {
    const startTime = Date.now();
    ctx.logger.info('=== 开始执行60秒定时任务 ===');

    try {
      // 从配置文件获取服务器信息
      const config = ctx.app.config;
      const serverConfig = config.server || {};

      // 检查是否启用定时任务
      if (!serverConfig.enableSchedule) {
        ctx.logger.info('60s任务：定时任务未启用，跳过执行');
        return;
      }

      // APSGO相关任务
      if (serverConfig.features?.monitoring) {
        ctx.logger.info('60s任务：开始执行APSGO监控任务');

        // 取消注释以启用实际任务
        // await axios.get('http://127.0.0.1:7001/apsgo');

        ctx.logger.info('60s任务：APSGO监控任务执行完成');
      }

      // 其他60秒周期任务可以在这里添加
      // 例如：健康检查、状态监控等

      const duration = Date.now() - startTime;
      ctx.logger.info(`=== 60秒定时任务执行完成，耗时: ${duration}ms ===`);
    } catch (error) {
      const duration = Date.now() - startTime;
      ctx.logger.error(`60s任务执行失败，耗时: ${duration}ms`, error);

      // 可以发送告警通知
      // await ctx.service.feishu.fs('60秒定时任务执行异常: ' + error.message);
    }
  },
};
