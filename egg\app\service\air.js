const axios = require('axios');
const { getPublicip } = require('../extend/helper');
const Service = require('egg').Service;

class AirService extends Service {
  async sda(from, to, departureDate, returnDate) {
    {
      const { ctx, app } = this;
      try {
        const options = {
          method: 'GET',
          url: 'https://flights.sda.cn/tRtApi/flightCache/calendarSearchCust',
          params: {
            from: from,
            fromType: 'AIRPORT',
            to: to,
            toType: 'AIRPORT',
            departureDate: departureDate,
            returnDate: returnDate,
            minDiscountLevel: '0',
          },
          headers: {
            cookie: 'ROUTEAPIID=.tRetailgatewayServer9',
            'User-Agent':
              'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36',
            Referer: 'https://flights.sda.cn/flight/search/hfe-xmn-231006-1',
            'Accept-Language': 'zh-CN',
          },
        };
        const response = await axios.request(options);
        return response.data;
      } catch (error) {
        console.log(error);
        // 处理错误，可能发送错误消息或采取其他适当的措施
      }
    }
  }

  async xm(from, to, startDate, endDate, title, rediskey) {
    const { ctx, app } = this;
    try {
      const options = {
        method: 'POST',
        url: 'https://csapi.xiamenair.com/offer/api/v1/offer/priceCalendar',
        headers: {
          'Content-Type': 'application/json',
          'User-Agent':
            'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36',
          Channel: 'PCWEB',
        },
        data: {
          shoppingPreference: { priceCalendarOnly: true, pricingMode: 'Money' },
          itineraries: [
            {
              startDate: startDate,
              endDate: endDate,
              origin: { airport: { code: from } },
              destination: { airport: { code: to } },
            },
          ],
        },
      };
      const response = await axios.request(options);
      let data = response.data;
      let list = [];
      let text = '';
      text = text + title;
      let clist = data.result.calendarPrice;
      for (let i in clist) {
        const item = clist[i];
        let obj = {};
        obj.date = i;
        obj.price = item.amount;
        const oldprice = (await app.redis.get(rediskey + obj.date)) / 1;
        list.push(obj);
        text = text + `${obj.date} ${obj.price}\n`;
        if (oldprice !== obj.price) {
          text = text + `价格改动，旧价格：${oldprice}，新价格：${obj.price}\n`;
        }
        await app.redis.set(rediskey + obj.date, obj.price / 1);
      }
      if (text.match('价格改动')) {
        await ctx.service.feishu.fs(text);
      }
      return list;
    } catch (error) {
      console.log(error);
      // 处理错误，可能发送错误消息或采取其他适当的措施
    }
  }

  async ly1(from, to, DepartureDate, title, rediskey) {
    const { ctx, app } = this;
    try {
      const options = {
        method: 'POST',
        url: 'https://www.ly.com/flights/api/getflightlist',
        headers: {
          'Accept-Encoding': 'gzip, deflate, br',
          'Accept-Language': 'zh-CN,zh;q=0.9',
          Connection: 'keep-alive',
          'Content-Type': 'application/json;charset=UTF-8',
          DNT: 1,
          Host: 'www.ly.com',
          Origin: 'https://www.ly.com',
          'Sec-Fetch-Dest': 'empty',
          'Sec-Fetch-Mode': 'cors',
          'Sec-Fetch-Site': 'same-origin',
          'User-Agent':
            'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36',
          accept: 'application/json, text/plain, */*',
          refid: 0,
          tcplat: 1,
        },
        data: {
          Departure: from,
          Arrival: to,
          GetType: '1',
          QueryType: '1',
          fromairport: '',
          toairport: '',
          DepartureDate: DepartureDate,
          IsBaby: 0,
          paging: {
            dataflag: 'all',
          },
          DepartureFilter: '',
          ArrivalFilter: '',
          flat: 1,
          plat: 1,
          isFromKylin: 1,
          refid: '0',
        },
      };
      const response = await axios.request(options);
      let data = response.data;
      let list = [];
      let text = '';
      text =
        text +
        title +
        'https://www.ly.com/flights/itinerary/oneway/' +
        from +
        '-' +
        to +
        '?date=' +
        DepartureDate +
        '&fromairport=&toairport=&childticket=0%2C0\n';
      for (let item of data.body.FlightInfoSimpleList) {
        let obj = {};
        obj.flightNo = item.flightNo;
        obj.airCompanyName = item.airCompanyName;
        obj.flyOffTime = item.flyOffTime;
        obj.arrivalTime = item.arrivalTime;
        obj.productPrices = item.productPrices;
        obj.flightNo = item.flightNo;
        obj.originAirportShortName = item.originAirportShortName;
        obj.arriveAirportShortName = item.arriveAirportShortName;
        list.push(obj);
        let ptext = '';
        let pricetext = '';

        for (let i in obj.productPrices) {
          pricetext = pricetext + obj.productPrices[i] + ' ';
        }
        ptext =
          obj.productPrices[0] ||
          obj.productPrices[1] ||
          obj.productPrices[2] ||
          obj.productPrices[3];
        const oldptext = await app.redis.get(rediskey + obj.flightNo);
        if (+oldptext !== +ptext && +ptext < +oldptext) {
          text =
            text +
            `${obj.airCompanyName} ${obj.flightNo}\n${obj.flyOffTime}\n${obj.arrivalTime}\n价格：${pricetext}\n\n`;
          text = text + `旧价格：${oldptext}\n新价格：${ptext}\n\n`;
        }
        if (+ptext < +oldptext || !oldptext) {
          await app.redis.set(rediskey + obj.flightNo, ptext);
        }
      }
      if (text.match('新价格')) {
        await ctx.service.feishu.fs2(text);
        // await ctx.service.wecom.zyjqywx(text);
      }
      return list;
    } catch (error) {
      console.log(error);
      // 处理错误，可能发送错误消息或采取其他适当的措施
    }
  }

  async ly(
    from,
    fromcity,
    to,
    tocity,
    DepartureDate,
    rediskey,
    robot = 2,
    lowprice = 1000,
  ) {
    const { ctx, app } = this;
    lowprice = +lowprice;
    try {
      const options = {
        method: 'POST',
        url: 'https://www.ly.com/flights/api/getflightlist',
        headers: {
          'Accept-Encoding': 'gzip, deflate, br',
          'Accept-Language': 'zh-CN,zh;q=0.9',
          Connection: 'keep-alive',
          'Content-Type': 'application/json;charset=UTF-8',
          DNT: 1,
          Host: 'www.ly.com',
          Origin: 'https://www.ly.com',
          'Sec-Fetch-Dest': 'empty',
          'Sec-Fetch-Mode': 'cors',
          'Sec-Fetch-Site': 'same-origin',
          'User-Agent':
            'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36',
          accept: 'application/json, text/plain, */*',
          refid: 0,
          tcplat: 1,
        },
        data: {
          Departure: from,
          Arrival: to,
          GetType: '1',
          QueryType: '1',
          fromairport: '',
          toairport: '',
          DepartureDate,
          IsBaby: 0,
          paging: {
            dataflag: 'all',
          },
          DepartureFilter: '',
          ArrivalFilter: '',
          flat: 1,
          plat: 1,
          isFromKylin: 1,
          refid: '0',
        },
      };

      const response = await axios.request(options);
      const data = response.data;
      const list = [];
      let text = `${fromcity}---${tocity}\n`;
      let preurl = `https://www.ly.com/flights/itinerary/oneway/${from}-${to}?date=${DepartureDate}&from=${fromcity}&to=${tocity}&fromairport=&toairport=&childticket=0,0`;
      let ctripurl = `https://flights.ctrip.com/online/list/oneway-${from.toLowerCase()}-${to.toLowerCase()}?_=1&depdate=${DepartureDate}&cabin=Y_S_C_F`;
      text += `${encodeURI(preurl)}\n${encodeURI(
        ctripurl,
      )}\n===================`;

      for (const item of data.body.FlightInfoSimpleList) {
        const productPricesArray = Object.values(item.productPrices).map(
          (price) => +price,
        );
        const obj = {
          flightNo: item.flightNo,
          airCompanyName: item.airCompanyName,
          flyOffTime: item.flyOffTime,
          arrivalTime: item.arrivalTime,
          productPrices: productPricesArray,
          originAirportShortName: item.originAirportShortName,
          arriveAirportShortName: item.arriveAirportShortName,
          atsn: item.atsn,
          boardPoint: item.boardPoint,
          otsn: item.otsn,
          offPoint: item.offPoint,
          lcd: item.lcd,
          food: item.sts && item.sts.mfg ? item.sts.mfg : '',
        };
        list.push(item);

        const oldPrice = await app.redis.get(rediskey + obj.flightNo);
        const newPrice = Math.min(...productPricesArray);
        const priceDetails = productPricesArray.join(' ');
        if (oldPrice && newPrice < lowprice && newPrice < +oldPrice) {
          text += `\n${obj.airCompanyName} ${obj.flightNo} ${obj.food}\n${obj.flyOffTime}${obj.otsn}${obj.boardPoint}\n${obj.arrivalTime}${obj.atsn}${obj.offPoint}\n旧价格：${oldPrice}\n新价格：${newPrice}\n${obj.lcd}\n===================`;
        }
        if (!oldPrice || newPrice < +oldPrice) {
          await app.redis.set(rediskey + obj.flightNo, newPrice.toString());
        }
      }

      if (text.includes('新价格')) {
        if (robot === 2) {
          await ctx.service.feishu.fs2(text);
        } else if (robot === 1) {
          await ctx.service.feishu.fs(text);
        }
        // await ctx.service.wecom.zyjqywx(text);
      }

      return list;
    } catch (error) {
      console.error('Error fetching flight data:', error);
      // Consider adding additional error handling, such as notifying an admin or retrying the request.
    }
  }

  async sdainfo(from, to, departureDate, title, rediskey) {
    const { ctx, app } = this;
    try {
      const options = {
        method: 'POST',
        url: 'https://flights.sda.cn/tRtApi/flight/resultSets',
        headers: {
          'Content-Type': 'application/json',
          'Accept-Language': 'zh-CN',
          Referer: 'https://flights.sda.cn/flight/search/xmn-lhw-230921-1',
          'User-Agent':
            'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36',
        },
        data: {
          cabinClass: 'ANY',
          currencyCode: 'CNY',
          bounds: [
            {
              departureDate: departureDate,
              destination: {
                code: 'AIR_' + to + '_CN',
                context: 'LOCATION_ID',
                locationType: 'AIRPORT',
              },
              origin: {
                code: 'AIR_' + from + '_CN',
                context: 'LOCATION_ID',
                locationType: 'AIRPORT',
              },
            },
          ],
          passengerCounts: [
            {
              count: 1,
              passengerType: 'ADT',
            },
          ],
        },
      };
      const response = await axios.request(options);
      let uuid = response.data.id;
      const info = await axios.get(
        'https://flights.sda.cn/tRtApi/flight/resultSets/' + uuid,
        {
          headers: {
            cookie: 'ROUTEAPIID=.tRetailgatewayServer9',
            'User-Agent':
              'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36',
            'Accept-Language': 'zh-CN',
            Referer: 'https://flights.sda.cn/flight/search/xmn-lhw-230921-1',
          },
        },
      );
      let data = info.data;
      const newprice = data.flightOptions[0].prices[0].total.amount / 1;
      const oldprice = (await app.redis.get(rediskey)) / 1;
      let text = '';
      text = text + title;
      if (newprice !== oldprice) {
        text = text + `旧价格：${oldprice}\n新价格：${newprice}\n\n`;
        await ctx.service.feishu.fs(text);
      }
      await app.redis.set(rediskey, newprice);

      return data.flightOptions[0].prices[0].total;
    } catch (e) {
      console.log(e);
    }
  }
}

module.exports = AirService;
