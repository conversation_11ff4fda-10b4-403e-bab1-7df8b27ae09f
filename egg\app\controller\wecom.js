const Controller = require('egg').Controller;
const { log } = require('console');
const fs = require('fs');
const axios = require('axios');
// const helper = require('../extend/helper');
const FormData = require('form-data');

class WecomController extends Controller {
  async qywx() {
    const ctx = this.ctx;
    let text = ctx.query.text;
    ctx.body = await ctx.service.wecom.qywx(text);
  }

  async zyjqywx() {
    const ctx = this.ctx;
    let text = ctx.query.text;
    ctx.body = await ctx.service.wecom.zyjqywx(text);
  }

  async qywximg() {
    const { ctx } = this;
    const file = ctx.request.files[0];
    ctx.body = await ctx.service.wecom.qywximg(file.filepath);
  }
}

module.exports = WecomController;
