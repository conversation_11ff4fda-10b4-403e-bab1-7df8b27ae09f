<html>
<head>
    <meta charset="UTF-8"/>
    <meta name="viewport" content="width=device-width,initial-scale=1.0"/>
    <script src="/public/vue/vue.min.js"></script>
    <script src="/public/element/index.js"></script>
    <link rel="stylesheet" href="/public/element/index.css">
    <script src="/public/js/axios.min.js"></script>

    <title>Element Plus demo</title>
</head>
<body>
<div>
</div>
<div id="app">
    <el-input
            placeholder="请输入内容"
            v-model="input"
            @input="getData"
            clearable>
    </el-input>
    <div v-for="item in tableData">
        <div v-html="item.content">
        </div>
        <div>
            <p v-html="item.A"></p>
            <p v-html="item.B"></p>
            <p v-html="item.C"></p>
            <p v-html="item.D"></p>
        </div>
    </div>
</div>

<script>
    new Vue({
        el: '#app',
        data: function () {
            return {
                tableData: null,
                link: "wcnm",
                input: ''
            }
        },
        created() {

        },
        mounted() {   //自动触发写入的函数
            this.getData();
        },
        methods: {
            handleSelect(item) {
                console.log(item);
            },
            formatter(row, column) {
                return (row.shtg + row.dsh) / 1;
                //:formatter="formatter"
            },
            formatterdate(row, column) {
                if (row.uptime) {
                    let DateString = row.uptime;
                    let date = new Date(DateString);
                    let year = date.getFullYear();
                    let month = date.getMonth() + 1;
                    let day = date.getDate();
                    let Hours = date.getHours();
                    let Minutes = date.getMinutes();
                    let Seconds = date.getSeconds();
                    if (month < 10) {
                        month = '0' + month;
                    }
                    if (day < 10) {
                        day = '0' + day;
                    }
                    let s_createtime = year + '-' + month + '-' + day + ' ' + Hours + ':' + Minutes + ':' + Seconds;
                    return s_createtime;
                }
            },
            tableRowClassName({row, rowIndex}) {
                if (row.id === 110401) {
                    return 'success-row';
                } else if (row.id === 101402
                ) {
                    return 'warning-row';
                }
                return '';
            },
            getData(item) {
                // console.log(item);
                axios.get('/cha?timu='+item).then(res => {
                    this.tableData = res.data;
                    console.log(this.tableData);
                })
            }
        }
    })


</script>

<style>
    .el-table .warning-row {
        background: oldlace;
    }

    .el-table .success-row {
        background: #f0f9eb;
    }
</style>
</body>
</html>
