#!/bin/bash

# 快速部署脚本 - 集成强力清理功能
set -e

# 颜色定义
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[0;33m'
BLUE='\033[0;34m'
NC='\033[0m'

# 日志函数
log() {
    echo -e "$@"
}

# 错误处理函数
handle_error() {
    log "${RED}❌ 部署失败: $1${NC}"
    exit 1
}

log "${BLUE}🚀 开始快速部署...${NC}"

# 进入项目目录
cd /home/<USER>"无法进入项目目录"

# 拉取最新代码
log "${YELLOW}📥 拉取最新代码...${NC}"
git pull origin main || handle_error "代码拉取失败"

# 智能依赖安装
if [ package.json -nt node_modules/.package-lock.json ] 2>/dev/null || [ ! -d node_modules ]; then
    log "${YELLOW}📦 安装依赖...${NC}"
    pnpm install --production || handle_error "依赖安装失败"
else
    log "${GREEN}📦 依赖无变化，跳过安装${NC}"
fi

# 使用clear_eggjs进行强力清理
log "${YELLOW}🧹 使用强力清理脚本...${NC}"
if [ -f "./clear_eggjs" ]; then
    chmod +x ./clear_eggjs
    ./clear_eggjs --force --quiet || log "${YELLOW}⚠️ 清理脚本执行完成${NC}"
else
    log "${YELLOW}⚠️ 未找到clear_eggjs脚本，使用备用清理方法...${NC}"
    
    # 备用清理方法
    log "${YELLOW}🛑 停止现有应用...${NC}"
    pnpm exec egg-scripts stop --title=egg 2>/dev/null || true
    
    # 强制清理所有相关进程
    log "${YELLOW}🧹 清理残留进程...${NC}"
    pkill -f "egg" 2>/dev/null || true
    pkill -f "node.*egg" 2>/dev/null || true
    pkill -f "egg-scripts" 2>/dev/null || true
    pkill -f "egg-cluster" 2>/dev/null || true
    pkill -f "egg-server" 2>/dev/null || true
    
    # 清理7001端口占用
    PORT_PIDS=$(lsof -t -i:7001 2>/dev/null || true)
    if [ -n "$PORT_PIDS" ]; then
        log "${YELLOW}🔌 清理7001端口占用进程: $PORT_PIDS${NC}"
        kill -9 $PORT_PIDS 2>/dev/null || true
    fi
    
    sleep 3
fi

# 验证清理结果
log "${YELLOW}🔍 验证清理结果...${NC}"
REMAINING_EGG=$(pgrep -f 'egg' 2>/dev/null | wc -l)
REMAINING_PORT=$(lsof -t -i:7001 2>/dev/null | wc -l)

if [ $REMAINING_EGG -gt 0 ]; then
    log "${YELLOW}⚠️ 仍有 $REMAINING_EGG 个egg相关进程在运行${NC}"
fi

if [ $REMAINING_PORT -gt 0 ]; then
    log "${YELLOW}⚠️ 端口7001仍被占用${NC}"
fi

# 启动应用
log "${BLUE}🚀 启动应用...${NC}"
pnpm start || handle_error "应用启动失败"

# 等待应用启动
log "${YELLOW}⏳ 等待应用启动...${NC}"
sleep 5

# 检查应用状态
if curl -s http://localhost:7001/health >/dev/null 2>&1; then
    log "${GREEN}✅ 应用启动成功！健康检查通过${NC}"
elif curl -s http://localhost:7001 >/dev/null 2>&1; then
    log "${GREEN}✅ 应用启动成功！服务可访问${NC}"
else
    log "${YELLOW}⚠️ 应用可能仍在启动中，请稍后检查${NC}"
fi

log "${GREEN}✅ 快速部署完成！${NC}"
log "${BLUE}🌐 访问地址: http://localhost:7001${NC}"
