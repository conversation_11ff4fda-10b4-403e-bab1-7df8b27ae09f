'use strict';

/**
 * 健康检查中间件
 * 用于监控应用状态，帮助排查掉线问题
 */

const os = require('os');
const fs = require('fs');

module.exports = (options = {}) => {
  return async function healthCheck(ctx, next) {
    // 只处理健康检查路径
    if (ctx.path !== '/health') {
      return await next();
    }

    const startTime = Date.now();
    const health = {
      status: 'ok',
      timestamp: new Date().toISOString(),
      uptime: process.uptime(),
      checks: {},
      system: {},
      application: {},
    };

    try {
      // 1. 系统资源检查
      health.system = await checkSystemResources();
      
      // 2. 数据库连接检查
      health.checks.mysql = await checkMysqlConnection(ctx);
      health.checks.redis = await checkRedisConnection(ctx);
      
      // 3. 应用状态检查
      health.application = await checkApplicationStatus(ctx);
      
      // 4. 磁盘空间检查
      health.checks.disk = await checkDiskSpace();
      
      // 5. 内存检查
      health.checks.memory = checkMemoryUsage();
      
      // 计算响应时间
      health.responseTime = Date.now() - startTime;
      
      // 判断整体健康状态
      const isHealthy = Object.values(health.checks).every(check => check.status === 'ok');
      health.status = isHealthy ? 'ok' : 'error';
      
      // 设置HTTP状态码
      ctx.status = isHealthy ? 200 : 503;
      ctx.body = health;
      
      // 记录健康检查日志
      if (!isHealthy) {
        ctx.logger.warn('Health check failed:', health);
      }
      
    } catch (error) {
      ctx.logger.error('Health check error:', error);
      ctx.status = 503;
      ctx.body = {
        status: 'error',
        error: error.message,
        timestamp: new Date().toISOString(),
      };
    }
  };
};

// 检查系统资源
async function checkSystemResources() {
  const cpus = os.cpus();
  const loadAvg = os.loadavg();
  
  return {
    platform: os.platform(),
    arch: os.arch(),
    cpuCount: cpus.length,
    loadAverage: {
      '1min': loadAvg[0],
      '5min': loadAvg[1],
      '15min': loadAvg[2],
    },
    memory: {
      total: Math.round(os.totalmem() / 1024 / 1024), // MB
      free: Math.round(os.freemem() / 1024 / 1024), // MB
      used: Math.round((os.totalmem() - os.freemem()) / 1024 / 1024), // MB
      usage: Math.round(((os.totalmem() - os.freemem()) / os.totalmem()) * 100), // %
    },
  };
}

// 检查MySQL连接
async function checkMysqlConnection(ctx) {
  try {
    if (!ctx.app.mysql) {
      return { status: 'disabled', message: 'MySQL not configured' };
    }
    
    const result = await ctx.app.mysql.query('SELECT 1 as test');
    return {
      status: 'ok',
      message: 'MySQL connection successful',
      responseTime: Date.now(),
    };
  } catch (error) {
    return {
      status: 'error',
      message: `MySQL connection failed: ${error.message}`,
      error: error.code,
    };
  }
}

// 检查Redis连接
async function checkRedisConnection(ctx) {
  try {
    if (!ctx.app.redis) {
      return { status: 'disabled', message: 'Redis not configured' };
    }
    
    const startTime = Date.now();
    await ctx.app.redis.ping();
    const responseTime = Date.now() - startTime;
    
    return {
      status: 'ok',
      message: 'Redis connection successful',
      responseTime,
    };
  } catch (error) {
    return {
      status: 'error',
      message: `Redis connection failed: ${error.message}`,
      error: error.code,
    };
  }
}

// 检查应用状态
async function checkApplicationStatus(ctx) {
  const memUsage = process.memoryUsage();
  
  return {
    pid: process.pid,
    nodeVersion: process.version,
    uptime: process.uptime(),
    memory: {
      rss: Math.round(memUsage.rss / 1024 / 1024), // MB
      heapTotal: Math.round(memUsage.heapTotal / 1024 / 1024), // MB
      heapUsed: Math.round(memUsage.heapUsed / 1024 / 1024), // MB
      external: Math.round(memUsage.external / 1024 / 1024), // MB
      heapUsage: Math.round((memUsage.heapUsed / memUsage.heapTotal) * 100), // %
    },
    env: process.env.NODE_ENV,
    cwd: process.cwd(),
  };
}

// 检查磁盘空间
async function checkDiskSpace() {
  try {
    const stats = await fs.promises.statvfs ? 
      fs.promises.statvfs('/') : 
      { bavail: 0, blocks: 1, frsize: 1 }; // fallback
    
    const total = stats.blocks * stats.frsize;
    const free = stats.bavail * stats.frsize;
    const used = total - free;
    const usage = Math.round((used / total) * 100);
    
    return {
      status: usage > 90 ? 'warning' : 'ok',
      total: Math.round(total / 1024 / 1024 / 1024), // GB
      free: Math.round(free / 1024 / 1024 / 1024), // GB
      used: Math.round(used / 1024 / 1024 / 1024), // GB
      usage, // %
      message: usage > 90 ? 'Disk space low' : 'Disk space sufficient',
    };
  } catch (error) {
    return {
      status: 'error',
      message: `Disk check failed: ${error.message}`,
    };
  }
}

// 检查内存使用
function checkMemoryUsage() {
  const memUsage = process.memoryUsage();
  const systemMem = {
    total: os.totalmem(),
    free: os.freemem(),
  };
  
  const heapUsagePercent = (memUsage.heapUsed / memUsage.heapTotal) * 100;
  const systemUsagePercent = ((systemMem.total - systemMem.free) / systemMem.total) * 100;
  
  let status = 'ok';
  let message = 'Memory usage normal';
  
  if (heapUsagePercent > 90) {
    status = 'error';
    message = 'Heap memory usage critical';
  } else if (heapUsagePercent > 80) {
    status = 'warning';
    message = 'Heap memory usage high';
  } else if (systemUsagePercent > 90) {
    status = 'error';
    message = 'System memory usage critical';
  } else if (systemUsagePercent > 80) {
    status = 'warning';
    message = 'System memory usage high';
  }
  
  return {
    status,
    message,
    heap: {
      used: Math.round(memUsage.heapUsed / 1024 / 1024), // MB
      total: Math.round(memUsage.heapTotal / 1024 / 1024), // MB
      usage: Math.round(heapUsagePercent), // %
    },
    system: {
      used: Math.round((systemMem.total - systemMem.free) / 1024 / 1024), // MB
      total: Math.round(systemMem.total / 1024 / 1024), // MB
      usage: Math.round(systemUsagePercent), // %
    },
  };
}
