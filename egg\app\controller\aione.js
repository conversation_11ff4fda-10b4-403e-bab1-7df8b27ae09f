const { Controller } = require('egg');
const { dayNow, dateNow } = require('../extend/helper');
const axios = require('axios');
const OpenAI = require('openai');
const dayjs = require('dayjs');
const utc = require('dayjs/plugin/utc');
const timezone = require('dayjs/plugin/timezone');
const ctx = require('egg-scripts/lib/command');
dayjs.extend(utc);
dayjs.extend(timezone);
const qiaopi = `\n用丰富emoji加超级无敌说人话解释这题从什么信息入手可以在考场上10秒左右快速秒杀，先给我草稿纸极简秒题手稿（说人话极速理解版本,数学题草稿6行左右），再说考场做题实操/实录，最后说题目的秒杀思路，秒完撕卷下一题(最关键的地方加粗，不要用md的code模块和plaintext渲染)\n`;
const { logger } = ctx;

class AioneController extends Controller {
  async index() {
    const { ctx } = this;
    let body = ctx.request.body;
    let content = body.content;
    ctx.body = await ctx.service.ai.index(content);
  }

  async sili() {
    const { ctx } = this;
    let body = ctx.request.body;
    let content = body.content;
    ctx.body = await ctx.service.ai.sili(content);
  }

  async save() {
    const { ctx } = this;
    let body = ctx.request.body;
    let content = body.content;
    let data = await ctx.service.xr['query'](`SELECT *
                                              FROM fbsy
                                              WHERE ds is null
                                                and allcateid like '%656600%'
                                                and (content like '%http%' and solution like '%http%')
                                              ORDER BY id desc limit 1`);
    let text = `
  ${data[0].content}\n
  \nA.${data[0].answerone}\n
  \nB.${data[0].answertwo}\n
  \nC.${data[0].answerthree}\n
  \nD.${data[0].answerfour}\n
  ====================================
  \n用emoji加大白话解释这题从什么信息入手可以在考场上快速秒杀\n
  \n${data[0].solution}\n
  \n用emoji加大白话解释这题从什么信息入手可以在考场上快速秒杀\n`;
    text = text
      .replace(/fenbike\.cn/g, 'fbstatic.cn')
      .replace(/src=(['"])\/\/([^'"]+)/g, 'src=$1https://$2');
    ctx.body = await ctx.service.ai['renderHtmlToImage'](text);
  }

  async save1() {
    const { ctx } = this;
    let body = ctx.request.body;
    let content = body.content;
    ctx.body = await ctx.service.ai.silipic(
      `你能看到这张图吗`,
      'C:/Users/<USER>/Desktop/question-2025-06-05.png',
    );
  }

  async updatefbsyzc() {
    const { ctx, app, logger } = this;
    const per = ctx['query'].per;
    const type = ctx['query'].type || 0;
    const type1 = ctx['query'].type1 || false;
    const model = ctx['query'].model;
    const up = ctx['query'].up;
    const lock = ctx['query'].lock || false;
    const ss = ctx['query'].ss || false;

    // 处理 lock 和 ss 参数
    if (lock) {
      await this.resetRedisLocks(app);
      await ctx.service.xr['query'](`UPDATE fbsy
                                     SET ds = NULL
                                     WHERE ds = 'ds'`);
      ctx.body = await app.redis.get('zhihuwenda');
      return;
    }
    if (ss) {
      await ctx.service.xr['query'](`UPDATE fbsy
                                     SET ds = NULL
                                     WHERE allcateid like '%656600%'
                                       and ds = 'ds'`);
      await app.redis.set('zhihuwenda2', 0);
      ctx.body = await app.redis.get('zhihuwenda2');
      return;
    }

    // 检查锁
    const redisLockMap = {
      2: { key: 'sili', message: '正在处理其他问题，请稍后再试' },
      3: { key: 'zhihuwenda', message: '正在处理其他问题，请稍后再试' },
      4: { key: 'zhihuwenda1', message: '正在处理其他问题，请稍后再试' },
      5: { key: 'zhihuwenda2', message: '正在处理其他问题，请稍后再试' },
      6: { key: 'zhihuwenda2', message: '正在处理其他问题，请稍后再试2' },
      7: { key: 'zhihuwenda3', message: '正在处理其他问题，请稍后再试2' },
      8: { key: 'zhihuwenda4', message: '正在处理其他问题，请稍后再试2' },
      9: { key: 'zhihuwenda5', message: '正在处理其他问题，请稍后再试2' },
      11: { key: 'zhihuwenda6', message: '正在处理其他问题，请稍后再试2' },
      12: { key: 'zhihuwenda7', message: '正在处理其他问题，请稍后再试2' },
    };
    const lockConfig = redisLockMap[+model];
    if (lockConfig) {
      const isLocked = await app.redis.get(lockConfig.key);
      if (+isLocked === 1) {
        ctx.body = {
          success: false,
          answers: lockConfig.message,
        };
        return;
      }
    }

    const startTime = Date.now();

    // 构建查询条件
    const option = this.buildQueryOption(type, type1, +model);
    const sql = this.buildQuerySql(option, per, up);

    let res = await ctx.service.xr['query'](sql);
    if (!res || res.length === 0) {
      ctx.body = {
        message: '零',
      };
      return;
    }

    let res1 = await ctx.service.xr['query'](`SELECT *
                                              FROM fbsy
                                              WHERE (ds = '重新更新')
                                              ORDER BY id desc limit ${per}`);
    if (!res1 || res1.length === 0) {
      await this.updateExternalData();
      ctx.body = { message: '结束' };
      return;
    } else {
      if (!up) {
        res = res1;
      }
    }

    let list = [];
    for (let item of res) {
      let text = this.buildFinalText(item);
      text = text
        .replace(/fenbike\.cn/g, 'fbstatic.cn')
        .replace(/\/\/fb\./g, 'https://fb.');

      let ai;
      const modelMap = {
        2: { key: 'index', serviceMethod: 'index' },
        3: {
          key: 'zhihuwenda',
          serviceMethod: 'askQuestion3',
          token: '72714742-7a0d-4cc5-8660-524efe8130e1',
          xone: '2.0_sVJK8h=bsxR9NyGwvdIy7CYDC1bOY5mgjtt+/zIrtqXLw6jTQ3OpQRyoM=xZ=W=4',
          xtwo: '2.0_GidPSl/yJJ5G+U1lwDUZCdwaWBx/ytpm4B=v3n3jo5rLjGfasYTk5hUupNZee62X',
          session_id: '3672455471234395532',
          cookie: 'zhcookie',
        },
      };
      const config = modelMap[+model];
      let timeout;

      try {
        if (config) {
          const { key, serviceMethod, token, xone, xtwo, session_id, cookie } =
            config;
          const isLocked = await app.redis.get(key);
          if ((+isLocked === 3 || +isLocked === 1) && key !== 'sili:count') {
            ctx.body = {
              success: false,
              answers: '正在处理其他问题，请稍后再试',
            };
            return;
          }
          const whatmodel = +model !== 2 ? '知乎问答' : '硅基流动收尾';
          +model !== 10 ? await app.redis.set(key, '1') : '';

          timeout = setTimeout(
            async () => {
              await ctx.service.ai[serviceMethod](
                text,
                token,
                xone,
                xtwo,
                session_id,
                cookie,
                model,
                key,
                true,
              );
              logger.info('停止');
              await this.updateRedisAfterTimeout(app, +model);
            },
            20 * 60 * 1000,
          );

          await ctx.service.xr['update']('fbsy', {
            id: item.id,
            ds: 'ds',
            choice: +model,
          });

          if (item.content.match('.cn/')) {
            const maxRetries = 5; // 最大重试次数
            let retries = 0;
            let x;
            while (retries < maxRetries) {
              try {
                x = await ctx.service.ai['renderHtmlToImage'](text);
                break;
              } catch (err) {
                retries++;
                console.error(
                  `renderHtmlToImage 失败，正在重试 (第 ${retries} 次)...`,
                  err,
                );
                await new Promise((r) => setTimeout(r, 500));
              }
            }
            if (!x) {
              logger.error('renderHtmlToImage 重试次数达到上限，跳过该记录');
              continue;
            }
            const timu = x?.text ?? '';
            text = timu + `\n===================================\n` + text;
          }

          ai = await ctx.service.ai[serviceMethod](
            text,
            token,
            xone,
            xtwo,
            session_id,
            cookie,
            model,
            key,
          );
        }
      } finally {
        const endTime = Date.now();
        const elapsed = endTime - startTime;
        const minutes = Math.floor(elapsed / 60000);
        const seconds = ((elapsed % 60000) / 1000).toFixed(2);

        if (timeout) {
          logger.info(`定时器 (ID: ${timeout}) 已清除`);
        } else {
          logger.info('没有定时器需要清除');
        }

        await this.updateRedisAfterProcess(app, +model);
        logger.info(`updatefbsyzc执行耗时: ${minutes} 分 ${seconds} 秒`);

        if (+minutes === 0 && seconds < 6) {
          logger.error('耗时小于6秒', ai?.status || 401, item.id, item.content);
          await ctx.service.xr['query'](`UPDATE fbsy
                                         SET ds  = '无法回答',
                                             tag = ${+item.tag + 1}
                                         WHERE id = ${item.id}`);
        }

        list.push(ai);

        try {
          await this.handleAiResponse(ctx, item, ai, +model);
        } catch (e) {
          const text =
            e?.response?.data?.error?.message || e?.message || '未知错误';
          ctx.service.feishu['fs']('更新出现了' + text);
        }

        if (item?.ds?.includes?.('延迟退休是基于我国经济')) {
          await ctx.service.xr['query'](`update fbsy
                                         set ds = '无法回答'
                                         where id = ${item.id}`);
        }
        if (+item.tag > 3) {
          logger.error('item.tag超过3了', item.id, item.content);
          await ctx.service.feishu['fs'](
            `item.tag超过3了,${item.id},${item.content}`,
          );
          await ctx.service.xr['query'](`UPDATE fbsy
                                         SET ds  = '无法回答',
                                             tag = ${+item.tag + 1}
                                         WHERE id = ${item.id}`);
        }
        if (+item.tag > 10) {
          logger.error('item.tag超过10了', item.id, item.content);
          await ctx.service.feishu['fs'](
            `item.tag超过10了,${item.id},${item.content}`,
          );
          await ctx.service.xr['query'](`UPDATE fbsy
                                         SET ds = '手稿'
                                         WHERE id = ${item.id}`);
        }

        const kong = await ctx.service.xr['query'](`SELECT count(id) as kong
                                                    FROM fbsy
                                                    WHERE ${option}
                                                      AND (ds IS NULL OR
                                                           (ds NOT LIKE '%手稿%' AND ds NOT LIKE '%无法回答%' AND ds NOT LIKE '%无法针对%' AND ds != 'ds'))
                                                    ORDER BY id desc`);
        const bukong = await ctx.service.xr['query'](`SELECT count(id) as bukong
                                                      FROM fbsy
                                                      where ${option}
                                                        and ds LIKE '%手稿%'
                                                      ORDER BY id desc`);

        const cateid = item.allcateid.split(',')[0];
      }
    }
  }

  // 重置 Redis 锁
  async resetRedisLocks(app) {
    const keys = await app.redis.keys('zhihuwenda*');
    for (const key of keys) {
      await app.redis.set(key, 0);
    }
    await app.redis.set('sili', 0);
    await app.redis.set(`sili:count`, 0);
  }

  // 构建查询条件
  buildQueryOption(type, type1, model) {
    let option = `allcateid like '%${type}%'`;
    if (type1) {
      option = `(allcateid like '%${type}%' or allcateid like '%${type1}%')`;
    }
    if (model === 10) {
      option = ` (allcateid like '%656618%' or allcateid like '%656610%' or allcateid like '%656608%' or allcateid like '%656704%')`;
    }
    return option;
  }

  // 构建查询 SQL
  buildQuerySql(option, per, up) {
    if (up) {
      return `SELECT *
              FROM fbsy
              WHERE (ds like '%无法回答%' or ds like '%无法针对%')
              ORDER BY id desc limit ${per}`;
    }
    return `SELECT *
            FROM fbsy
            WHERE ${option}
              AND (ds IS NULL OR
                   (ds NOT LIKE '%手稿%' AND ds NOT LIKE '%无法回答%' AND ds NOT LIKE '%无法针对%' AND ds != 'ds'))
            ORDER BY id DESC LIMIT ${per}`;
  }

  // 更新外部数据
  async updateExternalData() {
    const biao = [
      'gzslwwb1',
      'gzyy',
      'hs22sl',
      'hspdyd',
      'hsljpd',
      'fbsykj',
      'hsydsl',
      'hsydpd',
    ];
    for (let item of biao) {
      try {
        await axios.get(`http://127.0.0.1:7001/updatekj?biao=${item}&model=`);
      } catch (e) {
        // 处理异常
      }
    }
  }

  // 构建最终文本
  buildFinalText(item) {
    const isds = item?.ds ?? '';
    return `
  ${item?.material ? item.material : ''}\n${item.content}\n
  \nA.${item.answerone}\n
  \nB.${item.answertwo}\n
  \nC.${item.answerthree}\n
  \nD.${item.answerfour}\n
  ====================================
  \n${item.solution}\n
  \n${isds}\n
  ${qiaopi}`;
  }

  // 超时后更新 Redis
  async updateRedisAfterTimeout(app, model) {
    const redisSetMap = {
      2: 'sili',
      3: 'zhihuwenda',
      4: 'zhihuwenda1',
      5: 'zhihuwenda2',
      6: 'zhihuwenda2',
      7: 'zhihuwenda3',
      8: 'zhihuwenda4',
      9: 'zhihuwenda5',
      11: 'zhihuwenda6',
      12: 'zhihuwenda7',
    };
    const redisKey = redisSetMap[model];
    const isLocked = await app.redis.get(redisKey);
    if (redisKey) {
      if (+isLocked !== 3 || +isLocked === 1) {
        await app.redis.set(redisKey, '9');
        logger.info('停止后更新redis成功', isLocked);
      }
    } else {
      logger.error('停止没更新redis成功', isLocked);
      console.log(model);
    }
  }

  // 处理完成后更新 Redis
  async updateRedisAfterProcess(app, model) {
    const redisSetMap = {
      2: 'sili',
      3: 'zhihuwenda',
      4: 'zhihuwenda1',
      5: 'zhihuwenda2',
      6: 'zhihuwenda2',
      7: 'zhihuwenda3',
      8: 'zhihuwenda4',
      9: 'zhihuwenda5',
      11: 'zhihuwenda6',
      12: 'zhihuwenda7',
    };
    const redisKey = redisSetMap[model];
    const isLocked = await app.redis.get(redisKey);
    if (redisKey) {
      if (+isLocked !== 3 || +isLocked === 1) {
        await app.redis.set(redisKey, '2');
        logger.info('更新redis成功', isLocked);
      }
    } else {
      logger.error('没更新redis成功', isLocked);
      console.log(model);
    }
  }

  // 处理 AI 响应
  async handleAiResponse(ctx, item, ai, model) {
    if (ai?.status === 200) {
      await ctx.service.xr['update']('fbsy', {
        id: item.id,
        ds: ai.data.replace(/```/g, ''),
        choice: null,
      });
    } else if (model !== 10) {
      logger.error(
        'ai状态不是200,model不是10',
        ai?.status || 401,
        item.id,
        item.content,
      );
      await ctx.service.xr['query'](`UPDATE fbsy
                                     SET ds  = null,
                                         tag = ${+item.tag + 1}
                                     WHERE id = ${item.id}`);
    } else if (model === 10) {
      logger.error(
        'ai状态不是200，model是10',
        ai?.status || 401,
        item.id,
        item.content,
      );
      await ctx.service.xr['query'](`UPDATE fbsy
                                     SET ds = '无法回答'
                                     WHERE id = ${item.id}`);
    } else {
      logger.error('进入你好了', ai?.status || 402, item.id, item.content);
      ctx.service.feishu['fs']('进入你好了:' + item.id);
      await ctx.service.xr['query'](`UPDATE fbsy
                                     SET ds = '你好'
                                     WHERE id = ${item.id}`);
    }
  }
}

module.exports = AioneController;
