#!/bin/bash

# Egg.js 日志管理脚本
# 用于清理、监控和管理日志文件

LOG_DIR="/logs/egg"
BACKUP_DIR="/logs/egg/backup"
MAX_LOG_SIZE="100M"  # 单个日志文件最大大小
KEEP_DAYS=3          # 保留天数

# 颜色定义
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# 打印带颜色的消息
print_info() {
    echo -e "${BLUE}[INFO]${NC} $1"
}

print_success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1"
}

print_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

print_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

# 检查日志目录
check_log_dir() {
    if [ ! -d "$LOG_DIR" ]; then
        print_error "日志目录不存在: $LOG_DIR"
        exit 1
    fi
    print_info "日志目录: $LOG_DIR"
}

# 显示日志文件状态
show_log_status() {
    print_info "=== 日志文件状态 ==="
    
    if [ -d "$LOG_DIR" ]; then
        cd "$LOG_DIR" || exit 1
        
        echo "文件名                    大小      修改时间"
        echo "----------------------------------------"
        
        for file in *.log*; do
            if [ -f "$file" ]; then
                size=$(du -h "$file" | cut -f1)
                time=$(stat -c %y "$file" | cut -d' ' -f1,2 | cut -d'.' -f1)
                printf "%-20s %8s  %s\n" "$file" "$size" "$time"
            fi
        done
        
        echo "----------------------------------------"
        total_size=$(du -sh . | cut -f1)
        file_count=$(find . -name "*.log*" -type f | wc -l)
        print_info "总大小: $total_size, 文件数量: $file_count"
    fi
}

# 清理旧日志
clean_old_logs() {
    print_info "=== 清理 $KEEP_DAYS 天前的日志 ==="
    
    if [ -d "$LOG_DIR" ]; then
        cd "$LOG_DIR" || exit 1
        
        # 查找并删除旧文件
        old_files=$(find . -name "*.log*" -type f -mtime +$KEEP_DAYS)
        
        if [ -n "$old_files" ]; then
            echo "$old_files" | while read -r file; do
                print_warning "删除旧日志: $file"
                rm -f "$file"
            done
            print_success "旧日志清理完成"
        else
            print_info "没有需要清理的旧日志"
        fi
    fi
}

# 压缩大日志文件
compress_large_logs() {
    print_info "=== 压缩大于 $MAX_LOG_SIZE 的日志文件 ==="
    
    if [ -d "$LOG_DIR" ]; then
        cd "$LOG_DIR" || exit 1
        
        # 创建备份目录
        mkdir -p "$BACKUP_DIR"
        
        # 查找大文件
        large_files=$(find . -name "*.log" -type f -size +$MAX_LOG_SIZE)
        
        if [ -n "$large_files" ]; then
            echo "$large_files" | while read -r file; do
                if [[ ! "$file" =~ \.gz$ ]]; then
                    print_warning "压缩大文件: $file"
                    gzip "$file"
                    print_success "已压缩: ${file}.gz"
                fi
            done
        else
            print_info "没有需要压缩的大文件"
        fi
    fi
}

# 实时监控日志
monitor_logs() {
    print_info "=== 实时监控日志 (按 Ctrl+C 退出) ==="
    
    if [ -f "$LOG_DIR/egg-web.log" ]; then
        tail -f "$LOG_DIR/egg-web.log"
    else
        print_error "主日志文件不存在: $LOG_DIR/egg-web.log"
    fi
}

# 搜索日志内容
search_logs() {
    local keyword="$1"
    if [ -z "$keyword" ]; then
        print_error "请提供搜索关键词"
        return 1
    fi
    
    print_info "=== 搜索关键词: $keyword ==="
    
    if [ -d "$LOG_DIR" ]; then
        cd "$LOG_DIR" || exit 1
        grep -r --color=always "$keyword" *.log* 2>/dev/null || print_warning "未找到匹配内容"
    fi
}

# 显示帮助信息
show_help() {
    echo "Egg.js 日志管理工具"
    echo ""
    echo "用法: $0 [选项]"
    echo ""
    echo "选项:"
    echo "  status     显示日志文件状态"
    echo "  clean      清理旧日志文件"
    echo "  compress   压缩大日志文件"
    echo "  monitor    实时监控日志"
    echo "  search     搜索日志内容"
    echo "  all        执行清理和压缩"
    echo "  help       显示此帮助信息"
    echo ""
    echo "示例:"
    echo "  $0 status                    # 查看日志状态"
    echo "  $0 clean                     # 清理旧日志"
    echo "  $0 search '服务器位置'        # 搜索特定内容"
    echo "  $0 monitor                   # 实时监控"
}

# 主函数
main() {
    case "$1" in
        "status")
            check_log_dir
            show_log_status
            ;;
        "clean")
            check_log_dir
            clean_old_logs
            ;;
        "compress")
            check_log_dir
            compress_large_logs
            ;;
        "monitor")
            check_log_dir
            monitor_logs
            ;;
        "search")
            check_log_dir
            search_logs "$2"
            ;;
        "all")
            check_log_dir
            clean_old_logs
            compress_large_logs
            show_log_status
            ;;
        "help"|"--help"|"-h"|"")
            show_help
            ;;
        *)
            print_error "未知选项: $1"
            show_help
            exit 1
            ;;
    esac
}

# 执行主函数
main "$@"
