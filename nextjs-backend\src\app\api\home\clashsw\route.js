/**
 * Clash开关API路由
 * 迁移自egg项目的home.js clashsw方法
 */

const { NextResponse } = require('next/server');
const { app } = require('../../../../lib/app');

// 确保应用已初始化
let appInitialized = false;

async function ensureAppInitialized() {
  if (!appInitialized) {
    await app.init();
    appInitialized = true;
  }
}

async function GET(request) {
  try {
    await ensureAppInitialized();
    
    const { searchParams } = new URL(request.url);
    const sw = searchParams.get('sw');
    
    console.log('🔄 Clash开关操作 - sw:', sw);
    
    if (sw !== null) {
      // 更新数据库中的开关状态
      const updateResult = await app.mysql.update(
        'sw',
        {
          sw: +sw,
        },
        {
          where: {
            name: 'clash',
          },
        }
      );
      
      console.log('✅ 更新结果:', updateResult);
    }
    
    // 查询当前状态
    const currentState = await app.mysql.get('sw', { name: 'clash' });
    
    console.log('📊 当前Clash状态:', currentState);
    
    return NextResponse.json({
      data: currentState
    });
    
  } catch (error) {
    console.error('❌ ClashSW API错误:', error.message);
    return NextResponse.json({ 
      error: error.message,
      timestamp: new Date().toISOString()
    }, { status: 500 });
  }
}

module.exports = { GET };
