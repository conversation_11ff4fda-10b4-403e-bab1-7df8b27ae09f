#!/bin/bash

# Vue项目部署通知 - curl测试示例

# 配置
SERVER_URL="http://localhost:7001"
# SERVER_URL="https://your-production-server.com"

echo "🎯 Vue项目部署通知测试"
echo "服务器地址: $SERVER_URL"
echo "测试时间: $(date)"
echo "━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━"

# 1. 测试部署成功通知
echo ""
echo "1️⃣ 测试部署成功通知..."
curl -X POST "$SERVER_URL/api/deploy/success" \
  -H "Content-Type: application/json" \
  -d '{
    "projectName": "Vue前端项目",
    "environment": "production",
    "version": "v1.2.3",
    "branch": "main",
    "commitHash": "a1b2c3d4e5f6789012345678901234567890abcd",
    "commitMessage": "feat: 添加新功能和优化性能",
    "deployTime": "'$(date "+%Y-%m-%d %H:%M:%S")'",
    "deployDuration": "2分钟30秒",
    "deployedBy": "GitHub Actions",
    "serverUrl": "https://your-domain.com",
    "buildSize": "2.5MB",
    "features": [
      "🎨 新增用户管理功能",
      "⚡ 优化页面加载速度",
      "🐛 修复已知bug",
      "📦 更新UI组件库"
    ],
    "notes": "本次部署包含重要功能更新，请及时测试验证"
  }'

echo ""
echo ""

# 2. 测试部署失败通知
echo "2️⃣ 测试部署失败通知..."
curl -X POST "$SERVER_URL/api/deploy/failure" \
  -H "Content-Type: application/json" \
  -d '{
    "projectName": "Vue前端项目",
    "environment": "production",
    "branch": "main",
    "commitHash": "a1b2c3d4e5f6789012345678901234567890abcd",
    "failureTime": "'$(date "+%Y-%m-%d %H:%M:%S")'",
    "errorMessage": "Build failed: Module not found error in src/components/UserManagement.vue",
    "deployedBy": "GitHub Actions",
    "logUrl": "https://github.com/your-org/your-repo/actions/runs/123456789",
    "retryCount": 1
  }'

echo ""
echo ""

# 3. 获取API模板
echo "3️⃣ 获取API模板..."
curl -X GET "$SERVER_URL/api/deploy/template"

echo ""
echo ""

# 4. 测试内置测试接口
echo "4️⃣ 测试内置测试接口..."
curl -X GET "$SERVER_URL/api/deploy/test"

echo ""
echo ""
echo "🎉 所有测试完成！"

# GitHub Actions 示例
echo ""
echo "━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━"
echo "📋 GitHub Actions 使用示例:"
echo ""
echo "# 在你的 .github/workflows/deploy.yml 中添加:"
echo ""
echo "- name: Send deploy success notification"
echo "  if: success()"
echo "  run: |"
echo "    curl -X POST \"$SERVER_URL/api/deploy/success\" \\"
echo "      -H \"Content-Type: application/json\" \\"
echo "      -d '{"
echo "        \"projectName\": \"Vue前端项目\","
echo "        \"environment\": \"production\","
echo "        \"version\": \"\${{ github.ref_name }}\","
echo "        \"branch\": \"\${{ github.ref_name }}\","
echo "        \"commitHash\": \"\${{ github.sha }}\","
echo "        \"commitMessage\": \"\${{ github.event.head_commit.message }}\","
echo "        \"deployTime\": \"'$(date "+%Y-%m-%d %H:%M:%S")'\","
echo "        \"deployedBy\": \"GitHub Actions\","
echo "        \"serverUrl\": \"https://your-domain.com\""
echo "      }'"
echo ""
echo "- name: Send deploy failure notification"
echo "  if: failure()"
echo "  run: |"
echo "    curl -X POST \"$SERVER_URL/api/deploy/failure\" \\"
echo "      -H \"Content-Type: application/json\" \\"
echo "      -d '{"
echo "        \"projectName\": \"Vue前端项目\","
echo "        \"environment\": \"production\","
echo "        \"branch\": \"\${{ github.ref_name }}\","
echo "        \"commitHash\": \"\${{ github.sha }}\","
echo "        \"failureTime\": \"'$(date "+%Y-%m-%d %H:%M:%S")'\","
echo "        \"errorMessage\": \"部署失败，请查看日志\","
echo "        \"deployedBy\": \"GitHub Actions\","
echo "        \"logUrl\": \"\${{ github.server_url }}/\${{ github.repository }}/actions/runs/\${{ github.run_id }}\""
echo "      }'"
