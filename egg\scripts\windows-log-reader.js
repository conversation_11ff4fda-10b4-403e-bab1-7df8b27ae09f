#!/usr/bin/env node

// Windows本地日志读取工具

const fs = require('fs');
const path = require('path');
const { spawn } = require('child_process');

// Windows下可能的日志位置
const possibleLogPaths = [
  'logs/egg/',
  '../logs/egg/',
  '../../logs/egg/',
  'C:/logs/egg/',
  'D:/logs/egg/',
  process.env.USERPROFILE + '/logs/egg/',
  './logs/',
  '../logs/',
];

// 日志文件名
const logFiles = {
  web: 'egg-web.log',
  agent: 'egg-agent.log',
  error: 'common-error.log'
};

function findLogDirectory() {
  console.log('🔍 搜索日志目录...');
  
  for (const logPath of possibleLogPaths) {
    const fullPath = path.resolve(logPath);
    console.log(`检查: ${fullPath}`);
    
    if (fs.existsSync(fullPath)) {
      // 检查是否包含日志文件
      const hasLogFiles = Object.values(logFiles).some(fileName => {
        const filePath = path.join(fullPath, fileName);
        return fs.existsSync(filePath);
      });
      
      if (hasLogFiles) {
        console.log(`✅ 找到日志目录: ${fullPath}`);
        return fullPath;
      }
    }
  }
  
  console.log('❌ 未找到日志目录');
  return null;
}

function getLogFileInfo(logDir) {
  console.log('\n📊 日志文件信息:');
  
  const info = {};
  
  for (const [type, fileName] of Object.entries(logFiles)) {
    const filePath = path.join(logDir, fileName);
    
    if (fs.existsSync(filePath)) {
      const stat = fs.statSync(filePath);
      info[type] = {
        path: filePath,
        size: stat.size,
        sizeHuman: formatFileSize(stat.size),
        modified: stat.mtime.toLocaleString('zh-CN'),
        exists: true
      };
      
      console.log(`✅ ${type.toUpperCase()} 日志: ${fileName}`);
      console.log(`   路径: ${filePath}`);
      console.log(`   大小: ${info[type].sizeHuman}`);
      console.log(`   修改: ${info[type].modified}`);
    } else {
      info[type] = {
        path: filePath,
        exists: false
      };
      
      console.log(`❌ ${type.toUpperCase()} 日志: ${fileName} (不存在)`);
    }
    console.log('');
  }
  
  return info;
}

function formatFileSize(bytes) {
  if (bytes === 0) return '0 B';
  const k = 1024;
  const sizes = ['B', 'KB', 'MB', 'GB'];
  const i = Math.floor(Math.log(bytes) / Math.log(k));
  return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];
}

function readLastLines(filePath, lineCount = 50) {
  return new Promise((resolve, reject) => {
    if (!fs.existsSync(filePath)) {
      reject(new Error(`文件不存在: ${filePath}`));
      return;
    }
    
    // Windows下使用PowerShell的Get-Content命令
    const psCommand = `Get-Content -Path "${filePath}" -Tail ${lineCount} -Encoding UTF8`;
    
    const powershell = spawn('powershell', ['-Command', psCommand], {
      encoding: 'utf8'
    });
    
    let output = '';
    let errorOutput = '';
    
    powershell.stdout.setEncoding('utf8');
    powershell.stdout.on('data', (data) => {
      output += data;
    });
    
    powershell.stderr.setEncoding('utf8');
    powershell.stderr.on('data', (data) => {
      errorOutput += data;
    });
    
    powershell.on('close', (code) => {
      if (code === 0) {
        const lines = output.split('\n').filter(line => line.trim());
        resolve(lines);
      } else {
        reject(new Error(`PowerShell错误 (${code}): ${errorOutput}`));
      }
    });
    
    powershell.on('error', (error) => {
      // 如果PowerShell失败，尝试直接读取文件
      console.log('PowerShell失败，尝试直接读取文件...');
      
      try {
        const content = fs.readFileSync(filePath, 'utf8');
        const lines = content.split('\n').filter(line => line.trim());
        const lastLines = lines.slice(-lineCount);
        resolve(lastLines);
      } catch (readError) {
        reject(readError);
      }
    });
  });
}

async function displayLogContent(logDir, logType, lineCount = 50) {
  const fileName = logFiles[logType];
  const filePath = path.join(logDir, fileName);
  
  console.log(`\n📖 ${logType.toUpperCase()} 日志内容 (最后${lineCount}行):`);
  console.log('═'.repeat(80));
  
  try {
    const lines = await readLastLines(filePath, lineCount);
    
    if (lines.length === 0) {
      console.log('📝 日志文件为空');
      return;
    }
    
    lines.forEach((line, index) => {
      const lineNumber = lines.length - lineCount + index + 1;
      
      // 简单的日志级别着色
      let prefix = '📄';
      if (line.includes('ERROR')) prefix = '🔴';
      else if (line.includes('WARN')) prefix = '🟡';
      else if (line.includes('INFO')) prefix = '🔵';
      
      console.log(`${prefix} ${String(lineNumber).padStart(4, ' ')}: ${line}`);
    });
    
  } catch (error) {
    console.error(`❌ 读取日志失败: ${error.message}`);
  }
}

function watchLogFile(filePath, logType) {
  console.log(`\n👀 开始监控 ${logType.toUpperCase()} 日志: ${filePath}`);
  console.log('按 Ctrl+C 停止监控');
  console.log('═'.repeat(80));
  
  if (!fs.existsSync(filePath)) {
    console.error(`❌ 文件不存在: ${filePath}`);
    return;
  }
  
  let lastSize = fs.statSync(filePath).size;
  
  const watcher = fs.watchFile(filePath, { interval: 1000 }, (curr, prev) => {
    if (curr.size > lastSize) {
      // 文件增长了，读取新内容
      const stream = fs.createReadStream(filePath, {
        start: lastSize,
        end: curr.size - 1,
        encoding: 'utf8'
      });
      
      let buffer = '';
      
      stream.on('data', (chunk) => {
        buffer += chunk;
        const lines = buffer.split('\n');
        buffer = lines.pop(); // 保留最后一个可能不完整的行
        
        lines.forEach(line => {
          if (line.trim()) {
            const timestamp = new Date().toLocaleTimeString();
            
            // 简单的日志级别着色
            let prefix = '📄';
            if (line.includes('ERROR')) prefix = '🔴';
            else if (line.includes('WARN')) prefix = '🟡';
            else if (line.includes('INFO')) prefix = '🔵';
            
            console.log(`${prefix} [${timestamp}] ${line}`);
          }
        });
      });
      
      lastSize = curr.size;
    }
  });
  
  // 处理Ctrl+C
  process.on('SIGINT', () => {
    console.log('\n\n⏹️ 停止监控');
    fs.unwatchFile(filePath);
    process.exit(0);
  });
}

async function main() {
  console.log('🪟 Windows 日志读取工具');
  console.log('当前目录:', process.cwd());
  console.log('时间:', new Date().toLocaleString('zh-CN'));
  console.log('═'.repeat(60));
  
  // 查找日志目录
  const logDir = findLogDirectory();
  if (!logDir) {
    console.log('\n💡 建议:');
    console.log('1. 确认Egg.js应用已启动并生成了日志');
    console.log('2. 检查config中的日志路径配置');
    console.log('3. 手动指定日志目录路径');
    return;
  }
  
  // 获取日志文件信息
  const logInfo = getLogFileInfo(logDir);
  
  // 获取命令行参数
  const args = process.argv.slice(2);
  const command = args[0] || 'info';
  const logType = args[1] || 'web';
  const lineCount = parseInt(args[2]) || 50;
  
  switch (command) {
    case 'info':
      console.log('✅ 日志信息已显示');
      console.log('\n📋 可用命令:');
      console.log('node windows-log-reader.js read [web|agent|error] [行数]  - 读取日志');
      console.log('node windows-log-reader.js watch [web|agent|error]        - 监控日志');
      console.log('node windows-log-reader.js all                            - 显示所有日志');
      break;
      
    case 'read':
      if (logInfo[logType]?.exists) {
        await displayLogContent(logDir, logType, lineCount);
      } else {
        console.log(`❌ ${logType} 日志不存在`);
      }
      break;
      
    case 'watch':
      if (logInfo[logType]?.exists) {
        watchLogFile(logInfo[logType].path, logType);
      } else {
        console.log(`❌ ${logType} 日志不存在`);
      }
      break;
      
    case 'all':
      for (const type of ['web', 'agent', 'error']) {
        if (logInfo[type]?.exists) {
          await displayLogContent(logDir, type, 20);
        }
      }
      break;
      
    default:
      console.log(`❌ 未知命令: ${command}`);
  }
}

if (require.main === module) {
  main().catch(console.error);
}

module.exports = { findLogDirectory, getLogFileInfo, readLastLines, displayLogContent };
