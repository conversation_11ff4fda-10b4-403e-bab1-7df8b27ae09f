#!/usr/bin/env node

/**
 * 快速启动脚本 - 优化开发环境启动速度
 */

const { spawn } = require('child_process');
const path = require('path');
const fs = require('fs');

console.log('🚀 启动优化模式...');

// 设置环境变量
process.env.NODE_ENV = 'development';
process.env.EGG_SERVER_ENV = 'dev';

// 检查是否存在 node_modules
const nodeModulesPath = path.join(__dirname, '../node_modules');
if (!fs.existsSync(nodeModulesPath)) {
  console.log('📦 检测到缺少依赖，正在安装...');
  const installProcess = spawn('npm', ['install'], {
    cwd: path.join(__dirname, '..'),
    stdio: 'inherit'
  });
  
  installProcess.on('close', (code) => {
    if (code === 0) {
      startServer();
    } else {
      console.error('❌ 依赖安装失败');
      process.exit(1);
    }
  });
} else {
  startServer();
}

function startServer() {
  console.log('⚡ 启动开发服务器（优化模式）...');

  // Windows兼容性处理
  const isWindows = process.platform === 'win32';

  // 使用npm run而不是直接调用npx，更稳定
  const command = isWindows ? 'npm.cmd' : 'npm';
  const args = ['run', 'dev'];

  const serverProcess = spawn(command, args, {
    cwd: path.join(__dirname, '..'),
    stdio: 'inherit',
    shell: isWindows, // Windows下使用shell
    env: {
      ...process.env,
      // 优化Node.js启动参数
      NODE_OPTIONS: '--max-old-space-size=2048',
      NODE_ENV: 'development',
      EGG_SERVER_ENV: 'local',
    }
  });

  // 处理进程退出
  process.on('SIGINT', () => {
    console.log('\n🛑 正在关闭服务器...');
    serverProcess.kill('SIGINT');
  });

  process.on('SIGTERM', () => {
    console.log('\n🛑 正在关闭服务器...');
    serverProcess.kill('SIGTERM');
  });

  serverProcess.on('close', (code) => {
    console.log(`\n✅ 服务器已关闭，退出码: ${code}`);
    process.exit(code);
  });

  serverProcess.on('error', (err) => {
    console.error('❌ 启动失败:', err);
    process.exit(1);
  });
}

// 显示优化提示
console.log(`
🔧 启动优化说明:
- 使用单进程模式 (--workers=1)
- 禁用文件监听优化 (忽略静态资源)
- 减少内存使用
- 禁用不必要的插件

💡 如需完整功能，请使用: npm run dev
`);
