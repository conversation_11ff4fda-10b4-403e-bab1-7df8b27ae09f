#!/usr/bin/env node

// 检查包管理器脚本
const userAgent = process.env.npm_config_user_agent || '';
const execPath = process.env.npm_execpath || '';
const argv = process.argv.join(' ');

// 检查命令行参数
if (argv.includes('npm') && !argv.includes('pnpm')) {
  console.error('\n❌ This project only allows pnpm!');
  console.error('Please use "pnpm" instead of "npm"');
  console.error('Example: pnpm install, pnpm run dev, etc.\n');
  process.exit(1);
}

// 检查是否使用了 npm
if (userAgent.includes('npm') && !userAgent.includes('pnpm')) {
  console.error('\n❌ This project only allows pnpm!');
  console.error('Please use "pnpm" instead of "npm"');
  console.error('Example: pnpm install, pnpm run dev, etc.\n');
  process.exit(1);
}

// 检查是否使用了 yarn
if (userAgent.includes('yarn') && !userAgent.includes('pnpm')) {
  console.error('\n❌ This project only allows pnpm!');
  console.error('Please use "pnpm" instead of "yarn"');
  console.error('Example: pnpm install, pnpm add package-name, etc.\n');
  process.exit(1);
}

// 检查执行路径
if (execPath && execPath.includes('npm') && !execPath.includes('pnpm')) {
  console.error('\n❌ This project only allows pnpm!');
  console.error('Please use "pnpm" instead of "npm"');
  console.error('Example: pnpm install, pnpm run dev, etc.\n');
  process.exit(1);
}

if (execPath && execPath.includes('yarn') && !execPath.includes('pnpm')) {
  console.error('\n❌ This project only allows pnpm!');
  console.error('Please use "pnpm" instead of "yarn"');
  console.error('Example: pnpm install, pnpm add package-name, etc.\n');
  process.exit(1);
}

// 检查进程标题
if (process.title && process.title.includes('npm') && !process.title.includes('pnpm')) {
  console.error('\n❌ This project only allows pnpm!');
  console.error('Please use "pnpm" instead of "npm"');
  console.error('Example: pnpm install, pnpm run dev, etc.\n');
  process.exit(1);
}
