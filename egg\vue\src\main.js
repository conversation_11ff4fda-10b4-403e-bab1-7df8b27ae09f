import { useThemeStore } from '@/store/theme';
import Antd from 'ant-design-vue';
import 'ant-design-vue/dist/reset.css';
import axios from 'axios';
import dayjs from 'dayjs';
import ElementPlus from 'element-plus';
import 'element-plus/dist/index.css';
import { createPinia } from 'pinia';
import { createApp } from 'vue';
import VueAxios from 'vue-axios';
import App from './App.vue';
import './css/base.css';
import './css/canvas.css';
import './css/index.css';
import './css/layout.css';
import './css/loading-rainbow.css';
import './css/main.css';
import './css/table.css';
import router from './router';

const pinia = createPinia();
const app = createApp(App);
app.use(pinia);
app.use(VueAxios, axios);
app.use(router);
app.use(ElementPlus);
app.use(Antd);
app.use(dayjs);
window.axios = axios;

// useThemeStore 必须在 app.use(pinia) 之后调用
const themeStore = useThemeStore();

// 初始化主题（从localStorage读取或使用默认主题）
themeStore.initializeTheme();

// 添加全局路由守卫，离开 sy 页面自动切换纯白主题
router.beforeEach((to, from, next) => {
  // sy.vue 路由 name 规则：views/fb/sy.vue => name: 'fb-sy'
  if (to.name !== 'fb-sy') {
    themeStore.setTheme('plain');
  }
  next();
});

app.mount('#app');
