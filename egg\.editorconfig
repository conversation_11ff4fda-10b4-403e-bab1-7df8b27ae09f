# EditorConfig is awesome: https://EditorConfig.org

# top-most EditorConfig file
root = true

# Unix-style newlines with a newline ending every file
[*]
charset = utf-8
end_of_line = lf
insert_final_newline = true
trim_trailing_whitespace = true
indent_style = space
indent_size = 2

# JavaScript files
[*.{js,mjs,cjs}]
indent_style = space
indent_size = 2

# TypeScript files
[*.{ts,tsx}]
indent_style = space
indent_size = 2

# JSON files
[*.json]
indent_style = space
indent_size = 2

# YAML files
[*.{yml,yaml}]
indent_style = space
indent_size = 2

# Markdown files
[*.md]
trim_trailing_whitespace = false
indent_style = space
indent_size = 2

# Package.json
[package.json]
indent_style = space
indent_size = 2

# Configuration files
[*.{eslintrc,prettierrc,babelrc}]
indent_style = space
indent_size = 2

# Shell scripts
[*.sh]
indent_style = space
indent_size = 2

# Batch files
[*.{bat,cmd}]
end_of_line = crlf
