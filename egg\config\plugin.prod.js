'use strict';

/** @type Egg.EggPlugin */
module.exports = {
  // 继承基础插件配置
  nunjucks: {
    enable: true,
    package: 'egg-view-nunjucks',
  },
  ejs: {
    enable: true,
    package: 'egg-view-ejs',
  },
  mysql: {
    enable: true,
    package: 'egg-mysql',
  },
  // Redis插件配置
  redis: {
    enable: true, // 启用Redis
    package: 'egg-redis',
  },
  cors: {
    enable: true,
    package: 'egg-cors',
  },

  io: {
    enable: true,
    package: 'egg-socket.io',
  },
};
