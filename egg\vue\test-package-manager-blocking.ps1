# PowerShell script to test package manager blocking
Write-Host "🧪 Testing package manager blocking..." -ForegroundColor Blue

Write-Host "`n1. Testing npm blocking..." -ForegroundColor Yellow
try {
    $npmResult = npm install --dry-run 2>&1
    if ($LASTEXITCODE -ne 0) {
        Write-Host "✅ npm is successfully blocked!" -ForegroundColor Green
        Write-Host "Error message: $($npmResult -join ' ')" -ForegroundColor Gray
    } else {
        Write-Host "❌ npm is NOT blocked!" -ForegroundColor Red
    }
} catch {
    Write-Host "✅ npm is successfully blocked!" -ForegroundColor Green
}

Write-Host "`n2. Testing pnpm functionality..." -ForegroundColor Yellow
try {
    $pnpmVersion = pnpm --version
    if ($LASTEXITCODE -eq 0) {
        Write-Host "✅ pnpm is working correctly: v$pnpmVersion" -ForegroundColor Green
    } else {
        Write-Host "❌ pnpm is not working!" -ForegroundColor Red
    }
} catch {
    Write-Host "❌ pnpm is not working!" -ForegroundColor Red
}

Write-Host "`n3. Testing yarn blocking (if available)..." -ForegroundColor Yellow
try {
    $yarnResult = yarn install --dry-run 2>&1
    if ($LASTEXITCODE -ne 0) {
        Write-Host "✅ yarn is successfully blocked!" -ForegroundColor Green
    } else {
        Write-Host "⚠️  yarn is not blocked (but may not be installed)" -ForegroundColor Yellow
    }
} catch {
    Write-Host "✅ yarn is blocked or not installed" -ForegroundColor Green
}

Write-Host "`n🎉 Package manager blocking test completed!" -ForegroundColor Green
