<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>⏰ 定时任务管理中心</title>
    <script src="https://cdn.tailwindcss.com"></script>
    <style>
        /* 状态指示器动画 */
        .status-indicator {
            width: 12px;
            height: 12px;
            border-radius: 50%;
            display: inline-block;
            margin-right: 8px;
        }

        .status-active {
            background-color: #10b981;
            animation: pulse 2s infinite;
        }

        .status-inactive {
            background-color: #ef4444;
        }

        .status-disabled {
            background-color: #6b7280;
        }

        @keyframes pulse {
            0%, 100% { opacity: 1; }
            50% { opacity: 0.5; }
        }

        /* 开关样式 */
        .switch {
            position: relative;
            display: inline-block;
            width: 50px;
            height: 28px;
        }

        .switch input {
            opacity: 0;
            width: 0;
            height: 0;
        }

        .slider {
            position: absolute;
            cursor: pointer;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            background-color: #ccc;
            transition: .4s;
            border-radius: 28px;
        }

        .slider:before {
            position: absolute;
            content: "";
            height: 20px;
            width: 20px;
            left: 4px;
            bottom: 4px;
            background-color: white;
            transition: .4s;
            border-radius: 50%;
        }

        input:checked + .slider {
            background-color: #10b981;
        }

        input:checked + .slider:before {
            transform: translateX(22px);
        }

        /* 环境标签样式 */
        .env-tag {
            display: inline-block;
            padding: 2px 8px;
            border-radius: 12px;
            font-size: 12px;
            font-weight: 500;
            margin: 2px;
        }

        .env-prod {
            background-color: #fef3c7;
            color: #d97706;
        }

        .env-local {
            background-color: #dbeafe;
            color: #2563eb;
        }

        .env-dev {
            background-color: #dcfce7;
            color: #16a34a;
        }

        /* 加载动画 */
        .loading {
            animation: spin 1s linear infinite;
        }

        @keyframes spin {
            from { transform: rotate(0deg); }
            to { transform: rotate(360deg); }
        }
    </style>
</head>
<body class="bg-gradient-to-br from-gray-50 via-blue-50 to-indigo-50 min-h-screen">
    <!-- 背景装饰 -->
    <div class="absolute inset-0 overflow-hidden">
        <div class="absolute -top-40 -right-40 w-80 h-80 bg-blue-100 rounded-full mix-blend-multiply filter blur-xl opacity-30 animate-pulse"></div>
        <div class="absolute -bottom-40 -left-40 w-80 h-80 bg-indigo-100 rounded-full mix-blend-multiply filter blur-xl opacity-30 animate-pulse"></div>
    </div>

    <!-- 主容器 -->
    <div class="relative z-10 container mx-auto px-4 py-8">
        <!-- 头部 -->
        <header class="text-center mb-12">
            <h1 class="text-5xl font-bold text-gray-800 mb-4">
                ⏰ <span class="bg-gradient-to-r from-blue-600 to-indigo-600 bg-clip-text text-transparent">定时任务管理中心</span>
            </h1>
            <p class="text-xl text-gray-600 mb-2">智能定时任务控制面板</p>
            <p class="text-sm text-gray-500">最后更新: <span id="lastUpdate">{[ timestamp ]}</span></p>
        </header>

        <!-- 统计信息 -->
        <div class="grid grid-cols-1 md:grid-cols-4 gap-6 mb-8">
            <div class="bg-white/80 backdrop-blur-lg rounded-xl p-6 text-center shadow-lg border border-gray-200">
                <div class="text-3xl font-bold text-gray-800" id="totalTasks">{[ schedules | length ]}</div>
                <div class="text-gray-600">总任务数</div>
            </div>
            <div class="bg-white/80 backdrop-blur-lg rounded-xl p-6 text-center shadow-lg border border-gray-200">
                <div class="text-3xl font-bold text-green-600" id="activeTasks">
                    {% set activeCount = 0 %}
                    {% for schedule in schedules %}
                        {% if schedule.enabled %}
                            {% set activeCount = activeCount + 1 %}
                        {% endif %}
                    {% endfor %}
                    {[ activeCount ]}
                </div>
                <div class="text-gray-600">已启用</div>
            </div>
            <div class="bg-white/80 backdrop-blur-lg rounded-xl p-6 text-center shadow-lg border border-gray-200">
                <div class="text-3xl font-bold text-red-600" id="inactiveTasks">
                    {% set inactiveCount = 0 %}
                    {% for schedule in schedules %}
                        {% if not schedule.enabled %}
                            {% set inactiveCount = inactiveCount + 1 %}
                        {% endif %}
                    {% endfor %}
                    {[ inactiveCount ]}
                </div>
                <div class="text-gray-600">已禁用</div>
            </div>
            <div class="bg-white/80 backdrop-blur-lg rounded-xl p-6 text-center shadow-lg border border-gray-200">
                <div class="text-3xl font-bold text-blue-600" id="prodTasks">
                    {% set prodCount = 0 %}
                    {% for schedule in schedules %}
                        {% if 'prod' in schedule.env %}
                            {% set prodCount = prodCount + 1 %}
                        {% endif %}
                    {% endfor %}
                    {[ prodCount ]}
                </div>
                <div class="text-gray-600">生产环境</div>
            </div>
        </div>

        <!-- 操作按钮 -->
        <div class="flex flex-wrap justify-center gap-4 mb-8">
            <button id="refreshBtn" class="bg-blue-600 hover:bg-blue-700 text-white px-6 py-3 rounded-lg font-medium transition-colors flex items-center gap-2 shadow-lg">
                <svg class="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M4 4v5h.582m15.356 2A8.001 8.001 0 004.582 9m0 0H9m11 11v-5h-.581m0 0a8.003 8.003 0 01-15.357-2m15.357 2H15"></path>
                </svg>
                刷新数据
            </button>
            <button id="enableAllBtn" class="bg-green-600 hover:bg-green-700 text-white px-6 py-3 rounded-lg font-medium transition-colors flex items-center gap-2 shadow-lg">
                <svg class="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z"></path>
                </svg>
                启用全部
            </button>
            <button id="disableAllBtn" class="bg-red-600 hover:bg-red-700 text-white px-6 py-3 rounded-lg font-medium transition-colors flex items-center gap-2 shadow-lg">
                <svg class="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M10 14l2-2m0 0l2-2m-2 2l-2-2m2 2l2 2m7-2a9 9 0 11-18 0 9 9 0 0118 0z"></path>
                </svg>
                禁用全部
            </button>
        </div>

        <!-- 错误提示 -->
        {% if error %}
        <div class="bg-red-50 border border-red-300 text-red-800 px-4 py-3 rounded-lg mb-6 shadow-sm">
            <div class="flex items-center">
                <svg class="w-5 h-5 mr-2" fill="currentColor" viewBox="0 0 20 20">
                    <path fill-rule="evenodd" d="M18 10a8 8 0 11-16 0 8 8 0 0116 0zm-7 4a1 1 0 11-2 0 1 1 0 012 0zm-1-9a1 1 0 00-1 1v4a1 1 0 102 0V6a1 1 0 00-1-1z" clip-rule="evenodd"></path>
                </svg>
                {[ error ]}
            </div>
        </div>
        {% endif %}

        <!-- 定时任务列表 -->
        <div id="tasksContainer" class="grid grid-cols-1 lg:grid-cols-2 xl:grid-cols-3 gap-6">
            {% for schedule in schedules %}
            <div class="task-card bg-white/90 backdrop-blur-lg rounded-xl p-6 shadow-lg border border-gray-200" data-filename="{[ schedule.filename ]}">
                <!-- 任务头部 -->
                <div class="flex items-center justify-between mb-4">
                    <div class="flex items-center gap-3">
                        <div class="w-12 h-12 bg-gradient-to-r from-blue-500 to-indigo-500 rounded-full flex items-center justify-center text-white font-bold text-lg">
                            ⏰
                        </div>
                        <div>
                            <h3 class="text-gray-800 font-semibold text-lg">{[ schedule.name ]}</h3>
                            <p class="text-gray-500 text-sm">间隔: {[ schedule.interval ]}</p>
                        </div>
                    </div>
                    <label class="switch">
                        <input type="checkbox" class="task-toggle" data-filename="{[ schedule.filename ]}" {% if schedule.enabled %}checked{% endif %}>
                        <span class="slider"></span>
                    </label>
                </div>

                <!-- 任务状态 -->
                <div class="mb-4">
                    <div class="flex items-center mb-2">
                        <span class="status-indicator {% if schedule.enabled %}status-active{% else %}status-inactive{% endif %}"></span>
                        <span class="text-sm font-medium text-gray-700">
                            {% if schedule.enabled %}运行中{% else %}已停止{% endif %}
                        </span>
                    </div>
                    <div class="text-sm text-gray-600">
                        类型: {[ schedule.type ]} | 立即执行: {% if schedule.immediate %}是{% else %}否{% endif %}
                    </div>
                </div>

                <!-- 环境标签 -->
                <div class="mb-4">
                    <div class="text-sm text-gray-600 mb-2">运行环境:</div>
                    <div class="flex flex-wrap">
                        {% for env in schedule.env %}
                        <span class="env-tag env-{[ env ]}">{[ env ]}</span>
                        {% endfor %}
                        {% if schedule.env.length == 0 %}
                        <span class="env-tag" style="background-color: #f3f4f6; color: #6b7280;">全部环境</span>
                        {% endif %}
                    </div>
                </div>

                <!-- 操作按钮 -->
                <div class="space-y-2">
                    <div class="flex gap-2">
                        <button class="edit-task flex-1 bg-blue-500 hover:bg-blue-600 text-white py-2 px-3 rounded-lg text-sm font-medium transition-colors" data-filename="{[ schedule.filename ]}">
                            编辑配置
                        </button>
                        <button class="view-code bg-gray-500 hover:bg-gray-600 text-white py-2 px-3 rounded-lg text-sm font-medium transition-colors" data-filename="{[ schedule.filename ]}">
                            查看代码
                        </button>
                    </div>
                </div>
            </div>
            {% endfor %}
        </div>

        <!-- 空状态 -->
        {% if schedules.length === 0 %}
        <div class="text-center py-16">
            <div class="text-6xl mb-4">⏰</div>
            <h3 class="text-2xl font-semibold text-gray-800 mb-2">暂无定时任务</h3>
            <p class="text-gray-600 mb-6">系统中还没有配置任何定时任务</p>
        </div>
        {% endif %}
    </div>

    <!-- 编辑任务模态框 -->
    <div id="editTaskModal" class="fixed inset-0 bg-black/50 backdrop-blur-sm hidden items-center justify-center z-50">
        <div class="bg-white/95 backdrop-blur-lg rounded-xl p-8 max-w-2xl w-full mx-4 border border-gray-200 shadow-2xl max-h-[90vh] overflow-y-auto">
            <h3 class="text-2xl font-bold text-gray-800 mb-6">编辑定时任务配置</h3>
            <form id="editTaskForm">
                <input type="hidden" id="editFilename">

                <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
                    <!-- 基础配置 -->
                    <div class="space-y-4">
                        <h4 class="text-lg font-semibold text-gray-700 border-b pb-2">基础配置</h4>

                        <div>
                            <label class="block text-gray-700 text-sm font-medium mb-2">执行间隔/定时</label>
                            <div id="intervalOrCronBox">
                                <input type="text" id="editInterval" class="w-full px-4 py-3 bg-white border border-gray-300 rounded-lg text-gray-800 placeholder-gray-500 focus:outline-none focus:border-blue-500 focus:ring-2 focus:ring-blue-200" placeholder="例如: 30s, 5m, 1h 或 cron: 秒 分 时 日 月 周">
                                <div id="cronInputs" class="hidden mt-2 grid grid-cols-2 gap-2">
                                    <div><input type="text" id="cronSec" class="w-full px-2 py-1 border rounded" placeholder="秒"></div>
                                    <div><input type="text" id="cronMin" class="w-full px-2 py-1 border rounded" placeholder="分"></div>
                                    <div><input type="text" id="cronHour" class="w-full px-2 py-1 border rounded" placeholder="时"></div>
                                    <div><input type="text" id="cronDay" class="w-full px-2 py-1 border rounded" placeholder="日"></div>
                                    <div><input type="text" id="cronMonth" class="w-full px-2 py-1 border rounded" placeholder="月"></div>
                                    <div><input type="text" id="cronWeek" class="w-full px-2 py-1 border rounded" placeholder="周"></div>
                                </div>
                                <button type="button" id="toggleCronBtn" class="mt-2 text-blue-600 hover:underline text-xs">切换为 Cron 表达式输入</button>
                            </div>
                            <p class="text-xs text-gray-500 mt-1">支持: s(秒), m(分), h(小时), d(天) 或 Cron（秒 分 时 日 月 周）</p>
                        </div>

                        <div>
                            <label class="block text-gray-700 text-sm font-medium mb-2">启用状态</label>
                            <label class="switch">
                                <input type="checkbox" id="editEnabled">
                                <span class="slider"></span>
                            </label>
                            <span class="text-gray-600 ml-3">启用此定时任务</span>
                        </div>

                        <div>
                            <label class="block text-gray-700 text-sm font-medium mb-2">立即执行</label>
                            <label class="switch">
                                <input type="checkbox" id="editImmediate">
                                <span class="slider"></span>
                            </label>
                            <span class="text-gray-600 ml-3">应用启动时立即执行一次</span>
                        </div>
                    </div>

                    <!-- 环境配置 -->
                    <div class="space-y-4">
                        <h4 class="text-lg font-semibold text-gray-700 border-b pb-2">运行环境</h4>

                        <div class="space-y-3">
                            <label class="flex items-center">
                                <input type="checkbox" class="env-checkbox" value="prod" class="mr-2">
                                <span class="ml-2 text-gray-700">生产环境 (prod)</span>
                            </label>
                            <label class="flex items-center">
                                <input type="checkbox" class="env-checkbox" value="local" class="mr-2">
                                <span class="ml-2 text-gray-700">本地环境 (local)</span>
                            </label>
                            <label class="flex items-center">
                                <input type="checkbox" class="env-checkbox" value="dev" class="mr-2">
                                <span class="ml-2 text-gray-700">开发环境 (dev)</span>
                            </label>
                            <label class="flex items-center">
                                <input type="checkbox" class="env-checkbox" value="test" class="mr-2">
                                <span class="ml-2 text-gray-700">测试环境 (test)</span>
                            </label>
                        </div>

                        <p class="text-xs text-gray-500">选择任务运行的环境，不选择表示在所有环境运行</p>
                    </div>
                </div>

                <div class="flex gap-4 mt-8">
                    <button type="button" id="cancelEditTask" class="flex-1 bg-gray-500 hover:bg-gray-600 text-white py-3 rounded-lg font-medium transition-colors shadow-md">
                        取消
                    </button>
                    <button type="submit" class="flex-1 bg-blue-600 hover:bg-blue-700 text-white py-3 rounded-lg font-medium transition-colors shadow-md">
                        保存配置
                    </button>
                </div>
            </form>
        </div>
    </div>

    <!-- 查看代码模态框 -->
    <div id="viewCodeModal" class="fixed inset-0 bg-black/50 backdrop-blur-sm hidden items-center justify-center z-50">
        <div class="bg-white/95 backdrop-blur-lg rounded-xl p-8 max-w-4xl w-full mx-4 border border-gray-200 shadow-2xl max-h-[90vh] overflow-y-auto">
            <div class="flex justify-between items-center mb-6">
                <h3 class="text-2xl font-bold text-gray-800">查看任务代码</h3>
                <button id="closeCodeModal" class="text-gray-500 hover:text-gray-700">
                    <svg class="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 18L18 6M6 6l12 12"></path>
                    </svg>
                </button>
            </div>
            <div class="bg-gray-900 rounded-lg p-4 overflow-x-auto">
                <pre id="codeContent" class="text-green-400 text-sm font-mono whitespace-pre-wrap"></pre>
            </div>
        </div>
    </div>

    <!-- 加载提示 -->
    <div id="loadingOverlay" class="fixed inset-0 bg-black/50 backdrop-blur-sm hidden items-center justify-center z-50">
        <div class="bg-white/95 backdrop-blur-lg rounded-xl p-8 text-center shadow-2xl border border-gray-200">
            <div class="loading w-8 h-8 border-4 border-blue-500 border-t-transparent rounded-full mx-auto mb-4"></div>
            <div class="text-gray-800 font-medium">处理中...</div>
        </div>
    </div>

    <script>
        // 全局变量
        let schedules = {[ schedules | dump | safe ]};

        // DOM元素
        const tasksContainer = document.getElementById('tasksContainer');
        const editTaskModal = document.getElementById('editTaskModal');
        const viewCodeModal = document.getElementById('viewCodeModal');
        const loadingOverlay = document.getElementById('loadingOverlay');

        // 初始化
        document.addEventListener('DOMContentLoaded', function() {
            bindEvents();
            updateStatistics();
        });

        // 绑定事件
        function bindEvents() {
            // 刷新按钮
            document.getElementById('refreshBtn').addEventListener('click', refreshData);

            // 批量操作按钮
            document.getElementById('enableAllBtn').addEventListener('click', () => toggleAllTasks(true));
            document.getElementById('disableAllBtn').addEventListener('click', () => toggleAllTasks(false));

            // 任务开关切换
            document.addEventListener('change', function(e) {
                if (e.target.classList.contains('task-toggle')) {
                    handleTaskToggle(e.target);
                }
            });

            // 编辑任务按钮
            document.addEventListener('click', function(e) {
                if (e.target.classList.contains('edit-task')) {
                    showEditTaskModal(e.target.dataset.filename);
                }
                if (e.target.classList.contains('view-code')) {
                    showCodeModal(e.target.dataset.filename);
                }
            });

            // 模态框关闭
            document.getElementById('cancelEditTask').addEventListener('click', hideEditTaskModal);
            document.getElementById('closeCodeModal').addEventListener('click', hideCodeModal);
            document.getElementById('editTaskForm').addEventListener('submit', handleTaskUpdate);
        }

        // 显示加载状态
        function showLoading() {
            loadingOverlay.classList.remove('hidden');
            loadingOverlay.classList.add('flex');
        }

        // 隐藏加载状态
        function hideLoading() {
            loadingOverlay.classList.add('hidden');
            loadingOverlay.classList.remove('flex');
        }

        // 刷新数据
        async function refreshData() {
            var refreshBtn = document.getElementById('refreshBtn');
            refreshBtn.disabled = true;
            var oldHtml = refreshBtn.innerHTML;
            refreshBtn.innerHTML = '<span class="loading w-5 h-5 border-2 border-white border-t-transparent rounded-full inline-block mr-2"></span>刷新中...';
            try {
                const response = await fetch('/api/schedule/list');
                const result = await response.json();

                if (result.success) {
                    schedules = result.data;
                    renderTasks();
                    updateStatistics();
                    updateLastUpdateTime();
                    showNotification('数据刷新成功', 'success');
                } else {
                    showNotification('刷新失败: ' + result.message, 'error');
                }
            } catch (error) {
                console.error('刷新数据失败:', error);
                showNotification('网络错误，刷新失败', 'error');
            } finally {
                refreshBtn.disabled = false;
                refreshBtn.innerHTML = oldHtml;
            }
        }

        // 立即执行任务
        async function executeTaskNow(filename) {
            if (!confirm(`确定要立即执行任务 "${filename}" 吗？`)) {
                return;
            }

            showLoading();
            try {
                const response = await fetch('/api/schedule/execute', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                    },
                    body: JSON.stringify({
                        filename: filename
                    })
                });

                const result = await response.json();

                if (result.success) {
                    showNotification(`任务 "${filename}" 执行成功`, 'success');
                } else {
                    showNotification('执行失败: ' + result.message, 'error');
                }
            } catch (error) {
                console.error('执行任务失败:', error);
                showNotification('网络错误，执行失败', 'error');
            } finally {
                hideLoading();
            }
        }

        // 处理任务开关切换
        async function handleTaskToggle(toggle) {
            const filename = toggle.dataset.filename;
            const enabled = toggle.checked;

            // 只禁用按钮，不全屏 loading
            toggle.disabled = true;
            try {
                const response = await fetch('/api/schedule/update', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                    },
                    body: JSON.stringify({
                        filename: filename,
                        config: { enabled: enabled }
                    })
                });

                const result = await response.json();

                if (result.success) {
                    // 更新本地数据
                    const schedule = schedules.find(s => s.filename === filename);
                    if (schedule) {
                        schedule.enabled = enabled;
                        updateTaskCard(filename, schedule);
                        updateStatistics();
                    }
                    showNotification(`任务${enabled ? '启用' : '禁用'}成功`, 'success');
                } else {
                    // 恢复开关状态
                    toggle.checked = !enabled;
                    showNotification('操作失败: ' + result.message, 'error');
                }
            } catch (error) {
                console.error('切换任务状态失败:', error);
                toggle.checked = !enabled;
                showNotification('网络错误，操作失败', 'error');
            } finally {
                toggle.disabled = false;
            }
        }

        // 批量切换任务
        async function toggleAllTasks(enabled) {
            if (!confirm(`确定要${enabled ? '启用' : '禁用'}所有定时任务吗？`)) {
                return;
            }

            showLoading();
            let successCount = 0;
            let failCount = 0;

            for (const schedule of schedules) {
                if (schedule.enabled !== enabled) {
                    try {
                        const response = await fetch('/api/schedule/update', {
                            method: 'POST',
                            headers: {
                                'Content-Type': 'application/json',
                            },
                            body: JSON.stringify({
                                filename: schedule.filename,
                                config: { enabled: enabled }
                            })
                        });

                        const result = await response.json();
                        if (result.success) {
                            schedule.enabled = enabled;
                            successCount++;
                        } else {
                            failCount++;
                        }
                    } catch (error) {
                        failCount++;
                    }
                }
            }

            renderTasks();
            updateStatistics();
            hideLoading();

            if (failCount === 0) {
                showNotification(`成功${enabled ? '启用' : '禁用'}了 ${successCount} 个任务`, 'success');
            } else {
                showNotification(`操作完成：成功 ${successCount} 个，失败 ${failCount} 个`, 'warning');
            }
        }

        // 显示编辑任务模态框
        function showEditTaskModal(filename) {
            const schedule = schedules.find(s => s.filename === filename);
            if (!schedule) return;

            document.getElementById('editFilename').value = filename;
            document.getElementById('editEnabled').checked = schedule.enabled;
            document.getElementById('editImmediate').checked = schedule.immediate;

            // 环境
            const envCheckboxes = document.querySelectorAll('.env-checkbox');
            envCheckboxes.forEach(checkbox => {
                checkbox.checked = schedule.env.includes(checkbox.value);
            });

            // 判断 interval 还是 cron
            const intervalInput = document.getElementById('editInterval');
            const cronInputs = document.getElementById('cronInputs');
            const toggleCronBtn = document.getElementById('toggleCronBtn');
            let isCron = false;
            let cronArr = ["*", "*", "*", "*", "*", "*"];
            if (schedule.interval && schedule.interval.startsWith('cron:')) {
                isCron = true;
                let cronStr = schedule.interval.replace('cron:', '').trim();
                cronArr = cronStr.split(/\s+/);
                while (cronArr.length < 6) cronArr.push('*');
            }
            if (isCron) {
                intervalInput.classList.add('hidden');
                cronInputs.classList.remove('hidden');
                document.getElementById('cronSec').value = cronArr[0];
                document.getElementById('cronMin').value = cronArr[1];
                document.getElementById('cronHour').value = cronArr[2];
                document.getElementById('cronDay').value = cronArr[3];
                document.getElementById('cronMonth').value = cronArr[4];
                document.getElementById('cronWeek').value = cronArr[5];
                toggleCronBtn.textContent = '切换为普通间隔输入';
            } else {
                intervalInput.classList.remove('hidden');
                cronInputs.classList.add('hidden');
                intervalInput.value = schedule.interval && !schedule.interval.startsWith('cron:') ? schedule.interval : '';
                toggleCronBtn.textContent = '切换为 Cron 表达式输入';
            }
            toggleCronBtn.onclick = function() {
                if (intervalInput.classList.contains('hidden')) {
                    intervalInput.classList.remove('hidden');
                    cronInputs.classList.add('hidden');
                    toggleCronBtn.textContent = '切换为 Cron 表达式输入';
                } else {
                    intervalInput.classList.add('hidden');
                    cronInputs.classList.remove('hidden');
                    toggleCronBtn.textContent = '切换为普通间隔输入';
                }
            };

            editTaskModal.classList.remove('hidden');
            editTaskModal.classList.add('flex');
        }

        // 隐藏编辑任务模态框
        function hideEditTaskModal() {
            editTaskModal.classList.add('hidden');
            editTaskModal.classList.remove('flex');
        }

        // 处理任务更新
        async function handleTaskUpdate(e) {
            e.preventDefault();

            const filename = document.getElementById('editFilename').value;
            const enabled = document.getElementById('editEnabled').checked;
            const immediate = document.getElementById('editImmediate').checked;
            const intervalInput = document.getElementById('editInterval');
            const cronInputs = document.getElementById('cronInputs');
            let interval = '';
            let config = { enabled, immediate };
            if (!intervalInput.classList.contains('hidden')) {
                interval = intervalInput.value.trim();
                config.interval = interval;
                config.cron = undefined;
            } else {
                // 拼接 cron
                const sec = document.getElementById('cronSec').value.trim() || '*';
                const min = document.getElementById('cronMin').value.trim() || '*';
                const hour = document.getElementById('cronHour').value.trim() || '*';
                const day = document.getElementById('cronDay').value.trim() || '*';
                const month = document.getElementById('cronMonth').value.trim() || '*';
                const week = document.getElementById('cronWeek').value.trim() || '*';
                const cronStr = `${sec} ${min} ${hour} ${day} ${month} ${week}`;
                config.interval = undefined;
                config.cron = cronStr;
            }
            // 环境
            const selectedEnvs = Array.from(document.querySelectorAll('.env-checkbox:checked')).map(cb => cb.value);
            config.env = selectedEnvs;

            showLoading();
            try {
                const response = await fetch('/api/schedule/update', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                    },
                    body: JSON.stringify({
                        filename: filename,
                        config: config
                    })
                });

                const result = await response.json();

                if (result.success) {
                    // 更新本地数据
                    const schedule = schedules.find(s => s.filename === filename);
                    if (schedule) {
                        Object.assign(schedule, config);
                        updateTaskCard(filename, schedule);
                        updateStatistics();
                    }
                    hideEditTaskModal();
                    showNotification('任务配置更新成功', 'success');
                } else {
                    showNotification('更新失败: ' + result.message, 'error');
                }
            } catch (error) {
                console.error('更新任务配置失败:', error);
                showNotification('网络错误，更新失败', 'error');
            } finally {
                hideLoading();
            }
        }

        // 显示代码模态框
        function showCodeModal(filename) {
            const schedule = schedules.find(s => s.filename === filename);
            if (!schedule) return;

            document.getElementById('codeContent').textContent = schedule.content || '// 代码内容获取失败';
            viewCodeModal.classList.remove('hidden');
            viewCodeModal.classList.add('flex');
        }

        // 隐藏代码模态框
        function hideCodeModal() {
            viewCodeModal.classList.add('hidden');
            viewCodeModal.classList.remove('flex');
        }

        // 渲染任务列表
        function renderTasks() {
            if (schedules.length === 0) {
                tasksContainer.innerHTML = `
                    <div class="col-span-full text-center py-16">
                        <div class="text-6xl mb-4">⏰</div>
                        <h3 class="text-2xl font-semibold text-gray-800 mb-2">暂无定时任务</h3>
                        <p class="text-gray-600 mb-6">系统中还没有配置任何定时任务</p>
                    </div>
                `;
                return;
            }

            tasksContainer.innerHTML = schedules.map(schedule => {
                const envTags = schedule.env.length > 0
                    ? schedule.env.map(env => `<span class="env-tag env-${env}">${env}</span>`).join('')
                    : '<span class="env-tag" style="background-color: #f3f4f6; color: #6b7280;">全部环境</span>';

                return `
                    <div class="task-card bg-white/90 backdrop-blur-lg rounded-xl p-6 shadow-lg border border-gray-200" data-filename="${schedule.filename}">
                        <div class="flex items-center justify-between mb-4">
                            <div class="flex items-center gap-3">
                                <div class="w-12 h-12 bg-gradient-to-r from-blue-500 to-indigo-500 rounded-full flex items-center justify-center text-white font-bold text-lg">
                                    ⏰
                                </div>
                                <div>
                                    <h3 class="text-gray-800 font-semibold text-lg">${schedule.name}</h3>
                                    <p class="text-gray-500 text-sm">间隔: ${schedule.interval}</p>
                                </div>
                            </div>
                            <label class="switch">
                                <input type="checkbox" class="task-toggle" data-filename="${schedule.filename}" ${schedule.enabled ? 'checked' : ''}>
                                <span class="slider"></span>
                            </label>
                        </div>
                        <div class="mb-4">
                            <div class="flex items-center mb-2">
                                <span class="status-indicator ${schedule.enabled ? 'status-active' : 'status-inactive'}"></span>
                                <span class="text-sm font-medium text-gray-700">
                                    ${schedule.enabled ? '运行中' : '已停止'}
                                </span>
                            </div>
                            <div class="text-sm text-gray-600">
                                类型: ${schedule.type} | 立即执行: ${schedule.immediate ? '是' : '否'}
                            </div>
                        </div>
                        <div class="mb-4">
                            <div class="text-sm text-gray-600 mb-2">运行环境:</div>
                            <div class="flex flex-wrap">
                                ${envTags}
                            </div>
                        </div>
                        <div class="space-y-2">
                            <div class="flex gap-2">
                                <button class="edit-task flex-1 bg-blue-500 hover:bg-blue-600 text-white py-2 px-3 rounded-lg text-sm font-medium transition-colors" data-filename="${schedule.filename}">
                                    编辑配置
                                </button>
                                <button class="view-code bg-gray-500 hover:bg-gray-600 text-white py-2 px-3 rounded-lg text-sm font-medium transition-colors" data-filename="${schedule.filename}">
                                    查看代码
                                </button>
                            </div>
                        </div>
                    </div>
                `;
            }).join('');
        }

        // 更新单个任务卡片
        function updateTaskCard(filename, schedule) {
            const card = document.querySelector(`[data-filename="${filename}"]`);
            if (!card) return;

            const toggle = card.querySelector('.task-toggle');
            const statusIndicator = card.querySelector('.status-indicator');
            const statusText = card.querySelector('.text-sm.font-medium');

            toggle.checked = schedule.enabled;
            statusIndicator.className = `status-indicator ${schedule.enabled ? 'status-active' : 'status-inactive'}`;
            statusText.textContent = schedule.enabled ? '运行中' : '已停止';
        }

        // 更新统计信息
        function updateStatistics() {
            const total = schedules.length;
            const active = schedules.filter(s => s.enabled).length;
            const inactive = total - active;
            const prod = schedules.filter(s => s.env.includes('prod')).length;

            document.getElementById('totalTasks').textContent = total;
            document.getElementById('activeTasks').textContent = active;
            document.getElementById('inactiveTasks').textContent = inactive;
            document.getElementById('prodTasks').textContent = prod;
        }

        // 更新最后更新时间
        function updateLastUpdateTime() {
            document.getElementById('lastUpdate').textContent = new Date().toLocaleString('zh-CN');
        }

        // 显示通知
        function showNotification(message, type = 'info') {
            // 创建通知元素
            const notification = document.createElement('div');
            notification.className = `fixed top-4 right-4 z-50 px-6 py-4 rounded-lg shadow-lg transition-all duration-300 transform translate-x-full`;

            // 根据类型设置样式
            switch (type) {
                case 'success':
                    notification.className += ' bg-green-500 text-white';
                    break;
                case 'error':
                    notification.className += ' bg-red-500 text-white';
                    break;
                case 'warning':
                    notification.className += ' bg-yellow-500 text-white';
                    break;
                default:
                    notification.className += ' bg-blue-500 text-white';
            }

            notification.textContent = message;
            document.body.appendChild(notification);

            // 显示动画
            setTimeout(() => {
                notification.classList.remove('translate-x-full');
            }, 100);

            // 自动隐藏
            setTimeout(() => {
                notification.classList.add('translate-x-full');
                setTimeout(() => {
                    document.body.removeChild(notification);
                }, 300);
            }, 3000);
        }
    </script>
</body>
</html>
