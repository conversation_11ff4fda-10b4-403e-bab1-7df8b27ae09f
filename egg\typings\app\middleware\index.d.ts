// This file is created by egg-ts-helper@2.1.1
// Do not modify this file!!!!!!!!!
/* eslint-disable */

import 'egg';
import ExportAuth = require('../../../app/middleware/auth');
import ExportErrorHandler = require('../../../app/middleware/errorHandler');
import ExportHealthCheck = require('../../../app/middleware/health_check');
import ExportLogger = require('../../../app/middleware/logger');
import ExportPerformance = require('../../../app/middleware/performance');
import ExportProductionMonitor = require('../../../app/middleware/production-monitor');
import ExportRobot = require('../../../app/middleware/robot');

declare module 'egg' {
  interface IMiddleware {
    auth: typeof ExportAuth;
    errorHandler: typeof ExportErrorHandler;
    healthCheck: typeof ExportHealthCheck;
    logger: typeof ExportLogger;
    performance: typeof ExportPerformance;
    productionMonitor: typeof ExportProductionMonitor;
    robot: typeof ExportRobot;
  }
}
