<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="referrer" content="no-referrer" />
    <title>Title</title>
</head>
<script src="https://libs.baidu.com/jquery/2.1.1/jquery.js"></script>>
<body>

First name:<br>
<input type="text" name="type" value="XiuRen" class="type">
<br>
<input type="text" name="id" value="" class="id">
<br>
<input type="submit" value="Submit" class="nextButton">

<p>如果您点击提交，表单数据会被发送到名为 demo_form.asp 的页面。</p>
<div id="hc">

</div>
<script>
    $(".nextButton").click(function (data) {
        $("#hc").empty();
        var type = $(".type").val();
        var id = $(".id").val();
        // console.log(type);
        // console.log(id);
        $.get('/xr/info', {
            id: id,
            type: type
        }, function (result) {
            // $("#hc").append(result).append('<br>');
            // console.log(result);
            // var obj = JSON.parse(result);
            var page = result;
            console.log(page);
            for (var i = 0; i < page; i++) {
                $.get('/xr', {
                    id: id,
                    type: type,
                    page: i
                }, function (result) {
                    // $("#hc").append(result).append('<br>');
                    // var obj = JSON.parse(result);
                    var page = result.length;
                    for (var ii = 0; ii < page; ii++) {
                        $("#hc").append("<img src=" + result[ii] + ">");
                        console.log(result[ii]);
                    }
                    // console.log(page);
                    $(".id").val("");
                });
            }

        });

    });
</script>
</body>
</html>