/**
 * 题目排名获取API
 * 迁移自egg项目的fb.js gettimurank方法
 */

const { NextResponse } = require('next/server');
const { app } = require('../../../../lib/app');

// 确保应用已初始化
let appInitialized = false;

async function ensureAppInitialized() {
  if (!appInitialized) {
    await app.init();
    appInitialized = true;
  }
}

async function GET(request) {
  try {
    await ensureAppInitialized();
    
    const { searchParams } = new URL(request.url);
    const biao = searchParams.get('biao') || 'fbsy';
    const id = searchParams.get('id');
    const allcateid = searchParams.get('allcateid');
    
    console.log('📊 获取题目排名 - 参数:', { biao, id, allcateid });
    
    if (!id) {
      return NextResponse.json({ 
        error: '缺少id参数' 
      }, { status: 400 });
    }
    
    // 获取当前题目信息
    const currentTimu = await app.mysql.get(biao, { id: parseInt(id) });
    
    if (!currentTimu) {
      return NextResponse.json({ 
        error: '题目不存在' 
      }, { status: 404 });
    }
    
    let rankInfo = {
      currentRank: 0,
      totalCount: 0,
      categoryRank: 0,
      categoryCount: 0
    };
    
    try {
      // 计算总排名（基于ID）
      const totalRankSql = `SELECT COUNT(*) as rank FROM ${biao} WHERE id <= ?`;
      const totalRankResult = await app.mysql.query(totalRankSql, [parseInt(id)]);
      rankInfo.currentRank = totalRankResult[0].rank;
      
      // 获取总题目数
      const totalCountSql = `SELECT COUNT(*) as total FROM ${biao}`;
      const totalCountResult = await app.mysql.query(totalCountSql);
      rankInfo.totalCount = totalCountResult[0].total;
      
      // 如果有分类信息，计算分类排名
      if (allcateid && currentTimu.allcateid) {
        const categoryRankSql = `SELECT COUNT(*) as rank FROM ${biao} WHERE id <= ? AND allcateid LIKE ?`;
        const categoryRankResult = await app.mysql.query(categoryRankSql, [parseInt(id), `%${allcateid}%`]);
        rankInfo.categoryRank = categoryRankResult[0].rank;
        
        const categoryCountSql = `SELECT COUNT(*) as total FROM ${biao} WHERE allcateid LIKE ?`;
        const categoryCountResult = await app.mysql.query(categoryCountSql, [`%${allcateid}%`]);
        rankInfo.categoryCount = categoryCountResult[0].total;
      }
      
      console.log('✅ 排名计算完成:', rankInfo);
      
    } catch (error) {
      console.error('❌ 排名计算失败:', error.message);
      // 如果排名计算失败，返回默认值
    }
    
    // 构建排名字符串
    let rankStr = '';
    if (rankInfo.totalCount > 0) {
      rankStr = `${rankInfo.currentRank}/${rankInfo.totalCount}`;
      
      if (rankInfo.categoryCount > 0) {
        rankStr += ` (分类: ${rankInfo.categoryRank}/${rankInfo.categoryCount})`;
      }
    }
    
    // 返回与egg项目一致的格式: { res: [...] }
    return NextResponse.json({
      res: [{
        rank: rankInfo.currentRank,
        name: `题目${id}`,
        cateid: parseInt(id),
        total: rankInfo.totalCount,
        categoryRank: rankInfo.categoryRank,
        categoryTotal: rankInfo.categoryCount,
        rankString: rankStr,
        timu: currentTimu
      }]
    });
    
  } catch (error) {
    console.error('❌ gettimurank API错误:', error.message);
    return NextResponse.json({ 
      error: error.message,
      timestamp: new Date().toISOString()
    }, { status: 500 });
  }
}

module.exports = { GET };
