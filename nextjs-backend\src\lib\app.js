/**
 * 全局应用对象
 * 提供类似egg项目的app.mysql、app.redis等接口
 */

const { initDatabase, mysql } = require('./database');
const { initRedis, redis } = require('./redis');

class Application {
  constructor() {
    this.mysql = null;
    this.redis = null;
    this.initialized = false;
  }

  /**
   * 初始化应用
   */
  async init() {
    if (this.initialized) {
      return this;
    }

    try {
      // 初始化数据库
      initDatabase();
      this.mysql = mysql;

      // 初始化Redis
      initRedis();
      this.redis = redis;

      console.log('✅ 应用初始化成功');
      this.initialized = true;
      return this;
    } catch (error) {
      console.error('❌ 应用初始化失败:', error.message);
      throw error;
    }
  }



  /**
   * 健康检查
   */
  async healthCheck() {
    const health = {
      status: 'healthy',
      timestamp: new Date().toISOString(),
      services: {}
    };

    try {
      // 检查MySQL
      if (this.mysql) {
        const mysqlHealth = await this.mysql.healthCheck();
        health.services.mysql = mysqlHealth;
      }

      // 检查Redis
      if (this.redis) {
        const redisHealth = await this.redis.healthCheck();
        health.services.redis = redisHealth;
        if (redisHealth.status !== 'healthy') {
          health.status = 'degraded';
        }
      }

      return health;
    } catch (error) {
      health.status = 'unhealthy';
      health.error = error.message;
      return health;
    }
  }

  /**
   * 关闭应用
   */
  async close() {
    try {
      if (this.mysql) {
        await this.mysql.closePool();
      }
      
      if (this.redis) {
        const { closeRedis } = require('./redis');
        await closeRedis();
      }
      
      console.log('✅ 应用已关闭');
    } catch (error) {
      console.error('❌ 应用关闭失败:', error.message);
      throw error;
    }
  }
}

// 创建全局应用实例
const app = new Application();

module.exports = {
  Application,
  app
};
