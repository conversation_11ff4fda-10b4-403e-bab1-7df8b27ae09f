<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8"/>
    <meta name="viewport" content="width=device-width,initial-scale=1.0"/>
    <script src="/public/vue/vue.min.js"></script>
    <script src="/public/element/index.js"></script>
    <link rel="stylesheet" href="/public/element/index.css">
    <script src="/public/js/axios.min.js"></script>
    <title>Element Plus demo</title>
</head>
<body>
<div id="app">
</div>

<script>
  new Vue({
    el: '#app',
    data: function () {
      return {
        imglist : []
      }
    },
    created() {
    },
    mounted() {
    },
    methods: {

    }
  })
</script>

<style>

</style>
</body>
</html>