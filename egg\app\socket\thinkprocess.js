'use strict';

module.exports = (app) => {
  const { router, controller, io } = app;
  
  // 模拟思考数据（一个完整的思考过程）
  const mockThinkingData = [
    "🧠 嗯，用户这次又发来了一道行测类比推理题，要求我用小学生都能理解的生动方式讲解秒杀技巧。题目是'双肩包：斜挎包：手提包'找同类选项，正确答案是C。\n\n这道题的关键在于发现题干三个词都是按使用方式分类的背包类型。题干破绽很明显：其他选项要么分类标准混乱（如A项按材质和功能混搭），要么包含错误类别（如B项的金星木星根本不是恒星）。C项三个工程机械都是按功能分类，完美匹配。\n\n💡 考场操作实录（10秒内）：\n第1秒：锁定'并列同类项'题干特征；\n第3秒：发现C项三项机械都是功能分类；\n第5秒：快速枪毙ABD——尤其要强调D项的'淡水'是属性而非实体物品的分类。\n\n⚡️ 秒杀思路：\n先用分类标准统一性秒排除A（材质功能混杂）；\n再用常识硬伤排除B（金星木星不是恒星）；\n最后用逻辑一致性锁定C（三项都是工程机械功能分类）。\n\n🎯 最终答案：C选项"
  ];

  const mockAnswerData = [
    "🎉 解题完成！最终答案：C选项（类比推理题）"
  ];

  // 监听连接事件
  io.of('/thinkprocess').on('connection', async (socket) => {
    console.log('✅ 客户端连接到thinkprocess命名空间:', socket.id);
    
    // 发送连接成功消息
    socket.emit('connected', {
      message: '已连接到思考过程监听器',
      socketId: socket.id,
      timestamp: new Date().toISOString()
    });

    // 模拟发送思考过程数据（初始不自动开始）
    let thinkingIndex = 0;
    let thinkingInterval = null;
    
    // 监听断开连接
    socket.on('disconnect', () => {
      console.log('❌ 客户端断开thinkprocess命名空间连接:', socket.id);
      clearInterval(thinkingInterval); // 清理定时器
    });
    
    // 监听ping消息
    socket.on('ping', (data) => {
      console.log('收到ping消息:', data);
      socket.emit('pong', `收到ping: ${data}`);
    });

    // 监听重新开始模拟的请求
    socket.on('restart-mock', (data) => {
      console.log('🔄 收到重新开始模拟请求');
      console.log('📝 请求数据:', data);
      console.log('🔄 重新开始模拟数据');
      
      // 清除之前的定时器
      if (thinkingInterval) {
        clearInterval(thinkingInterval);
        thinkingInterval = null;
      }
      
      // 发送思考数据（只发送一次）
      const thinkingContent = mockThinkingData[0];
      console.log('🧠 发送模拟思考数据（一次性）');
      
      socket.emit('thinking', { 
        content: thinkingContent,
        timestamp: new Date().toISOString()
      });
      
      app.redis.set('thinking_process', JSON.stringify({ 
        content: thinkingContent, 
        time: Date.now() 
      }));
      
      // 思考完成后发送答案（只发送一次）
      setTimeout(() => {
        const answerContent = mockAnswerData[0];
        console.log('💭 发送模拟答案数据（一次性）');
        
        socket.emit('answer', { 
          content: answerContent,
          timestamp: new Date().toISOString()
        });
        
        // 保存答案到Redis
        app.redis.set('thinking_answer', JSON.stringify({ 
          content: answerContent, 
          time: Date.now() 
        }));
      }, 3000); // 思考完成后3秒发送答案
    });

    // 监听清空所有模拟数据的请求
    socket.on('clear-mock-data', () => {
      console.log('🗑️ 收到清空所有模拟数据请求');
      
      // 清空Redis缓存
      app.redis.del('thinking_process');
      app.redis.del('thinking_answer');
      
      // 停止当前的模拟数据发送
      if (thinkingInterval) {
        clearInterval(thinkingInterval);
        thinkingInterval = null;
      }
      thinkingIndex = 0;
      
      console.log('✅ 已清空所有模拟数据');
    });
  });
}; 