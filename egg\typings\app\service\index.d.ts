// This file is created by egg-ts-helper@2.1.1
// Do not modify this file!!!!!!!!!
/* eslint-disable */

import 'egg';
type AnyClass = new (...args: any[]) => any;
type AnyFunc<T = any> = (...args: any[]) => T;
type CanExportFunc = AnyFunc<Promise<any>> | AnyFunc<IterableIterator<any>>;
type AutoInstanceType<T, U = T extends CanExportFunc ? T : T extends AnyFunc ? ReturnType<T> : T> = U extends AnyClass ? InstanceType<U> : U;
import ExportAi = require('../../../app/service/ai');
import ExportAir = require('../../../app/service/air');
import ExportDd = require('../../../app/service/dd');
import ExportDeployment = require('../../../app/service/deployment');
import ExportFeishu = require('../../../app/service/feishu');
import ExportFenbi = require('../../../app/service/fenbi');
import ExportNews = require('../../../app/service/news');
import ExportNotification = require('../../../app/service/notification');
import ExportTele = require('../../../app/service/tele');
import ExportWecom = require('../../../app/service/wecom');
import ExportXr = require('../../../app/service/xr');
import ExportXrplus = require('../../../app/service/xrplus');

declare module 'egg' {
  interface IService {
    ai: AutoInstanceType<typeof ExportAi>;
    air: AutoInstanceType<typeof ExportAir>;
    dd: AutoInstanceType<typeof ExportDd>;
    deployment: AutoInstanceType<typeof ExportDeployment>;
    feishu: AutoInstanceType<typeof ExportFeishu>;
    fenbi: AutoInstanceType<typeof ExportFenbi>;
    news: AutoInstanceType<typeof ExportNews>;
    notification: AutoInstanceType<typeof ExportNotification>;
    tele: AutoInstanceType<typeof ExportTele>;
    wecom: AutoInstanceType<typeof ExportWecom>;
    xr: AutoInstanceType<typeof ExportXr>;
    xrplus: AutoInstanceType<typeof ExportXrplus>;
  }
}
