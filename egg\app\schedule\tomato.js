const axios = require('axios');
const { dateNow } = require('../extend/helper');
module.exports = {
  schedule: {
    interval: '30m',
    type: 'all',
    env: ['prod'],
    immediate: true,
    enabled: true,
  },
  async task(ctx) {
    // let ip = await axios.get("http://ip.wcy9.com/cnip.php");
    // ip = ip.data.toString("utf8"); // 将二进制数据转换为字符串
    // console.log(ip);
    // if (this.config.vps === 'gz') {
    //   await axios
    //     .get('http://127.0.0.1:7001/updatefbsyzc?ss=1')
    //     .then((res) => {
    //       // console.log(res.data);
    //       // console.log(dateNow());
    //     })
    //     .catch((e) => {
    //       if (e) {
    //         console.log(e.message);
    //       }
    //     });
    //   let text1 = `执行ss\n${dateNow()}\n`;
    //   await ctx.service.feishu.fs3(text1);
    // }

    //如果时间是早上8点到晚上24点
    let sql = `select *
               from sw
               where name = 'tomato'`;
    let res = await ctx.service.xr.query(sql);
    let sw = res[0].sw;
    if (+sw !== 0) {
      let hours = new Date().getHours();
      hours = +hours;
      if ((hours >= 8 && hours < 12) || (hours >= 14 && hours < 23)) {
        let text = `开始工作了\n${dateNow()}\n${hours}`;
        await ctx.service.feishu.fs(text);

        // 25分钟后提醒开始休息
        setTimeout(
          async () => {
            let text1 = `25分钟休息了\n${dateNow()}\n${hours}`;
            await ctx.service.feishu.fs(text1);
          },
          25 * 60 * 1000,
        ); // 25分钟转换成毫秒
      }
    }
  },
};
