'use strict';

/**
 * @param {Egg.Application} app - egg application
 */
module.exports = (app) => {
  const { router, controller } = app;

  router.get('/fs', controller.feishu['fs']);
  router.get('/fs3', controller.feishu['fs3']);
  router.get('/fsimg', controller.feishu['fsimg']);
  router.get('/fstoken', controller.feishu['fstoken']);
  router.get('/fsupload', controller.feishu['fsupload']);
  router.post('/fsupload', controller.feishu['fsupload']);
  router.get('/fsdc', controller.feishu['fsdc']);
  router.get('/fsproxy', controller.feishu['fsproxy']);
  router.post('/fsproxy', controller.feishu['fsproxy']);
};
