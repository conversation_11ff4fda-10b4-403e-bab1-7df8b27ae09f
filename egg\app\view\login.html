<!doctype html>
<html lang="zh-CN">
  <head>
    <meta charset="UTF-8" />
    <meta name="viewport" content="width=device-width, initial-scale=1.0" />
    <title>登录</title>
    <script src="https://cdn.tailwindcss.com"></script>
  </head>
  <body
    class="min-h-screen flex items-center justify-center bg-gradient-to-br from-white via-blue-50 to-indigo-100"
  >
    <div
      class="w-full max-w-md bg-white/80 backdrop-blur-lg rounded-2xl shadow-xl p-8 border border-gray-200"
    >
      <h2 class="text-3xl font-bold text-center text-gray-800 mb-8">登录系统</h2>
      <form id="loginForm" class="space-y-6">
        <div>
          <label class="block text-gray-700 text-sm font-medium mb-2">帐号</label>
          <input
            type="text"
            id="username"
            name="username"
            class="w-full px-4 py-3 bg-white border border-gray-300 rounded-lg text-gray-800 placeholder-gray-400 focus:outline-none focus:border-blue-500 focus:ring-2 focus:ring-blue-200"
            placeholder="请输入帐号"
            required
            autocomplete="username"
          />
        </div>
        <div>
          <label class="block text-gray-700 text-sm font-medium mb-2">密码</label>
          <input
            type="password"
            id="password"
            name="password"
            class="w-full px-4 py-3 bg-white border border-gray-300 rounded-lg text-gray-800 placeholder-gray-400 focus:outline-none focus:border-blue-500 focus:ring-2 focus:ring-blue-200"
            placeholder="请输入密码"
            required
            autocomplete="current-password"
          />
        </div>
        <button
          type="submit"
          class="w-full bg-gradient-to-r from-blue-400 to-indigo-500 hover:from-blue-500 hover:to-indigo-600 text-white font-semibold py-3 rounded-lg shadow-md transition-all"
        >
          登录
        </button>
      </form>
      <div id="loginMsg" class="mt-4 text-center text-red-500 text-sm hidden"></div>
    </div>
    <script>
      document.getElementById('loginForm').addEventListener('submit', async function (e) {
        e.preventDefault();
        const username = document.getElementById('username').value.trim();
        const password = document.getElementById('password').value;
        const msg = document.getElementById('loginMsg');
        msg.classList.add('hidden');
        msg.textContent = '';
        try {
          const res = await fetch('/api/login', {
            method: 'POST',
            headers: { 'Content-Type': 'application/json' },
            body: JSON.stringify({ username, password }),
          });
          const result = await res.json();
          if (result.success) {
            // 使用服务器返回的重定向URL，如果没有则默认跳转到首页
            const redirectUrl = result.redirectUrl || '/';
            window.location.href = redirectUrl;
          } else {
            msg.textContent = result.message || '登录失败';
            msg.classList.remove('hidden');
          }
        } catch (err) {
          msg.textContent = '网络错误';
          msg.classList.remove('hidden');
        }
      });
    </script>
  </body>
</html>
