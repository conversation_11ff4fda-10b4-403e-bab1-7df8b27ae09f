const { Controller } = require('egg');
const { dayNow } = require('../extend/helper');

class AirController extends Controller {
  async all() {
    const { ctx } = this;
    let list = [
      ['XMN', '厦门', 'HGH', '杭州', '2024-12-06', 'lyxmhz1206', 1, 310],
      ['XMN', '厦门', 'HGH', '杭州', '2024-12-07', 'lyxmhz1207', 1, 310],
      ['HGH', '杭州', 'XMN', '厦门', '2024-12-08', 'lyhzxm1208', 1, 310],
      ['HGH', '杭州', 'XMN', '厦门', '2024-12-09', 'lyhzxm1209', 1, 310],
    ];
    let i = 0;

    for (let item of list) {
      i++;
      await ctx.service.air.ly(
        item[0],
        item[1],
        item[2],
        item[3],
        item[4],
        item[5],
        item[6],
        item[7],
      );
      await new Promise((resolve) => setTimeout(resolve, 620));
    }

    ctx.body = { test: i };
  }

  async lytest() {
    const { ctx } = this;
    ctx.body = await ctx.service.air.ly(
      'XMN',
      '厦门',
      'CTU',
      '成都',
      '2024-10-02',
      'lyxmcd2',
      1,
      600,
    );
  }
}

module.exports = AirController;
