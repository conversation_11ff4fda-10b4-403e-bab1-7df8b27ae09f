/**
 * 路由管理工具
 * 提供路由注册、查询、统计等功能
 */

const { 
  getRouteRewrites, 
  addRoute, 
  addRoutes, 
  findRoute, 
  getRouteStats 
} = require('../config/routes');

class RouteManager {
  constructor() {
    this.initialized = false;
  }

  /**
   * 初始化路由管理器
   */
  init() {
    if (this.initialized) return;
    
    console.log('🚀 路由管理器初始化...');
    const stats = getRouteStats();
    console.log(`📊 路由统计: 总计${stats.total}个路由`);
    console.log(`   - FB路由: ${stats.fb}个`);
    console.log(`   - Home路由: ${stats.home}个`);
    console.log(`   - 工具路由: ${stats.util}个`);
    
    this.initialized = true;
  }

  /**
   * 获取所有路由
   */
  getAllRoutes() {
    return getRouteRewrites();
  }

  /**
   * 查找路由
   */
  findRoute(source) {
    return findRoute(source);
  }

  /**
   * 添加单个路由
   */
  addRoute(source, destination, category = 'custom') {
    try {
      addRoute({ source, destination, category });
      console.log(`✅ 路由添加成功: ${source} -> ${destination}`);
      return true;
    } catch (error) {
      console.error(`❌ 路由添加失败: ${error.message}`);
      return false;
    }
  }

  /**
   * 批量添加路由
   */
  addRoutes(routes) {
    try {
      addRoutes(routes);
      console.log(`✅ 批量路由添加成功: ${routes.length}个路由`);
      return true;
    } catch (error) {
      console.error(`❌ 批量路由添加失败: ${error.message}`);
      return false;
    }
  }

  /**
   * 获取路由统计信息
   */
  getStats() {
    return getRouteStats();
  }

  /**
   * 验证路由配置
   */
  validateRoutes() {
    const routes = this.getAllRoutes();
    const issues = [];
    const sourceMap = new Map();

    routes.forEach((route, index) => {
      // 检查必需字段
      if (!route.source || !route.destination) {
        issues.push({
          type: 'missing_fields',
          index,
          route,
          message: '缺少source或destination字段'
        });
      }

      // 检查重复的source
      if (sourceMap.has(route.source)) {
        issues.push({
          type: 'duplicate_source',
          index,
          route,
          duplicate: sourceMap.get(route.source),
          message: `重复的source路径: ${route.source}`
        });
      } else {
        sourceMap.set(route.source, { index, route });
      }

      // 检查路径格式
      if (route.source && !route.source.startsWith('/')) {
        issues.push({
          type: 'invalid_format',
          index,
          route,
          message: `source路径应以/开头: ${route.source}`
        });
      }

      if (route.destination && !route.destination.startsWith('/')) {
        issues.push({
          type: 'invalid_format',
          index,
          route,
          message: `destination路径应以/开头: ${route.destination}`
        });
      }
    });

    return {
      valid: issues.length === 0,
      issues,
      totalRoutes: routes.length
    };
  }

  /**
   * 打印路由信息
   */
  printRoutes(category = null) {
    const routes = this.getAllRoutes();
    const stats = this.getStats();

    console.log('\n📋 路由配置信息:');
    console.log('='.repeat(50));

    if (category) {
      const categoryRoutes = routes.filter(r => 
        stats.categories[category] && stats.categories[category].includes(r.source)
      );
      console.log(`📂 ${category.toUpperCase()}路由 (${categoryRoutes.length}个):`);
      categoryRoutes.forEach(route => {
        console.log(`   ${route.source} -> ${route.destination}`);
      });
    } else {
      Object.keys(stats.categories).forEach(cat => {
        console.log(`📂 ${cat.toUpperCase()}路由 (${stats.categories[cat].length}个):`);
        stats.categories[cat].forEach(source => {
          const route = this.findRoute(source);
          if (route) {
            console.log(`   ${route.source} -> ${route.destination}`);
          }
        });
        console.log('');
      });
    }

    console.log('='.repeat(50));
    console.log(`📊 总计: ${stats.total}个路由\n`);
  }

  /**
   * 生成路由文档
   */
  generateDocs() {
    const routes = this.getAllRoutes();
    const stats = this.getStats();
    
    let docs = '# API路由文档\n\n';
    docs += `> 自动生成于: ${new Date().toLocaleString()}\n\n`;
    docs += `## 路由统计\n\n`;
    docs += `- 总路由数: ${stats.total}\n`;
    docs += `- FB相关: ${stats.fb}\n`;
    docs += `- Home相关: ${stats.home}\n`;
    docs += `- 工具相关: ${stats.util}\n\n`;

    Object.keys(stats.categories).forEach(category => {
      docs += `## ${category.toUpperCase()}路由\n\n`;
      docs += '| 源路径 | 目标路径 |\n';
      docs += '|--------|----------|\n';
      
      stats.categories[category].forEach(source => {
        const route = this.findRoute(source);
        if (route) {
          docs += `| \`${route.source}\` | \`${route.destination}\` |\n`;
        }
      });
      docs += '\n';
    });

    return docs;
  }
}

// 创建单例实例
const routeManager = new RouteManager();

module.exports = {
  RouteManager,
  routeManager
};
