{"server": {"port": 3000, "hostname": "127.0.0.1", "bodyParser": {"jsonLimit": "20mb", "formLimit": "20mb", "textLimit": "20mb"}}, "database": {"mysql": {"host": "************", "port": 11436, "user": "root", "password": "wangcong", "database": "zz", "charset": "utf8mb4", "connectionLimit": 10, "acquireTimeout": 60000, "timeout": 60000, "reconnect": true}}, "redis": {"host": "************", "port": 2639, "password": "", "db": 0, "connectTimeout": 10000, "maxRetriesPerRequest": 3, "retryDelayOnFailover": 100, "enableReadyCheck": false, "lazyConnect": true}, "websocket": {"port": 3001, "namespaces": ["/button", "/thinkprocess", "/test", "/canvas"], "cors": {"origin": "*", "methods": ["GET", "POST"]}}, "security": {"csrf": {"enable": false}, "cors": {"origin": "*", "allowMethods": "GET,HEAD,PUT,POST,DELETE,PATCH"}}, "logging": {"level": "INFO", "dir": "./logs", "maxFiles": 10, "maxSize": "10MB"}, "proxy": true, "vps": "gz"}