const Subscription = require('egg').Subscription;

class ClashCache extends Subscription {
  static get schedule() {
    return {
      interval: '300s',
      type: 'all',
      immediate: true,
      enabled: true,
    };
  }

  async subscribe() {
    let sw = await this.app.redis.get('clashsw');
    if (+sw === 1) {
      await this.app.redis.set('clashsw', 0);
      await this.ctx.service.feishu.fs3('clashsw已关闭');
    }
  }
}

module.exports = ClashCache;
