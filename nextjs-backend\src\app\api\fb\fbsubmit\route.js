/**
 * 粉笔答案提交API
 * 迁移自egg项目的fb.js fbsubmit方法
 */

const { NextResponse } = require('next/server');
const { app } = require('../../../../lib/app');

// 确保应用已初始化
let appInitialized = false;

async function ensureAppInitialized() {
  if (!appInitialized) {
    await app.init();
    appInitialized = true;
  }
}

async function POST(request) {
  try {
    await ensureAppInitialized();
    
    const body = await request.json();
    const { kjid, mode, answers, biao = 'fbsy' } = body;
    
    console.log('📝 提交答案 - 参数:', { kjid, mode, answersCount: answers?.length });
    
    if (!kjid) {
      return NextResponse.json({ 
        error: '缺少kjid参数' 
      }, { status: 400 });
    }
    
    let result = {
      success: true,
      message: '答案提交成功',
      kjid,
      mode,
      submittedCount: 0,
      updatedCount: 0,
      errors: []
    };
    
    // 如果有答案数组，批量处理
    if (answers && Array.isArray(answers)) {
      for (const answer of answers) {
        try {
          const { id, choice, userAnswer } = answer;
          
          if (!id) {
            result.errors.push({ error: '缺少题目ID', answer });
            continue;
          }
          
          // 更新题目的用户答案
          const updateData = {};
          if (choice !== undefined) {
            updateData.choice = choice;
          }
          if (userAnswer !== undefined) {
            updateData.userAnswer = userAnswer;
          }
          
          if (Object.keys(updateData).length > 0) {
            const updateResult = await app.mysql.update(
              biao,
              updateData,
              { where: { id: parseInt(id) } }
            );
            
            if (updateResult.affectedRows > 0) {
              result.updatedCount++;
            }
          }
          
          result.submittedCount++;
          
        } catch (error) {
          console.error('❌ 单个答案提交失败:', error.message);
          result.errors.push({ 
            error: error.message, 
            answer 
          });
        }
      }
    }
    
    // 记录提交日志到Redis（可选）
    try {
      const submitLog = {
        kjid,
        mode,
        timestamp: new Date().toISOString(),
        submittedCount: result.submittedCount,
        updatedCount: result.updatedCount,
        errorCount: result.errors.length
      };
      
      await app.redis.setex(
        `submit_log:${kjid}:${Date.now()}`,
        3600, // 1小时过期
        JSON.stringify(submitLog)
      );
    } catch (redisError) {
      console.error('❌ Redis日志记录失败:', redisError.message);
      // Redis失败不影响主流程
    }
    
    console.log('✅ 答案提交完成:', {
      submittedCount: result.submittedCount,
      updatedCount: result.updatedCount,
      errorCount: result.errors.length
    });
    
    // 返回与egg项目一致的格式: { msg: ... }
    return NextResponse.json({
      msg: {
        submittedCount: result.submittedCount,
        updatedCount: result.updatedCount,
        success: result.success
      }
    });
    
  } catch (error) {
    console.error('❌ fbsubmit API错误:', error.message);
    return NextResponse.json({ 
      success: false,
      error: error.message,
      timestamp: new Date().toISOString()
    }, { status: 500 });
  }
}

module.exports = { POST };
