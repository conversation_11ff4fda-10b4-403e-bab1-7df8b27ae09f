/**
 * 数据库测试API路由
 * 演示如何使用app.mysql和app.service.xr
 */

const { app } = require('../../../lib/app');
const { NextResponse } = require('next/server');

// 确保应用已初始化
let appInitialized = false;

async function ensureAppInitialized() {
  if (!appInitialized) {
    await app.init();
    appInitialized = true;
  }
}

async function GET(request) {
  try {
    await ensureAppInitialized();
    
    const { searchParams } = new URL(request.url);
    const action = searchParams.get('action') || 'health';
    
    switch (action) {
      case 'health':
        // 健康检查
        const health = await app.healthCheck();
        return NextResponse.json(health);
        
      case 'tables':
        // 获取数据库表列表
        const tables = await app.mysql.query('SHOW TABLES');
        return NextResponse.json({ tables });
        
      case 'sw':
        // 直接使用app.mysql操作（类似egg项目的app.mysql.get）
        const swData = await app.mysql.get('sw', { name: 'clash' });
        return NextResponse.json({ data: swData });

      case 'mysql-select':
        // 使用app.mysql.select操作
        const selectResult = await app.mysql.select('sw', { where: { name: 'clash' } });
        return NextResponse.json({ data: selectResult });

      case 'redis-test':
        // 测试Redis操作
        await app.redis.set('test-key', 'test-value', 60);
        const redisValue = await app.redis.get('test-key');
        return NextResponse.json({
          set: 'test-value',
          get: redisValue,
          ping: await app.redis.ping()
        });
        
      default:
        return NextResponse.json({
          error: 'Invalid action',
          availableActions: ['health', 'tables', 'sw', 'mysql-select', 'redis-test']
        }, { status: 400 });
    }
    
  } catch (error) {
    console.error('❌ API错误:', error.message);
    return NextResponse.json({ 
      error: error.message,
      timestamp: new Date().toISOString()
    }, { status: 500 });
  }
}

async function POST(request) {
  try {
    await ensureAppInitialized();
    
    const body = await request.json();
    const { action, table, data, where } = body;
    
    switch (action) {
      case 'create':
        // 创建记录
        if (!table || !data) {
          return NextResponse.json({ error: 'table和data参数必需' }, { status: 400 });
        }
        const createResult = await app.mysql.insert(table, data);
        return NextResponse.json(createResult);

      case 'update':
        // 更新记录
        if (!table || !data || !where) {
          return NextResponse.json({ error: 'table、data和where参数必需' }, { status: 400 });
        }
        const updateResult = await app.mysql.update(table, data, { where });
        return NextResponse.json(updateResult);

      case 'query':
        // 执行原生SQL
        if (!body.sql) {
          return NextResponse.json({ error: 'sql参数必需' }, { status: 400 });
        }
        const queryResult = await app.mysql.query(body.sql, body.params || []);
        return NextResponse.json({ data: queryResult });
        
      default:
        return NextResponse.json({ 
          error: 'Invalid action',
          availableActions: ['create', 'update', 'query']
        }, { status: 400 });
    }
    
  } catch (error) {
    console.error('❌ API错误:', error.message);
    return NextResponse.json({ 
      error: error.message,
      timestamp: new Date().toISOString()
    }, { status: 500 });
  }
}

module.exports = { GET, POST };
