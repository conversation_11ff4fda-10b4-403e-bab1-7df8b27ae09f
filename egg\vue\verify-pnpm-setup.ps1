# PowerShell script to verify pnpm setup
Write-Host "🔍 Verifying pnpm setup..." -ForegroundColor Blue

# Check if pnpm is installed
Write-Host "`n1. Checking pnpm installation..." -ForegroundColor Yellow
try {
    $pnpmVersion = pnpm --version
    Write-Host "✅ pnpm is installed: v$pnpmVersion" -ForegroundColor Green
} catch {
    Write-Host "❌ pnpm is not installed!" -ForegroundColor Red
    exit 1
}

# Check package.json configuration
Write-Host "`n2. Checking package.json configuration..." -ForegroundColor Yellow
$packageJson = Get-Content "package.json" | ConvertFrom-Json
if ($packageJson.packageManager -like "pnpm*") {
    Write-Host "✅ packageManager field is set to pnpm" -ForegroundColor Green
} else {
    Write-Host "⚠️  packageManager field not found or not set to pnpm" -ForegroundColor Yellow
}

if ($packageJson.engines.pnpm) {
    Write-Host "✅ pnpm engine requirement is set: $($packageJson.engines.pnpm)" -ForegroundColor Green
} else {
    Write-Host "⚠️  pnpm engine requirement not found" -ForegroundColor Yellow
}

if ($packageJson.scripts.preinstall -eq "npx only-allow pnpm") {
    Write-Host "✅ preinstall script is configured to enforce pnpm" -ForegroundColor Green
} else {
    Write-Host "⚠️  preinstall script not found or not configured properly" -ForegroundColor Yellow
}

# Check if .npmrc exists
Write-Host "`n3. Checking .npmrc configuration..." -ForegroundColor Yellow
if (Test-Path ".npmrc") {
    Write-Host "✅ .npmrc file exists" -ForegroundColor Green
} else {
    Write-Host "⚠️  .npmrc file not found" -ForegroundColor Yellow
}

# Check if only-allow is installed
Write-Host "`n4. Checking only-allow package..." -ForegroundColor Yellow
if (Test-Path "node_modules/only-allow") {
    Write-Host "✅ only-allow package is installed" -ForegroundColor Green
} else {
    Write-Host "⚠️  only-allow package not found" -ForegroundColor Yellow
}

# Check if pnpm-lock.yaml exists
Write-Host "`n5. Checking lockfile..." -ForegroundColor Yellow
if (Test-Path "pnpm-lock.yaml") {
    Write-Host "✅ pnpm-lock.yaml exists" -ForegroundColor Green
} else {
    Write-Host "❌ pnpm-lock.yaml not found!" -ForegroundColor Red
}

Write-Host "`n🎉 pnpm setup verification completed!" -ForegroundColor Green
Write-Host "You can now use pnpm commands:" -ForegroundColor Cyan
Write-Host "  pnpm install  - Install dependencies" -ForegroundColor White
Write-Host "  pnpm dev      - Start development server" -ForegroundColor White
Write-Host "  pnpm build    - Build for production" -ForegroundColor White
