#!/usr/bin/env node

/**
 * EggJS性能基准测试脚本
 * 对比优化前后的启动时间、热加载响应时间、内存使用等关键指标
 */

const { spawn, exec } = require('child_process');
const fs = require('fs');
const path = require('path');
const http = require('http');

// 基准测试配置
const BENCHMARK_CONFIG = {
  iterations: 5, // 测试迭代次数
  warmupIterations: 2, // 预热迭代次数
  testTimeout: 60000, // 测试超时时间
  startupTimeout: 30000, // 启动超时时间
  port: 7001,
  host: '127.0.0.1',
};

// 测试结果存储
const testResults = {
  timestamp: new Date().toISOString(),
  config: BENCHMARK_CONFIG,
  tests: {},
  summary: {},
};

/**
 * 日志记录
 */
function log(message, level = 'INFO') {
  const timestamp = new Date().toLocaleTimeString();
  console.log(`[${timestamp}] [${level}] ${message}`);
}

/**
 * 等待指定时间
 */
function sleep(ms) {
  return new Promise(resolve => setTimeout(resolve, ms));
}

/**
 * 检查应用是否启动
 */
function checkAppStarted(port = BENCHMARK_CONFIG.port) {
  return new Promise((resolve) => {
    const req = http.request({
      hostname: BENCHMARK_CONFIG.host,
      port: port,
      path: '/',
      method: 'GET',
      timeout: 3000,
    }, (res) => {
      resolve(res.statusCode === 200);
    });
    
    req.on('error', () => resolve(false));
    req.on('timeout', () => {
      req.destroy();
      resolve(false);
    });
    
    req.end();
  });
}

/**
 * 等待应用启动
 */
async function waitForAppStartup(maxWaitTime = BENCHMARK_CONFIG.startupTimeout) {
  const startTime = Date.now();
  
  while (Date.now() - startTime < maxWaitTime) {
    if (await checkAppStarted()) {
      return Date.now() - startTime;
    }
    await sleep(500);
  }
  
  throw new Error('Application startup timeout');
}

/**
 * 停止应用
 */
async function stopApp() {
  return new Promise((resolve) => {
    exec('powershell -ExecutionPolicy Bypass -File ./kill-port.ps1 -Port 7001 -Name Egg', {
      cwd: process.cwd(),
    }, (error) => {
      // 忽略错误，因为可能没有运行的进程
      resolve();
    });
  });
}

/**
 * 启动应用并测量启动时间
 */
async function measureStartupTime(command = 'pnpm run dev:optimized') {
  log(`启动应用: ${command}`);
  
  const startTime = Date.now();
  
  const child = spawn('cmd', ['/c', command], {
    cwd: process.cwd(),
    stdio: ['ignore', 'pipe', 'pipe'],
  });
  
  let output = '';
  let errorOutput = '';
  
  child.stdout.on('data', (data) => {
    output += data.toString();
  });
  
  child.stderr.on('data', (data) => {
    errorOutput += data.toString();
  });
  
  try {
    // 等待应用启动
    const actualStartupTime = await waitForAppStartup();
    const totalTime = Date.now() - startTime;
    
    log(`应用启动成功: ${actualStartupTime}ms (总时间: ${totalTime}ms)`);
    
    // 分离子进程
    child.unref();
    
    return {
      success: true,
      startupTime: actualStartupTime,
      totalTime: totalTime,
      pid: child.pid,
    };
  } catch (error) {
    log(`应用启动失败: ${error.message}`, 'ERROR');
    
    // 清理子进程
    try {
      child.kill();
    } catch (e) {
      // 忽略清理错误
    }
    
    return {
      success: false,
      error: error.message,
      output: output.slice(-500), // 最后500个字符
      errorOutput: errorOutput.slice(-500),
    };
  }
}

/**
 * 测试API响应时间
 */
async function measureApiResponseTime(path = '/', iterations = 10) {
  log(`测试API响应时间: ${path}`);
  
  const responseTimes = [];
  
  for (let i = 0; i < iterations; i++) {
    const startTime = Date.now();
    
    try {
      await new Promise((resolve, reject) => {
        const req = http.request({
          hostname: BENCHMARK_CONFIG.host,
          port: BENCHMARK_CONFIG.port,
          path: path,
          method: 'GET',
          timeout: 5000,
        }, (res) => {
          let data = '';
          res.on('data', chunk => data += chunk);
          res.on('end', () => resolve(data));
        });
        
        req.on('error', reject);
        req.on('timeout', () => {
          req.destroy();
          reject(new Error('Request timeout'));
        });
        
        req.end();
      });
      
      const responseTime = Date.now() - startTime;
      responseTimes.push(responseTime);
      
    } catch (error) {
      log(`API请求失败: ${error.message}`, 'WARN');
    }
    
    // 请求间隔
    if (i < iterations - 1) {
      await sleep(100);
    }
  }
  
  if (responseTimes.length === 0) {
    return { success: false, error: 'No successful requests' };
  }
  
  const avgResponseTime = Math.round(responseTimes.reduce((sum, time) => sum + time, 0) / responseTimes.length);
  const minResponseTime = Math.min(...responseTimes);
  const maxResponseTime = Math.max(...responseTimes);
  
  log(`API响应时间 - 平均: ${avgResponseTime}ms, 最小: ${minResponseTime}ms, 最大: ${maxResponseTime}ms`);
  
  return {
    success: true,
    average: avgResponseTime,
    min: minResponseTime,
    max: maxResponseTime,
    samples: responseTimes.length,
  };
}

/**
 * 测试内存使用
 */
async function measureMemoryUsage() {
  log('测试内存使用情况');
  
  try {
    const response = await new Promise((resolve, reject) => {
      const req = http.request({
        hostname: BENCHMARK_CONFIG.host,
        port: BENCHMARK_CONFIG.port,
        path: '/cluster-stats',
        method: 'GET',
        timeout: 5000,
        headers: { 'Accept': 'application/json' },
      }, (res) => {
        let data = '';
        res.on('data', chunk => data += chunk);
        res.on('end', () => {
          try {
            resolve(JSON.parse(data));
          } catch (e) {
            reject(new Error('Invalid JSON response'));
          }
        });
      });
      
      req.on('error', reject);
      req.on('timeout', () => {
        req.destroy();
        reject(new Error('Request timeout'));
      });
      
      req.end();
    });
    
    if (response.success && response.data && response.data.cluster) {
      const memory = response.data.cluster.memoryUsage;
      const memoryUsageMB = {
        rss: Math.round(memory.rss / 1024 / 1024),
        heapTotal: Math.round(memory.heapTotal / 1024 / 1024),
        heapUsed: Math.round(memory.heapUsed / 1024 / 1024),
        external: Math.round(memory.external / 1024 / 1024),
        heapUsageRatio: Math.round((memory.heapUsed / memory.heapTotal) * 100),
      };
      
      log(`内存使用 - RSS: ${memoryUsageMB.rss}MB, 堆: ${memoryUsageMB.heapUsed}/${memoryUsageMB.heapTotal}MB (${memoryUsageMB.heapUsageRatio}%)`);
      
      return {
        success: true,
        memory: memoryUsageMB,
        uptime: response.data.cluster.uptime,
      };
    }
    
    return { success: false, error: 'Invalid cluster response' };
  } catch (error) {
    log(`内存使用测试失败: ${error.message}`, 'ERROR');
    return { success: false, error: error.message };
  }
}

/**
 * 执行完整的性能测试
 */
async function runPerformanceTest(testName, command) {
  log(`\n🧪 开始性能测试: ${testName}`);
  log('='.repeat(60));
  
  const testResult = {
    name: testName,
    command: command,
    iterations: [],
    summary: {},
  };
  
  // 预热测试
  log('🔥 预热测试...');
  for (let i = 0; i < BENCHMARK_CONFIG.warmupIterations; i++) {
    await stopApp();
    await sleep(2000);
    
    const result = await measureStartupTime(command);
    if (result.success) {
      log(`预热 ${i + 1}: ${result.startupTime}ms`);
      await stopApp();
      await sleep(1000);
    }
  }
  
  // 正式测试
  log('📊 正式测试...');
  for (let i = 0; i < BENCHMARK_CONFIG.iterations; i++) {
    await stopApp();
    await sleep(2000);
    
    log(`\n--- 测试迭代 ${i + 1}/${BENCHMARK_CONFIG.iterations} ---`);
    
    const iteration = {
      index: i + 1,
      timestamp: new Date().toISOString(),
    };
    
    // 测试启动时间
    const startupResult = await measureStartupTime(command);
    iteration.startup = startupResult;
    
    if (startupResult.success) {
      // 等待应用稳定
      await sleep(3000);
      
      // 测试API响应时间
      const apiResult = await measureApiResponseTime('/', 5);
      iteration.api = apiResult;
      
      // 测试内存使用
      const memoryResult = await measureMemoryUsage();
      iteration.memory = memoryResult;
      
      // 测试集群状态API
      const clusterApiResult = await measureApiResponseTime('/cluster-stats', 3);
      iteration.clusterApi = clusterApiResult;
    }
    
    testResult.iterations.push(iteration);
    
    // 停止应用
    await stopApp();
    await sleep(1000);
  }
  
  // 计算汇总统计
  const successfulIterations = testResult.iterations.filter(iter => iter.startup.success);
  
  if (successfulIterations.length > 0) {
    const startupTimes = successfulIterations.map(iter => iter.startup.startupTime);
    const apiTimes = successfulIterations
      .filter(iter => iter.api && iter.api.success)
      .map(iter => iter.api.average);
    
    testResult.summary = {
      successfulIterations: successfulIterations.length,
      totalIterations: BENCHMARK_CONFIG.iterations,
      successRate: (successfulIterations.length / BENCHMARK_CONFIG.iterations) * 100,
      startup: {
        average: Math.round(startupTimes.reduce((sum, time) => sum + time, 0) / startupTimes.length),
        min: Math.min(...startupTimes),
        max: Math.max(...startupTimes),
        standardDeviation: calculateStandardDeviation(startupTimes),
      },
      api: apiTimes.length > 0 ? {
        average: Math.round(apiTimes.reduce((sum, time) => sum + time, 0) / apiTimes.length),
        min: Math.min(...apiTimes),
        max: Math.max(...apiTimes),
      } : null,
    };
    
    log(`\n📊 ${testName} 测试结果:`);
    log(`   成功率: ${testResult.summary.successRate.toFixed(1)}%`);
    log(`   启动时间: 平均${testResult.summary.startup.average}ms (${testResult.summary.startup.min}-${testResult.summary.startup.max}ms)`);
    if (testResult.summary.api) {
      log(`   API响应: 平均${testResult.summary.api.average}ms (${testResult.summary.api.min}-${testResult.summary.api.max}ms)`);
    }
  } else {
    log(`❌ ${testName} 测试失败: 没有成功的迭代`, 'ERROR');
  }
  
  return testResult;
}

/**
 * 计算标准差
 */
function calculateStandardDeviation(values) {
  const mean = values.reduce((sum, value) => sum + value, 0) / values.length;
  const squaredDifferences = values.map(value => Math.pow(value - mean, 2));
  const variance = squaredDifferences.reduce((sum, value) => sum + value, 0) / values.length;
  return Math.round(Math.sqrt(variance));
}

/**
 * 主函数
 */
async function main() {
  log('🚀 开始EggJS性能基准测试');
  log('='.repeat(70));
  
  try {
    // 确保没有运行的应用
    await stopApp();
    await sleep(3000);
    
    // 测试优化后的启动
    const optimizedTest = await runPerformanceTest('优化后启动', 'pnpm run dev:optimized');
    testResults.tests.optimized = optimizedTest;
    
    // 保存测试结果
    const resultPath = path.join(__dirname, '..', 'run', 'benchmark-results.json');
    const resultDir = path.dirname(resultPath);
    
    if (!fs.existsSync(resultDir)) {
      fs.mkdirSync(resultDir, { recursive: true });
    }
    
    fs.writeFileSync(resultPath, JSON.stringify(testResults, null, 2));
    log(`\n📝 测试结果已保存到: ${resultPath}`);
    
    // 生成测试报告
    generateBenchmarkReport();
    
    log('\n✅ 性能基准测试完成!');
    
  } catch (error) {
    log(`❌ 测试过程中发生错误: ${error.message}`, 'ERROR');
    process.exit(1);
  } finally {
    // 清理
    await stopApp();
  }
}

/**
 * 生成基准测试报告
 */
function generateBenchmarkReport() {
  log('\n📊 基准测试报告');
  log('='.repeat(70));
  
  if (testResults.tests.optimized && testResults.tests.optimized.summary.successfulIterations > 0) {
    const optimized = testResults.tests.optimized.summary;
    
    log('🚀 优化后性能指标:');
    log(`   启动时间: ${optimized.startup.average}ms (±${optimized.startup.standardDeviation}ms)`);
    log(`   启动范围: ${optimized.startup.min}ms - ${optimized.startup.max}ms`);
    log(`   成功率: ${optimized.successRate.toFixed(1)}%`);
    
    if (optimized.api) {
      log(`   API响应: ${optimized.api.average}ms (${optimized.api.min}-${optimized.api.max}ms)`);
    }
    
    // 性能评估
    log('\n💡 性能评估:');
    if (optimized.startup.average < 1000) {
      log('   ✅ 启动性能优秀 (< 1秒)');
    } else if (optimized.startup.average < 2000) {
      log('   🟡 启动性能良好 (1-2秒)');
    } else {
      log('   🔴 启动性能需要优化 (> 2秒)');
    }
    
    if (optimized.api && optimized.api.average < 100) {
      log('   ✅ API响应优秀 (< 100ms)');
    } else if (optimized.api && optimized.api.average < 500) {
      log('   🟡 API响应良好 (100-500ms)');
    } else if (optimized.api) {
      log('   🔴 API响应需要优化 (> 500ms)');
    }
  }
}

// 如果直接运行脚本
if (require.main === module) {
  main();
}

module.exports = {
  runPerformanceTest,
  measureStartupTime,
  measureApiResponseTime,
  measureMemoryUsage,
  main,
};
