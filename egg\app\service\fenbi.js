const Service = require('egg').Service;
const cheerio = require('cheerio');
const axios = require('axios');
const iconv = require('iconv-lite');
const fs = require('fs');
const path = require('path');

class FbService extends Service {
  async axiosretry(url, options, retries = 10) {
    const { ctx, app } = this;
    let attempt = 0;
    while (attempt < retries) {
      try {
        return await axios.get(url, options); // 尝试请求
      } catch (error) {
        attempt++;
        await ctx.service.feishu.fs3(
          `${path} Attempt ${attempt} failed: ${error.message}`,
        );
        console.error(`Attempt ${attempt} failed: ${error.message}`);
        if (attempt >= retries) throw error; // 达到最大重试次数
        await new Promise((resolve) => setTimeout(resolve, attempt * 2000)); // 重试前等待
      }
    }
  }
}

module.exports = FbService;
