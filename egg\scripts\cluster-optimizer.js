#!/usr/bin/env node

/**
 * EggJS集群配置优化脚本
 * 根据系统硬件和环境动态配置最优的worker进程数和集群参数
 */

const os = require('os');
const fs = require('fs');
const path = require('path');

// 获取系统信息
const cpuCount = os.cpus().length;
const totalMemory = os.totalmem();
const freeMemory = os.freemem();
const platform = os.platform();
const arch = os.arch();

// 内存单位转换
const totalMemoryGB = Math.round(totalMemory / 1024 / 1024 / 1024);
const freeMemoryGB = Math.round(freeMemory / 1024 / 1024 / 1024);

console.log('⚙️ EggJS集群配置优化');
console.log('='.repeat(50));
console.log(`💻 系统平台: ${platform} (${arch})`);
console.log(`⚡ CPU核心数: ${cpuCount}`);
console.log(`🧠 总内存: ${totalMemoryGB}GB`);
console.log(`💾 可用内存: ${freeMemoryGB}GB`);

// 获取环境配置
const nodeEnv = process.env.NODE_ENV || 'development';
const eggEnv = process.env.EGG_SERVER_ENV || 'local';

console.log(`🌍 运行环境: ${nodeEnv} (${eggEnv})`);

/**
 * 计算最优worker进程数
 */
function calculateOptimalWorkers() {
  let workers;
  
  if (nodeEnv === 'production') {
    // 生产环境：根据CPU核心数和内存情况计算
    if (totalMemoryGB >= 8) {
      // 内存充足，可以使用更多worker
      workers = Math.min(cpuCount, 8); // 最多8个worker
    } else if (totalMemoryGB >= 4) {
      // 中等内存，适中的worker数量
      workers = Math.min(cpuCount, 4);
    } else {
      // 内存较少，减少worker数量
      workers = Math.min(cpuCount, 2);
    }
  } else {
    // 开发环境：使用单进程，便于调试
    workers = 1;
  }
  
  return workers;
}

/**
 * 生成集群配置
 */
function generateClusterConfig() {
  const workers = calculateOptimalWorkers();
  
  const config = {
    workers,
    baseDir: process.cwd(),
    framework: path.join(process.cwd(), 'node_modules', 'egg'),
    plugins: {},
    
    // 端口配置
    port: 7001,
    hostname: nodeEnv === 'production' ? '0.0.0.0' : '127.0.0.1',
    
    // 进程管理配置
    sticky: false, // 开发环境禁用sticky sessions
    startTimeout: 30000, // 启动超时时间
    workerStartTimeout: 10000, // worker启动超时时间
    
    // 优雅关闭配置
    killTimeout: 5000, // 强制杀死进程前的等待时间
    
    // 环境特定配置
    env: nodeEnv,
    eggEnv: eggEnv,
  };
  
  // 生产环境特定配置
  if (nodeEnv === 'production') {
    config.sticky = true; // 生产环境启用sticky sessions
    config.startTimeout = 60000; // 生产环境增加启动超时时间
    config.workerStartTimeout = 30000;
    
    // 进程监控配置
    config.refork = true; // 启用进程重启
    config.reforkLimit = 10; // 重启次数限制
    config.reforkInterval = 60000; // 重启间隔
  }
  
  return config;
}

/**
 * 生成启动脚本参数
 */
function generateStartupArgs() {
  const workers = calculateOptimalWorkers();
  const config = generateClusterConfig();
  
  const args = [
    `--workers=${workers}`,
    `--port=${config.port}`,
    `--sticky=${config.sticky}`,
  ];
  
  // 开发环境特定参数
  if (nodeEnv !== 'production') {
    args.push('--debug');
    args.push('--inspect=9229');
  }
  
  return args;
}

/**
 * 评估worker_threads支持
 */
function evaluateWorkerThreadsSupport() {
  const nodeVersion = process.version;
  const majorVersion = parseInt(nodeVersion.slice(1).split('.')[0]);
  
  console.log('\n🧵 Worker Threads支持评估:');
  console.log(`📦 Node.js版本: ${nodeVersion}`);
  
  if (majorVersion >= 12) {
    console.log('✅ 支持Worker Threads (Node.js >= 12.x)');
    
    // 评估是否适合使用worker_threads
    if (cpuCount >= 4 && totalMemoryGB >= 4) {
      console.log('💡 建议: 可以考虑使用worker_threads进行CPU密集型任务');
      console.log('   - 图像处理 (Sharp)');
      console.log('   - 数据计算');
      console.log('   - 文件压缩');
    } else {
      console.log('⚠️ 建议: 当前硬件配置下，cluster模式更适合');
    }
  } else {
    console.log('❌ 不支持Worker Threads (需要Node.js >= 12.x)');
  }
  
  return {
    supported: majorVersion >= 12,
    recommended: majorVersion >= 12 && cpuCount >= 4 && totalMemoryGB >= 4,
    nodeVersion,
    majorVersion,
  };
}

/**
 * 生成进程间通信优化建议
 */
function generateIPCOptimizations() {
  const optimizations = [];
  
  if (nodeEnv === 'production') {
    optimizations.push({
      type: 'messenger',
      description: '使用高效的进程间消息传递',
      config: {
        maxListeners: 100,
        timeout: 5000,
      },
    });
    
    optimizations.push({
      type: 'cluster',
      description: '优化cluster进程管理',
      config: {
        exec: path.join(__dirname, '..', 'app.js'),
        silent: false,
        windowsHide: true,
      },
    });
  }
  
  return optimizations;
}

/**
 * 保存配置到文件
 */
function saveConfiguration() {
  const config = generateClusterConfig();
  const startupArgs = generateStartupArgs();
  const workerThreadsSupport = evaluateWorkerThreadsSupport();
  const ipcOptimizations = generateIPCOptimizations();
  
  const fullConfig = {
    timestamp: new Date().toISOString(),
    environment: nodeEnv,
    eggEnvironment: eggEnv,
    systemInfo: {
      platform,
      arch,
      cpuCount,
      totalMemoryGB,
      freeMemoryGB,
    },
    clusterConfig: config,
    startupArgs,
    workerThreadsSupport,
    ipcOptimizations,
    recommendations: generateRecommendations(config),
  };
  
  // 保存配置
  const configPath = path.join(__dirname, '..', 'run', 'cluster-config.json');
  const configDir = path.dirname(configPath);
  
  if (!fs.existsSync(configDir)) {
    fs.mkdirSync(configDir, { recursive: true });
  }
  
  fs.writeFileSync(configPath, JSON.stringify(fullConfig, null, 2));
  console.log(`\n📝 集群配置已保存到: ${configPath}`);
  
  return fullConfig;
}

/**
 * 生成优化建议
 */
function generateRecommendations(config) {
  const recommendations = [];
  
  // Worker数量建议
  if (config.workers === 1 && nodeEnv === 'production') {
    recommendations.push({
      type: 'warning',
      message: '生产环境建议使用多个worker进程以提高并发性能',
    });
  }
  
  // 内存建议
  if (totalMemoryGB < 2) {
    recommendations.push({
      type: 'warning',
      message: '系统内存较少，建议增加内存或优化应用内存使用',
    });
  }
  
  // CPU建议
  if (cpuCount === 1) {
    recommendations.push({
      type: 'info',
      message: '单核CPU环境，cluster模式优势有限',
    });
  }
  
  // 环境建议
  if (nodeEnv !== 'production' && config.workers > 1) {
    recommendations.push({
      type: 'info',
      message: '开发环境建议使用单worker便于调试',
    });
  }
  
  return recommendations;
}

/**
 * 主函数
 */
function main() {
  console.log('\n🚀 开始集群配置优化...');
  
  const config = saveConfiguration();
  
  console.log('\n📊 优化结果:');
  console.log(`👥 推荐Worker数量: ${config.clusterConfig.workers}`);
  console.log(`🔗 启动参数: ${config.startupArgs.join(' ')}`);
  
  if (config.recommendations.length > 0) {
    console.log('\n💡 优化建议:');
    config.recommendations.forEach((rec, index) => {
      const icon = rec.type === 'warning' ? '⚠️' : 'ℹ️';
      console.log(`   ${index + 1}. ${icon} ${rec.message}`);
    });
  }
  
  console.log('\n✅ 集群配置优化完成!');
  
  return config;
}

// 如果直接运行脚本
if (require.main === module) {
  main();
}

module.exports = {
  calculateOptimalWorkers,
  generateClusterConfig,
  generateStartupArgs,
  evaluateWorkerThreadsSupport,
  generateIPCOptimizations,
  main,
};
