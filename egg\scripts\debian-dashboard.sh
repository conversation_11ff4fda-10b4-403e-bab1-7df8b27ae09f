#!/bin/bash

# Debian专用Egg.js监控仪表板
# 实时显示应用和系统状态，专门解决Debian环境问题

APP_PORT=7001

# 颜色定义
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
PURPLE='\033[0;35m'
CYAN='\033[0;36m'
NC='\033[0m'

# 清屏函数
clear_screen() {
    clear
    echo -e "${BLUE}╔══════════════════════════════════════════════════════════════╗${NC}"
    echo -e "${BLUE}║                 🐧 Debian Egg.js 监控仪表板                   ║${NC}"
    echo -e "${BLUE}║                    $(date '+%Y-%m-%d %H:%M:%S')                    ║${NC}"
    echo -e "${BLUE}╚══════════════════════════════════════════════════════════════╝${NC}"
    echo
}

# 检查应用状态
check_app_status() {
    echo -e "${CYAN}📱 应用状态${NC}"
    echo "━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━"
    
    # 检查端口监听
    if netstat -tlnp | grep ":$APP_PORT " > /dev/null 2>&1; then
        echo -e "端口状态: ${GREEN}✅ 监听中 ($APP_PORT)${NC}"
        
        # 获取进程信息
        local pid=$(netstat -tlnp | grep ":$APP_PORT " | awk '{print $7}' | cut -d'/' -f1)
        if [ ! -z "$pid" ]; then
            local process_info=$(ps -p $pid -o pid,ppid,pcpu,pmem,vsz,rss,etime --no-headers 2>/dev/null)
            if [ ! -z "$process_info" ]; then
                echo "进程PID: $pid"
                echo "CPU使用: $(echo $process_info | awk '{print $3}')%"
                echo "内存使用: $(echo $process_info | awk '{print int($6/1024)}')MB"
                echo "运行时间: $(echo $process_info | awk '{print $7}')"
            fi
        fi
        
        # 健康检查
        local health_response=$(curl -s --max-time 5 "http://localhost:$APP_PORT/health" 2>/dev/null)
        if echo "$health_response" | grep -q '"status":"ok"'; then
            echo -e "健康检查: ${GREEN}✅ 正常${NC}"
        else
            echo -e "健康检查: ${RED}❌ 异常${NC}"
        fi
    else
        echo -e "端口状态: ${RED}❌ 未监听${NC}"
        echo -e "健康检查: ${RED}❌ 无响应${NC}"
    fi
    echo
}

# 检查系统资源（Debian优化）
check_system_resources() {
    echo -e "${CYAN}🖥️  系统资源 (Debian优化)${NC}"
    echo "━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━"
    
    # 内存检查（重点关注OOM）
    local mem_info=$(free -m)
    local mem_total=$(echo "$mem_info" | awk 'NR==2{print $2}')
    local mem_used=$(echo "$mem_info" | awk 'NR==2{print $3}')
    local mem_available=$(echo "$mem_info" | awk 'NR==2{print $7}')
    local mem_percent=$((mem_used * 100 / mem_total))
    
    if [ $mem_percent -gt 85 ]; then
        echo -e "内存使用: ${RED}⚠️  ${mem_used}MB/${mem_total}MB (${mem_percent}%) - 危险${NC}"
    elif [ $mem_percent -gt 70 ]; then
        echo -e "内存使用: ${YELLOW}⚠️  ${mem_used}MB/${mem_total}MB (${mem_percent}%) - 警告${NC}"
    else
        echo -e "内存使用: ${GREEN}✅ ${mem_used}MB/${mem_total}MB (${mem_percent}%)${NC}"
    fi
    echo "可用内存: ${mem_available}MB"
    
    # Swap使用情况
    local swap_info=$(free -m | grep Swap)
    local swap_total=$(echo "$swap_info" | awk '{print $2}')
    local swap_used=$(echo "$swap_info" | awk '{print $3}')
    if [ $swap_total -gt 0 ]; then
        local swap_percent=$((swap_used * 100 / swap_total))
        if [ $swap_percent -gt 50 ]; then
            echo -e "Swap使用: ${RED}⚠️  ${swap_used}MB/${swap_total}MB (${swap_percent}%)${NC}"
        else
            echo -e "Swap使用: ${GREEN}✅ ${swap_used}MB/${swap_total}MB (${swap_percent}%)${NC}"
        fi
    else
        echo -e "Swap状态: ${YELLOW}⚠️  未配置${NC}"
    fi
    
    # CPU负载
    local load_info=$(uptime)
    local load_1min=$(echo "$load_info" | awk -F'load average:' '{print $2}' | awk '{print $1}' | sed 's/,//')
    local cpu_cores=$(nproc)
    local load_ratio=$(echo "$load_1min $cpu_cores" | awk '{printf "%.2f", $1/$2}')
    
    if (( $(echo "$load_ratio > 2.0" | bc -l) )); then
        echo -e "系统负载: ${RED}⚠️  $load_1min (${cpu_cores}核心, 比率: $load_ratio)${NC}"
    elif (( $(echo "$load_ratio > 1.0" | bc -l) )); then
        echo -e "系统负载: ${YELLOW}⚠️  $load_1min (${cpu_cores}核心, 比率: $load_ratio)${NC}"
    else
        echo -e "系统负载: ${GREEN}✅ $load_1min (${cpu_cores}核心, 比率: $load_ratio)${NC}"
    fi
    
    # 磁盘使用
    local disk_usage=$(df -h / | awk 'NR==2{print $5}' | sed 's/%//')
    local disk_used=$(df -h / | awk 'NR==2{print $3}')
    local disk_total=$(df -h / | awk 'NR==2{print $2}')
    
    if [ $disk_usage -gt 90 ]; then
        echo -e "磁盘使用: ${RED}⚠️  $disk_used/$disk_total (${disk_usage}%)${NC}"
    elif [ $disk_usage -gt 80 ]; then
        echo -e "磁盘使用: ${YELLOW}⚠️  $disk_used/$disk_total (${disk_usage}%)${NC}"
    else
        echo -e "磁盘使用: ${GREEN}✅ $disk_used/$disk_total (${disk_usage}%)${NC}"
    fi
    echo
}

# 检查Debian特有问题
check_debian_issues() {
    echo -e "${CYAN}🐧 Debian系统检查${NC}"
    echo "━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━"
    
    # 检查OOM Killer事件
    local oom_events=$(dmesg | grep -i "killed process" | wc -l)
    if [ $oom_events -gt 0 ]; then
        echo -e "OOM事件: ${RED}⚠️  发现 $oom_events 次OOM Killer事件${NC}"
        local recent_oom=$(dmesg | grep -i "killed process" | tail -1)
        if [ ! -z "$recent_oom" ]; then
            echo "最近OOM: $(echo $recent_oom | cut -c1-60)..."
        fi
    else
        echo -e "OOM事件: ${GREEN}✅ 无OOM Killer事件${NC}"
    fi
    
    # 检查系统限制
    local max_files=$(ulimit -n)
    if [ $max_files -lt 65536 ]; then
        echo -e "文件描述符: ${YELLOW}⚠️  $max_files (建议65536+)${NC}"
    else
        echo -e "文件描述符: ${GREEN}✅ $max_files${NC}"
    fi
    
    # 检查进程限制
    local max_proc=$(ulimit -u)
    if [ $max_proc -lt 32768 ]; then
        echo -e "进程限制: ${YELLOW}⚠️  $max_proc (建议32768+)${NC}"
    else
        echo -e "进程限制: ${GREEN}✅ $max_proc${NC}"
    fi
    
    # 检查僵尸进程
    local zombie_count=$(ps aux | awk '$8 ~ /^Z/ { count++ } END { print count+0 }')
    if [ $zombie_count -gt 0 ]; then
        echo -e "僵尸进程: ${RED}⚠️  $zombie_count 个${NC}"
    else
        echo -e "僵尸进程: ${GREEN}✅ 无${NC}"
    fi
    echo
}

# 检查网络状态
check_network_status() {
    echo -e "${CYAN}🌐 网络状态${NC}"
    echo "━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━"
    
    local conn_count=$(netstat -an | grep :$APP_PORT | wc -l)
    local established_count=$(netstat -an | grep ESTABLISHED | wc -l)
    local time_wait_count=$(netstat -an | grep TIME_WAIT | wc -l)
    local close_wait_count=$(netstat -an | grep CLOSE_WAIT | wc -l)
    
    echo "应用连接: $conn_count (端口 $APP_PORT)"
    echo "已建立连接: $established_count"
    
    if [ $time_wait_count -gt 1000 ]; then
        echo -e "TIME_WAIT: ${RED}⚠️  $time_wait_count (过多)${NC}"
    elif [ $time_wait_count -gt 500 ]; then
        echo -e "TIME_WAIT: ${YELLOW}⚠️  $time_wait_count (较多)${NC}"
    else
        echo -e "TIME_WAIT: ${GREEN}✅ $time_wait_count${NC}"
    fi
    
    if [ $close_wait_count -gt 100 ]; then
        echo -e "CLOSE_WAIT: ${RED}⚠️  $close_wait_count (可能有连接泄漏)${NC}"
    else
        echo -e "CLOSE_WAIT: ${GREEN}✅ $close_wait_count${NC}"
    fi
    echo
}

# 检查监控状态
check_monitor_status() {
    echo -e "${CYAN}👁️  监控状态${NC}"
    echo "━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━"
    
    # 检查Debian专用监控
    if ps aux | grep debian-monitor | grep -v grep > /dev/null; then
        echo -e "Debian监控: ${GREEN}✅ 运行中${NC}"
    else
        echo -e "Debian监控: ${RED}❌ 未运行${NC}"
    fi
    
    # 检查systemd服务
    if systemctl is-active eggjs-monitor > /dev/null 2>&1; then
        echo -e "系统服务: ${GREEN}✅ 活跃${NC}"
    else
        echo -e "系统服务: ${RED}❌ 非活跃${NC}"
    fi
    
    # 检查日志文件
    local log_files=("/var/log/debian-monitor.log" "/var/log/eggjs-crash-analysis.log")
    for log_file in "${log_files[@]}"; do
        if [ -f "$log_file" ]; then
            local log_size=$(du -h "$log_file" | cut -f1)
            echo "日志文件: $(basename $log_file) ($log_size)"
        fi
    done
    echo
}

# 检查最近事件
check_recent_events() {
    echo -e "${CYAN}📋 最近事件${NC}"
    echo "━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━"
    
    # 检查应用错误日志
    local error_log="/root/eggjslogs/egg-server-example/common-error.log"
    if [ -f "$error_log" ]; then
        local recent_errors=$(tail -10 "$error_log" | grep -c "ERROR")
        if [ $recent_errors -gt 0 ]; then
            echo -e "${RED}🚨 最近10行中有 $recent_errors 个错误${NC}"
            tail -3 "$error_log" | while read line; do
                if [ ! -z "$line" ]; then
                    echo "  $(echo $line | cut -c1-70)..."
                fi
            done
        else
            echo -e "${GREEN}✅ 最近无应用错误${NC}"
        fi
    else
        echo -e "${YELLOW}⚠️  应用错误日志不存在${NC}"
    fi
    
    # 检查系统日志中的相关错误
    local sys_errors=$(journalctl --since "10 minutes ago" | grep -i "egg\|node" | wc -l)
    if [ $sys_errors -gt 0 ]; then
        echo -e "${YELLOW}⚠️  系统日志中有 $sys_errors 条相关记录${NC}"
    else
        echo -e "${GREEN}✅ 系统日志正常${NC}"
    fi
    echo
}

# 显示操作菜单
show_menu() {
    echo -e "${PURPLE}🎮 操作菜单${NC}"
    echo "━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━"
    echo "r) 刷新显示    s) 启动应用    t) 停止应用    m) 重启监控"
    echo "l) 查看日志    o) 检查OOM     n) 网络诊断    q) 退出"
    echo
}

# 主循环
main_loop() {
    while true; do
        clear_screen
        check_app_status
        check_system_resources
        check_debian_issues
        check_network_status
        check_monitor_status
        check_recent_events
        show_menu
        
        echo -n "请选择操作 (自动刷新30秒): "
        read -t 30 choice
        
        case "$choice" in
            "r"|"R")
                continue
                ;;
            "s"|"S")
                echo "启动应用..."
                cd /home/<USER>
                sleep 3
                ;;
            "t"|"T")
                echo "停止应用..."
                cd /home/<USER>
                sleep 3
                ;;
            "m"|"M")
                echo "重启监控..."
                sudo systemctl restart eggjs-monitor
                sleep 2
                ;;
            "l"|"L")
                echo "查看监控日志:"
                tail -30 /var/log/debian-monitor.log 2>/dev/null || echo "日志文件不存在"
                read -p "按回车继续..."
                ;;
            "o"|"O")
                echo "检查OOM事件:"
                dmesg | grep -i "killed process" | tail -10 || echo "无OOM事件"
                read -p "按回车继续..."
                ;;
            "n"|"N")
                echo "网络诊断:"
                echo "端口监听: $(netstat -tlnp | grep :$APP_PORT)"
                echo "连接统计: $(netstat -an | grep :$APP_PORT | wc -l) 个连接"
                echo "TIME_WAIT: $(netstat -an | grep TIME_WAIT | wc -l) 个"
                read -p "按回车继续..."
                ;;
            "q"|"Q")
                echo "退出Debian监控仪表板"
                exit 0
                ;;
            "")
                # 超时，自动刷新
                continue
                ;;
            *)
                echo "无效选择，请重试"
                sleep 1
                ;;
        esac
    done
}

# 检查依赖
check_dependencies() {
    if ! command -v bc &> /dev/null; then
        echo "安装bc计算器..."
        sudo apt-get update && sudo apt-get install -y bc
    fi
}

# 启动仪表板
echo "正在启动Debian专用Egg.js监控仪表板..."
check_dependencies
chmod +x /home/<USER>/scripts/debian-monitor.sh
main_loop
