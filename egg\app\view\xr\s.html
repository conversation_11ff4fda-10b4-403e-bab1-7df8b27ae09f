<html>
<head>
    <meta charset="UTF-8"/>
    <meta name="viewport" content="width=device-width,initial-scale=1.0"/>
    <script src="/public/vue/vue.min.js"></script>
    <script src="/public/element/index.js"></script>
    <link rel="stylesheet" href="/public/element/index.css">
    <script src="/public/js/axios.min.js"></script>

    <title>Element Plus demo</title>
</head>
<body>
<div>
</div>
<div id="app">
    <div>
        <el-form ref="form" :model="form" label-width="80px" @submit.native.prevent>
            <el-form-item label="活动名称">
                <el-input v-model="form.name"></el-input>
            </el-form-item>
            <el-button type="primary" @click="onSubmit(form)">立即创建</el-button>
        </el-form>
    </div>
    <div>
        <p v-for="item in tableData">
            <a :href="item.url" type="primary" target="_blank">
                <img :src="item.img">
            </a>
        </p>
    </div>
    <div>
        <!--        <a href="/xr?page={{ppage}}">prev</a>-->
        <!--        <a href="/xr?page={{npage}}">next</a>-->
        <div class="block">
            <span class="demonstration">显示总数</span>
            <el-pagination
                    @size-change="handleSizeChange"
                    @current-change="handleCurrentChange"
                    :current-page.sync="currentPage1"
                    :page-size="12"
                    layout="total, prev, pager, next"
                    :total="1000">
            </el-pagination>
        </div>
    </div>
</div>

<script>
    new Vue({
        el: '#app',
        data: function () {

            return {
                tableData: null,
                currentPage1: 1,
                form: {
                    name: '鱼子酱',
                }
            }
        },
        created() {

        },
        mounted() {
            this.getData();
        },
        methods: {
            toTop() {
                document.body.scrollTop = 0;
            },
            onSubmit(val) {
                let keyword = encodeURI(val.name);
                this.getData1(keyword);
                // this.getData(val);
            },
            handleSizeChange(val) {
                console.log(`每页 ${val} 条`);
            },
            handleCurrentChange(val) {
                // console.log(`当前页: ${page}`);
                this.getData1(this.form.name, val);
            },
            getData1(name, page = 1) {
                axios.get('/sou?s=' + name + '&page=' + page).then(res => {
                    console.log(`当前页: ${page}`);
                    this.tableData = res.data;
                    this.toTop();
                })
            },
            getData(page = 1) {
                axios.get('/sou?s={{skey}}&page=' + page).then(res => {
                    console.log(`当前页: ${page}`);
                    this.tableData = res.data;
                    this.toTop();
                })
            }
        }
    })


</script>

<style>
    html {
        width: auto;
        height: auto;
        max-width: 100%;
        max-height: 100%;
    }

</style>
</body>
</html>
