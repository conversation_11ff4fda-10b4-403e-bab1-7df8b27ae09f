{"server": {"port": 7001, "hostname": "0.0.0.0"}, "database": {"mysql": {"connectionLimit": 20, "acquireTimeout": 30000, "timeout": 30000}}, "redis": {"connectTimeout": 5000, "maxRetriesPerRequest": 5, "retryDelayOnFailover": 50, "lazyConnect": false}, "websocket": {"port": 7002}, "logging": {"level": "WARN", "dir": "/logs/prod", "maxFiles": 30, "maxSize": "50MB"}, "cluster": {"workers": "auto"}, "watcher": {"enabled": false}, "lazyLoading": {"enabled": false}, "schedule": {"enabled": true}, "performance": {"compression": true, "cache": {"enabled": true, "maxAge": 3600}}}