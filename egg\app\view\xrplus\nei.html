<html>
<head>
  <meta charset='UTF-8' />
  <meta name='viewport' content='width=device-width,initial-scale=1.0' />
  <script src='/public/vue/vue.min.js'></script>
  <script src='/public/element/index.js'></script>
  <link rel='stylesheet' href='/public/element/index.css'>
  <script src='/public/js/axios.min.js'></script>
  <link rel='stylesheet' href='https://cdn.jsdelivr.net/npm/bootstrap@4.6.2/dist/css/bootstrap.min.css'>
  <title>Element Plus demo</title>
</head>
<body>
<div>
</div>
<div id='app'>
  <div class='container'>
    <div class='row'>
      <div class='imgbox'>
        <p v-for='item in tableData'>
          <img :src='item'>
        </p>
      </div>

    </div>
  </div>

</div>

<script>
  new Vue({
    el: '#app',
    data: function() {
      return {
        tableData: null,
        imgs: []
      }
    },
    created() {
    },
    mounted() {
      this.getData()
    },
    methods: {
      getData() {
        axios.get('/xrpluspageinfo?id={[id]}').then(res => {
          this.tableData = res.data
        })
      }

    }
  })


</script>

<style>
    img {
        width: auto;
        height: auto;
        max-width: 100%;
        max-height: 100%;
    }
</style>
</body>
</html>
