/**
 * 批量更新ID为空值API
 * 迁移自egg项目的fb.js updateidsnull方法
 */

const { NextResponse } = require('next/server');
const { app } = require('../../../../lib/app');

// 确保应用已初始化
let appInitialized = false;

async function ensureAppInitialized() {
  if (!appInitialized) {
    await app.init();
    appInitialized = true;
  }
}

async function GET(request) {
  try {
    await ensureAppInitialized();
    
    const { searchParams } = new URL(request.url);
    const ids = searchParams.get('ids');
    const type = searchParams.get('type') || 'choice';
    const biao = searchParams.get('biao') || 'fbsy';
    
    console.log('🗑️ 批量设置空值 - 参数:', { ids, type, biao });
    
    if (!ids) {
      return NextResponse.json({ 
        error: '缺少ids参数' 
      }, { status: 400 });
    }
    
    // 解析ID列表
    const idList = ids.split(',')
      .map(id => parseInt(id.trim()))
      .filter(id => !isNaN(id));
    
    if (idList.length === 0) {
      return NextResponse.json({ 
        error: '无效的ID列表' 
      }, { status: 400 });
    }
    
    console.log('📝 处理ID列表:', idList);
    
    // 根据类型设置不同的字段为空
    let updateFields = [];
    let updateValues = [];
    
    switch (type) {
      case 'choice':
        updateFields.push('choice = NULL');
        break;
      case 'userAnswer':
        updateFields.push('userAnswer = NULL');
        break;
      case 'all':
        updateFields.push('choice = NULL');
        updateFields.push('userAnswer = NULL');
        break;
      default:
        updateFields.push('choice = NULL');
    }
    
    try {
      // 构建批量更新SQL
      const placeholders = idList.map(() => '?').join(',');
      const sql = `UPDATE ${biao} SET ${updateFields.join(', ')} WHERE id IN (${placeholders})`;
      
      console.log('🔧 执行SQL:', sql);
      console.log('📊 参数:', idList);
      
      // 执行批量更新
      const updateResult = await app.mysql.query(sql, idList);
      
      console.log('✅ 批量空值设置成功:', {
        idsCount: idList.length,
        type,
        affectedRows: updateResult.affectedRows
      });
      
      // 获取更新后的数据进行验证
      const verificationSql = `SELECT id, choice, userAnswer FROM ${biao} WHERE id IN (${placeholders})`;
      const updatedData = await app.mysql.query(verificationSql, idList);
      
      return NextResponse.json({
        success: true,
        message: '批量空值设置成功',
        data: {
          processedIds: idList,
          type,
          affectedRows: updateResult.affectedRows,
          updatedCount: updatedData.length,
          updatedData
        }
      });
      
    } catch (dbError) {
      console.error('❌ 数据库操作失败:', dbError.message);
      return NextResponse.json({ 
        error: '数据库操作失败: ' + dbError.message 
      }, { status: 500 });
    }
    
  } catch (error) {
    console.error('❌ updateidsnull API错误:', error.message);
    return NextResponse.json({ 
      error: error.message,
      timestamp: new Date().toISOString()
    }, { status: 500 });
  }
}

module.exports = { GET };
