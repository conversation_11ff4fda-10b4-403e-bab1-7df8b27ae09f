<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8"/>
    <meta name="viewport" content="width=device-width,initial-scale=1.0"/>
    <script src="/public/vue/vue.min.js"></script>
    <script src="/public/element/index.js"></script>
    <link rel="stylesheet" href="/public/element/index.css">
    <script src="/public/js/axios.min.js"></script>
    <script src="/public/js/DPlayer.min.js"></script>
    <title>Element Plus demo</title>
</head>
<body>
<div class="input"><input type="text" width="100%" onchange="change()" value="" class="ur"></div>
<div id="dplayer"></div>

<script>
    function change(){
        let url = document.querySelector('.ur').value;
        const dp = new DPlayer({
            container: document.getElementById('dplayer'),
            video: {
                url: url,
            },
        });
    }
</script>

<style>
    #dplayer , .input{
        margin: 0 auto;
        width: 960px;
    }
</style>
</body>
</html>