<html>
<head>
    <meta charset="UTF-8" />
    <meta name="viewport" content="width=device-width,initial-scale=1.0" />
    <script src="/public/vue/vue.min.js"></script>
    <script src="/public/element/index.js"></script>
    <link rel="stylesheet" href="/public/element/index.css">
    <script src="/public/js/axios.min.js"></script>
    <title>Element Plus demo</title>
</head>
<body>
<div id="app">
    <el-table
            :data="tableData"
            style="width: 100%"
            :default-sort = "{prop: 'date', order: 'descending'}"
    >
        <el-table-column
                prop="time"
                label="时间"
                width="180">
        </el-table-column>
        <el-table-column
                prop="price"
                label="价格"
                width="180">
        </el-table-column>
        <el-table-column
                prop="cpu"
                label="cpu"
                width="180">
        </el-table-column>
        <el-table-column
                prop="ram"
                label="ram"
                width="180">
        </el-table-column>
        <el-table-column
                prop="hdd"
                label="hdd"
                width="180">
        </el-table-column>
        <el-table-column
                prop="bw"
                label="bw"
                width="180">
        </el-table-column>
        <el-table-column
                prop="location"
                label="location"
                width="180">
        </el-table-column>
        <el-table-column
                prop="virt"
                label="virt"
                width="180">
        </el-table-column>
    </el-table>
</div>
<script>
    const App = {
        data() {
            return {
                tableData: {{data | safe}}
        };
        },
        methods: {
            formatter(row, column) {
                return (row.shtg+row.dsh)/1;
            }
        }
    };
    const app = Vue.createApp(App);
    app.use(ElementPlus);
    app.mount("#app");
</script>
</body>
</html>