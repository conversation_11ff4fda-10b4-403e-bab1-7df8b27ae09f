/* eslint valid-jsdoc: "off" */

'use strict';

/**
 * @param {Egg.EggAppInfo} appInfo app info
 */

module.exports = (appInfo) => {
  /**
   * built-in config
   * @type {Egg.EggAppConfig}
   **/
  const config = (exports = {});
  exports.cors = {
    origin: '*',
    // 表示允许的源
    allowMethods: 'GET,HEAD,PUT,POST,DELETE,PATCH',
    // 表示允许的http请求方式
  };
  // 使用统一配置模块
  const { getLocalMySQLConfig } = require('./database');
  const { getLocalRedisConfig } = require('./redis');

  // 数据库配置 - 本地环境
  exports.mysql = getLocalMySQLConfig();

  // Redis配置 - 本地环境
  exports.redis = getLocalRedisConfig();
  exports.logger = {
    dir: '/logs/egg',
    level: 'INFO',
  };

  return {
    logger: {
      appLogName: `${appInfo.name}-web.log`,
      coreLogName: 'egg-web.log',
      agentLogName: 'egg-agent.log',
      errorLogName: 'common-error.log',
    },
    ...config,
  };
};
