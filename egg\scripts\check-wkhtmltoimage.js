#!/usr/bin/env node

const { spawn } = require('child_process');
const fs = require('fs');
const path = require('path');

console.log('🔍 检查 wkhtmltoimage 状态...\n');

// 1. 检查 wkhtmltoimage 是否安装
function checkWkhtmltoimage() {
  return new Promise((resolve) => {
    const child = spawn('wkhtmltoimage', ['--version']);
    let stdout = '';
    let stderr = '';

    child.stdout.on('data', (data) => {
      stdout += data.toString();
    });

    child.stderr.on('data', (data) => {
      stderr += data.toString();
    });

    child.on('close', (code) => {
      if (code === 0) {
        console.log('✅ wkhtmltoimage 已安装');
        console.log('📋 版本信息:', stdout.trim());
        resolve(true);
      } else {
        console.log('❌ wkhtmltoimage 未安装或无法运行');
        console.log('❌ 错误信息:', stderr);
        resolve(false);
      }
    });

    child.on('error', (error) => {
      console.log('❌ wkhtmltoimage 启动失败:', error.message);
      if (error.code === 'ENOENT') {
        console.log('💡 建议: 请安装 wkhtmltopdf 包');
        console.log('   Ubuntu/Debian: sudo apt-get install wkhtmltopdf');
        console.log('   CentOS/RHEL: sudo yum install wkhtmltopdf');
      }
      resolve(false);
    });
  });
}

// 2. 测试 wkhtmltoimage 渲染功能
async function testWkhtmltoimage() {
  console.log('\n🧪 测试 wkhtmltoimage 渲染功能...');

  const testHtml = `
<!DOCTYPE html>
<html>
<head>
  <meta charset="UTF-8">
  <style>
    body { font-family: Arial, sans-serif; padding: 20px; }
    h1 { color: #333; }
  </style>
</head>
<body>
  <h1>测试页面</h1>
  <p>这是一个测试页面，用于验证 wkhtmltoimage 功能。</p>
  <p>当前时间: ${new Date().toLocaleString()}</p>
</body>
</html>`;

  const tempDir = '/tmp/';
  const timestamp = Date.now();
  const htmlFile = path.join(tempDir, `test_${timestamp}.html`);
  const imageFile = path.join(tempDir, `test_${timestamp}.png`);

  try {
    // 写入测试HTML文件
    fs.writeFileSync(htmlFile, testHtml);
    console.log('📝 测试HTML文件已创建:', htmlFile);

    // 执行 wkhtmltoimage
    const result = await new Promise((resolve) => {
      const args = [
        '--width',
        '1200',
        '--height',
        '0', // 自动高度
        '--format',
        'png',
        '--quality',
        '95',
        '--quiet',
        htmlFile,
        imageFile,
      ];

      console.log('🚀 执行命令: wkhtmltoimage', args.join(' '));

      const child = spawn('wkhtmltoimage', args);
      let stderr = '';

      child.stderr.on('data', (data) => {
        stderr += data.toString();
      });

      child.on('close', (code) => {
        resolve({ code, stderr });
      });

      child.on('error', (error) => {
        resolve({ error: error.message });
      });
    });

    // 检查结果
    if (result.error) {
      console.log('❌ wkhtmltoimage 执行失败:', result.error);
      return false;
    }

    if (result.code === 0 && fs.existsSync(imageFile)) {
      const stats = fs.statSync(imageFile);
      console.log('✅ wkhtmltoimage 渲染成功');
      console.log('📊 图片大小:', (stats.size / 1024).toFixed(2), 'KB');
      console.log('📁 图片路径:', imageFile);

      // 清理测试文件
      try {
        fs.unlinkSync(htmlFile);
        fs.unlinkSync(imageFile);
        console.log('🧹 测试文件已清理');
      } catch (e) {
        console.log('⚠️ 清理测试文件失败:', e.message);
      }

      return true;
    } else {
      console.log('❌ wkhtmltoimage 渲染失败');
      console.log('❌ 退出代码:', result.code);
      console.log('❌ 错误信息:', result.stderr);
      return false;
    }
  } catch (error) {
    console.log('❌ 测试过程出错:', error.message);
    return false;
  }
}

// 3. 检查系统依赖
function checkSystemDependencies() {
  console.log('\n🔧 检查系统依赖...');

  // 检查字体
  const fontPaths = [
    '/usr/share/fonts/',
    '/usr/local/share/fonts/',
    '/System/Library/Fonts/',
  ];

  let fontsFound = false;
  for (const fontPath of fontPaths) {
    if (fs.existsSync(fontPath)) {
      console.log('✅ 字体目录存在:', fontPath);
      fontsFound = true;
      break;
    }
  }

  if (!fontsFound) {
    console.log('⚠️ 未找到系统字体目录，可能影响中文显示');
    console.log('💡 建议安装字体: sudo apt-get install fonts-wqy-zenhei');
  }

  // 检查临时目录
  const tempDir = '/tmp/';
  try {
    fs.accessSync(tempDir, fs.constants.W_OK);
    console.log('✅ 临时目录可写:', tempDir);
  } catch (error) {
    console.log('❌ 临时目录不可写:', tempDir);
    console.log('💡 建议: sudo chmod 777 /tmp/');
  }
}

// 主函数
async function main() {
  console.log('🔍 wkhtmltoimage 诊断工具');
  console.log('================================\n');

  // 检查安装状态
  const isInstalled = await checkWkhtmltoimage();

  if (isInstalled) {
    // 测试渲染功能
    const renderWorks = await testWkhtmltoimage();

    // 检查系统依赖
    checkSystemDependencies();

    console.log('\n📋 诊断结果:');
    console.log('- wkhtmltoimage 安装:', isInstalled ? '✅' : '❌');
    console.log('- 渲染功能:', renderWorks ? '✅' : '❌');

    if (isInstalled && renderWorks) {
      console.log('\n🎉 wkhtmltoimage 工作正常！');
      console.log('💡 如果仍有问题，可能是 Puppeteer 降级时的问题');
    } else {
      console.log('\n⚠️ wkhtmltoimage 存在问题，建议重新安装');
    }
  } else {
    console.log('\n❌ wkhtmltoimage 未正确安装');
    console.log('\n💡 安装建议:');
    console.log('Ubuntu/Debian:');
    console.log('  sudo apt-get update');
    console.log('  sudo apt-get install wkhtmltopdf');
    console.log('  sudo apt-get install xvfb  # 如果是无头服务器');
    console.log('\nCentOS/RHEL:');
    console.log('  sudo yum install wkhtmltopdf');
    console.log('  sudo yum install xorg-x11-server-Xvfb  # 如果是无头服务器');
  }
}

main().catch(console.error);
