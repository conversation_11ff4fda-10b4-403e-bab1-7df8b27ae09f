#!/usr/bin/env node

/**
 * 应用健康检查脚本
 * 监控应用状态、数据库连接、Redis连接、内存使用等关键指标
 */

const http = require('http');
const fs = require('fs');
const path = require('path');

// 健康检查配置
const HEALTH_CONFIG = {
  app: {
    host: process.env.APP_HOST || '127.0.0.1',
    port: process.env.APP_PORT || 7001,
    timeout: 5000, // 5秒超时
  },
  thresholds: {
    memoryUsage: 0.8, // 内存使用率阈值80%
    cpuUsage: 0.9, // CPU使用率阈值90%
    responseTime: 2000, // 响应时间阈值2秒
    errorRate: 0.05, // 错误率阈值5%
  },
  intervals: {
    check: 30000, // 30秒检查一次
    report: 300000, // 5分钟生成一次报告
  },
};

/**
 * 检查应用响应
 */
function checkAppResponse() {
  return new Promise((resolve, reject) => {
    const startTime = Date.now();
    
    const req = http.request({
      hostname: HEALTH_CONFIG.app.host,
      port: HEALTH_CONFIG.app.port,
      path: '/',
      method: 'GET',
      timeout: HEALTH_CONFIG.app.timeout,
    }, (res) => {
      const responseTime = Date.now() - startTime;
      let data = '';
      
      res.on('data', chunk => {
        data += chunk;
      });
      
      res.on('end', () => {
        resolve({
          status: res.statusCode,
          responseTime,
          healthy: res.statusCode === 200 && responseTime < HEALTH_CONFIG.thresholds.responseTime,
          data: data.slice(0, 100), // 只保留前100个字符
        });
      });
    });
    
    req.on('error', (error) => {
      reject({
        healthy: false,
        error: error.message,
        responseTime: Date.now() - startTime,
      });
    });
    
    req.on('timeout', () => {
      req.destroy();
      reject({
        healthy: false,
        error: 'Request timeout',
        responseTime: HEALTH_CONFIG.app.timeout,
      });
    });
    
    req.end();
  });
}

/**
 * 检查集群状态
 */
async function checkClusterStatus() {
  try {
    const response = await new Promise((resolve, reject) => {
      const req = http.request({
        hostname: HEALTH_CONFIG.app.host,
        port: HEALTH_CONFIG.app.port,
        path: '/cluster-stats',
        method: 'GET',
        timeout: HEALTH_CONFIG.app.timeout,
        headers: {
          'Accept': 'application/json',
        },
      }, (res) => {
        let data = '';
        res.on('data', chunk => data += chunk);
        res.on('end', () => {
          try {
            const parsed = JSON.parse(data);
            resolve(parsed);
          } catch (e) {
            reject(new Error('Invalid JSON response'));
          }
        });
      });
      
      req.on('error', reject);
      req.on('timeout', () => {
        req.destroy();
        reject(new Error('Request timeout'));
      });
      
      req.end();
    });
    
    if (response.success && response.data) {
      const { cluster, ipc } = response.data;
      const memoryUsage = cluster.memoryUsage.heapUsed / cluster.memoryUsage.heapTotal;
      
      return {
        healthy: memoryUsage < HEALTH_CONFIG.thresholds.memoryUsage,
        cluster: {
          pid: cluster.pid,
          workers: cluster.workers,
          uptime: cluster.uptime,
          memoryUsage: Math.round(memoryUsage * 100),
        },
        ipc: {
          messagesSent: ipc.messagesSent,
          queueLength: ipc.queueLength,
        },
      };
    }
    
    return { healthy: false, error: 'Invalid cluster response' };
  } catch (error) {
    return { healthy: false, error: error.message };
  }
}

/**
 * 检查系统资源
 */
function checkSystemResources() {
  const memUsage = process.memoryUsage();
  const cpuUsage = process.cpuUsage();
  
  // 计算内存使用率
  const heapUsageRatio = memUsage.heapUsed / memUsage.heapTotal;
  const rssUsageRatio = memUsage.rss / (require('os').totalmem());
  
  return {
    healthy: heapUsageRatio < HEALTH_CONFIG.thresholds.memoryUsage && 
             rssUsageRatio < HEALTH_CONFIG.thresholds.memoryUsage,
    memory: {
      heapUsed: Math.round(memUsage.heapUsed / 1024 / 1024), // MB
      heapTotal: Math.round(memUsage.heapTotal / 1024 / 1024), // MB
      rss: Math.round(memUsage.rss / 1024 / 1024), // MB
      external: Math.round(memUsage.external / 1024 / 1024), // MB
      heapUsageRatio: Math.round(heapUsageRatio * 100),
      rssUsageRatio: Math.round(rssUsageRatio * 100),
    },
    cpu: {
      user: cpuUsage.user,
      system: cpuUsage.system,
    },
    uptime: Math.round(process.uptime()),
  };
}

/**
 * 执行完整健康检查
 */
async function performHealthCheck() {
  const timestamp = new Date().toISOString();
  const results = {
    timestamp,
    overall: true,
    checks: {},
  };
  
  try {
    // 检查应用响应
    console.log('🔍 检查应用响应...');
    results.checks.app = await checkAppResponse();
    console.log(`   ${results.checks.app.healthy ? '✅' : '❌'} 应用响应: ${results.checks.app.responseTime}ms`);
  } catch (error) {
    results.checks.app = error;
    console.log(`   ❌ 应用响应检查失败: ${error.error}`);
  }
  
  try {
    // 检查集群状态
    console.log('🔍 检查集群状态...');
    results.checks.cluster = await checkClusterStatus();
    console.log(`   ${results.checks.cluster.healthy ? '✅' : '❌'} 集群状态: ${results.checks.cluster.cluster?.memoryUsage || 'N/A'}% 内存使用`);
  } catch (error) {
    results.checks.cluster = { healthy: false, error: error.message };
    console.log(`   ❌ 集群状态检查失败: ${error.message}`);
  }
  
  // 检查系统资源
  console.log('🔍 检查系统资源...');
  results.checks.system = checkSystemResources();
  console.log(`   ${results.checks.system.healthy ? '✅' : '❌'} 系统资源: ${results.checks.system.memory.heapUsageRatio}% 堆内存`);
  
  // 计算整体健康状态
  results.overall = Object.values(results.checks).every(check => check.healthy);
  
  return results;
}

/**
 * 保存健康检查报告
 */
function saveHealthReport(results) {
  const reportPath = path.join(__dirname, '..', 'run', 'health-reports.json');
  const reportDir = path.dirname(reportPath);
  
  // 确保目录存在
  if (!fs.existsSync(reportDir)) {
    fs.mkdirSync(reportDir, { recursive: true });
  }
  
  // 读取历史报告
  let reports = [];
  if (fs.existsSync(reportPath)) {
    try {
      reports = JSON.parse(fs.readFileSync(reportPath, 'utf8'));
    } catch (e) {
      reports = [];
    }
  }
  
  // 添加当前报告
  reports.push(results);
  
  // 只保留最近100次报告
  if (reports.length > 100) {
    reports = reports.slice(-100);
  }
  
  // 保存报告
  fs.writeFileSync(reportPath, JSON.stringify(reports, null, 2));
  console.log(`📝 健康报告已保存到: ${reportPath}`);
}

/**
 * 生成健康状态摘要
 */
function generateHealthSummary(results) {
  console.log('\n📊 健康检查摘要');
  console.log('='.repeat(50));
  console.log(`🕐 检查时间: ${new Date(results.timestamp).toLocaleString('zh-CN')}`);
  console.log(`🎯 整体状态: ${results.overall ? '✅ 健康' : '❌ 异常'}`);
  
  if (!results.overall) {
    console.log('\n⚠️ 发现问题:');
    Object.entries(results.checks).forEach(([key, check]) => {
      if (!check.healthy) {
        console.log(`   - ${key}: ${check.error || '状态异常'}`);
      }
    });
  }
  
  // 性能指标
  if (results.checks.app && results.checks.app.healthy) {
    console.log(`⚡ 响应时间: ${results.checks.app.responseTime}ms`);
  }
  
  if (results.checks.system) {
    console.log(`💾 内存使用: ${results.checks.system.memory.heapUsageRatio}%`);
    console.log(`🕐 运行时间: ${Math.floor(results.checks.system.uptime / 3600)}小时`);
  }
}

/**
 * 主函数
 */
async function main() {
  console.log('🏥 开始应用健康检查...');
  
  try {
    const results = await performHealthCheck();
    generateHealthSummary(results);
    saveHealthReport(results);
    
    // 根据健康状态设置退出码
    process.exit(results.overall ? 0 : 1);
  } catch (error) {
    console.error('❌ 健康检查失败:', error);
    process.exit(1);
  }
}

// 如果直接运行脚本
if (require.main === module) {
  main();
}

module.exports = {
  performHealthCheck,
  checkAppResponse,
  checkClusterStatus,
  checkSystemResources,
  HEALTH_CONFIG,
};
