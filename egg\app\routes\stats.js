'use strict';

/**
 * @param {Egg.Application} app - egg application
 */
module.exports = (app) => {
  const { router, controller } = app;

  router.get('/stats', controller.tz['index']);
  router.get('/stats/login', controller.tz['login']);
  router.get('/stats/data', controller.tz['getStat']);
  router.get('/stats/url', controller.tz['geturl']);
  router.get('/stats/get/:ip', controller.tz['getStat2']);
  router.get('/stats/get', controller.tz['getStat2']);
  router.get('/stats/get1', controller.tz['getStat3']);
  router.post('/stats/get1', controller.tz['getStat3']);
  router.get('/stats/get4', controller.tz['getStat4']);

  router.get('/stats/get5', controller.tz['getStat5']);
  router.post('/stats/get5', controller.tz['getStat5']);

  router.post('/stats/add', controller.tz['add']);
  router.post('/stats/del', controller.tz['del']);
  router.post('/stats/update', controller.tz['update']);
};
