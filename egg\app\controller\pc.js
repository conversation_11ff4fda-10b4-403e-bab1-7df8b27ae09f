const axios = require('axios');
const cheerio = require('cheerio');
const { dayformat, dateformat, dateNow } = require('../extend/helper');
const { allair, rejc } = require('../extend/air');
const fs = require('fs');
const { log } = require('console');
const Controller = require('egg').Controller;
const ipRangeCheck = require('ip-range-check');
const yaml = require('yaml');
const dnsPacket = require('dns-packet');
const base64url = require('base64url');
const { exec } = require('child_process');

class NewsController extends Controller {
  async url() {
    const ctx = this.ctx;
    let id = ctx.query.id;
    let type = ctx.query.type;
    let page = ctx.query.page;
    let url;
    if (page === '0') {
      url = 'https://www.xiurenji.cc/' + type + '/' + id + '.html';
    } else {
      url = 'https://www.xiurenji.cc/' + type + '/' + id + '_' + page + '.html';
    }

    ctx.body = await ctx.service.xr.list(url);
  }

  async xrinfo() {
    const ctx = this.ctx;
    let xrurl = ctx.query.url;
    let urlfg = xrurl.split('/');
    let type = urlfg[3];
    let id = urlfg[4].split('.')[0];
    let page = await ctx.service.xr.page(xrurl);
    let arr = [];
    for (let i = 0; i <= page; i++) {
      let xurl = '';
      if (i === 0) {
        xurl = 'https://www.xiurenji.cc/' + type + '/' + id + '.html';
      } else {
        xurl = 'https://www.xiurenji.cc/' + type + '/' + id + '_' + i + '.html';
      }
      let urld = await ctx.service.xr.list(xurl);
      for (let turl of urld) {
        arr.push(turl);
      }
    }
    ctx.body = arr;
  }

  async all() {
    const ctx = this.ctx;
    let xrurl = ctx.query.url;
    let urlfg = xrurl.split('/');
    let urltype = urlfg[3];
    let urlid = urlfg[4].split('.')[0];

    await ctx.render('xr.html', { id: urlid, type: urltype });
  }

  async bb() {
    const ctx = this.ctx;
    // let page = ctx.query.page;
    // let imgurl = [];
    // for (let i = 350; i <= 351; i++) {
    //     imgurl.push('https://kr.35551049.xyz/bb/' + i + '.png');
    // }
    //
    // await ctx.render('bb.html', {page: page, imgurl: imgurl});
    const appkey = this.app.config.keys;
    ctx.body = { app: appkey };
  }

  async info() {
    const ctx = this.ctx;
    let id = ctx.query.id;
    let type = ctx.query.type;
    let xrurl = 'https://www.xiurenji.cc/' + type + '/' + id + '.html';
    ctx.body = await ctx.service.xr.page(xrurl);
  }

  async show() {
    const ctx = this.ctx;
    await ctx.render('x.html');
  }

  //封面

  async nav() {
    const ctx = this.ctx;
    ctx.body = await ctx.service.xr.nav();
  }

  async locid() {
    const ctx = this.ctx;
    ctx.body = await ctx.service.xr.locid();
  }

  async zw() {
    const ctx = this.ctx;
    let data = await ctx.service.xr.select('zzsydw');
    let res = data.data;
    for (let item of res) {
      let options = {
        where: {
          id: item.id,
        },
      };
      if (item.zy === null) {
        let zy = await this.zwxq(item.link);
        item.zy = zy.zy;
        ctx.body = await ctx.service.xr.update('zzsydw', item, options);
      }
    }
  }

  //职位详情
  async zwxq(url) {
    const ctx = this.ctx;
    // let url = 'http://sys.zzksbm.com:26999/web_files/staticHtmls/posts/3/5/post_id=260.html';

    let data = await ctx.service.xr.zwxq(url);
    let zw = {};
    zw.zy = data[0];
    return zw;
    // ctx.body = zw;
  }

  async sydwsql() {
    const ctx = this.ctx;
    // let res = [];
    let data = await ctx.service.xr.zz1(43);
    // ctx.body = data;
    // return;
    for (let item of data) {
      let date = new Date();
      let year = date.getFullYear();
      let month = date.getMonth() + 1;
      let day = date.getDate();
      let Hours = date.getHours();
      let Minutes = date.getMinutes();
      let Seconds = date.getSeconds();
      if (month < 10) {
        month = '0' + month;
      }
      if (day < 10) {
        day = '0' + day;
      }
      let s_createtime =
        year +
        '-' +
        month +
        '-' +
        day +
        ' ' +
        Hours +
        ':' +
        Minutes +
        ':' +
        Seconds;

      let x = {};
      x.dw = item.dw;
      x.id = item.id;
      x.dm = item.dm;
      x.zprs = item.zprs;
      x.dsh = item.dsh;
      x.shtg = item.shtg;
      x.shbtg = item.shbtg;
      x.link = item.link;
      x.uptime = s_createtime;
      if (x.id) {
        // res.push(x);
        let options = {
          where: {
            id: x.id,
          },
        };
        ctx.body = await ctx.service.xr.update('zzsydw', x, options);
      }
    }

    // ctx.body = res;
  }

  async zzz() {
    const ctx = this.ctx;
    let data = await ctx.service.xr.zz();

    let i = 0;
    for (let item of data) {
      let date = new Date();
      let year = date.getFullYear();
      let month = date.getMonth() + 1;
      let day = date.getDate();
      let Hours = date.getHours();
      let Minutes = date.getMinutes();
      let Seconds = date.getSeconds();
      if (month < 10) {
        month = '0' + month;
      }
      if (day < 10) {
        day = '0' + day;
      }
      let s_createtime =
        year +
        '-' +
        month +
        '-' +
        day +
        ' ' +
        Hours +
        ':' +
        Minutes +
        ':' +
        Seconds;
      let options = {
        where: {
          id: item.id,
        },
      };
      item.uptime = s_createtime;
      console.log('分隔符==================');
      if (item.id) {
        let cr = await ctx.service.xr.update('zz', item, options);
        console.log(cr);
      }
      console.log(item.id);
      console.log('分隔符==================');
      i++;
    }
    ctx.body = i;

    // ctx.body = data;
    // return;
    // await ctx.render('zz.html', { list: JSON.stringify(data) });
  }

  async zz() {
    function in_array(search, array) {
      for (let i in array) {
        if (array[i] === search) {
          return true;
        }
      }
      return false;
    }

    const ctx = this.ctx;
    let all = ctx.query.all;
    let data = await ctx.service.xr.select('zz');
    let jzsj = 12;
    // let jzsj = await ctx.service.xr.jzsj();
    // ctx.body = data;
    // return;
    let ids = [
      100101, 100203, 100802, 101301, 101303, 101406, 101608, 101708, 101710,
      101903, 102103, 102301, 102303, 102503, 102505, 102709, 103105, 103406,
      260501, 220403, 221204, 132801, 134601, 137001, 120303, 114104,
    ];
    let x = [];
    for (let item of data.data) {
      item.total = item.shtg + item.dsh;

      if (all) {
        x.push(item);
      } else if (in_array(item.id, ids)) {
        x.push(item);
      }
    }

    await ctx.render('zz.html', { list: JSON.stringify(x), jzsj: jzsj });
  }

  async zzsydw1() {
    function in_array(search, array) {
      for (let i in array) {
        if (array[i] === search) {
          return true;
        }
      }
      return false;
    }

    const ctx = this.ctx;
    let all = ctx.query.all;
    let json = ctx.query.json;
    let time1 = ctx.query.time;
    let page = ctx.query.page;
    let time = '';
    let data1 = await ctx.service.xr.zz1(1);
    let data = await ctx.service.xr.select('zzsydw');
    data = data.data;
    let res = [];
    // ctx.body = data;
    // let ids = [100301, 100302, 100303, 100304, 100601, 101003, 101101, 101301, 101402, 101810, 101812, 102801, 103404, 103405, 103406, 103407, 103408, 103504, 103505, 103701, 103807, 104001, 104201, 104203, 104204, 104301, 104502, 104510, 104511, 104902, 104904, 105008, 105601, 110304, 110401, 110402, 110701, 120403, 120601, 120602, 120901, 120902, 130302, 133101, 133601, 200101, 200102, 200301, 200401, 200402, 201501, 202301, 203101, 203301, 221401];
    let ids = [
      100301, 100302, 100303, 100304, 100601, 101003, 101101, 101301, 101402,
      101810, 101812, 102801, 103404, 103405, 103406, 103407, 103408, 103504,
      103505, 103701, 103807, 104001, 104201, 104203, 104204, 104301, 104502,
      104510, 104511, 104902, 104904, 105008, 105601, 110304, 110401, 110402,
      110701, 120403, 120601, 120602, 120901, 120902, 130302, 133101, 133601,
      140101, 140201, 140603, 142301, 142401, 142901, 142902, 143201, 143302,
      145002, 145003, 145301, 150701, 150702, 150801, 150802, 150901, 150902,
      160903, 160906, 161601, 162803, 163201, 163301, 172201, 172501, 174201,
      180701, 180801, 181801, 181802, 184512, 184513, 184518, 190301, 190302,
      190501, 190502, 190601, 190602, 190701, 191101, 191102, 193001, 193002,
      193801, 194501, 200101, 200102, 200301, 200401, 200402, 201501, 202301,
      203101, 203301, 210103, 210104, 210105, 211601, 211701, 211702, 212201,
      213301, 213905, 221401, 250103, 260803, 260903,
    ];

    let dsh = 0;
    let shtg = 0;
    let shbtg = 0;
    let total = 0;
    let zprs = 0;
    for (let d of data) {
      d.total = (d.shtg + d.dsh) / 1;

      if (all && d.id) {
        zprs = zprs + d.zprs;
        dsh = dsh + d.dsh;
        shtg = shtg + d.shtg;
        shbtg = shbtg + d.shbtg;
        total = total + d.total;
        res.push(d);
      } else if (in_array(d.id, ids)) {
        zprs = zprs + d.zprs;
        dsh = dsh + d.dsh;
        shtg = shtg + d.shtg;
        shbtg = shbtg + d.shbtg;
        total = total + d.total;
        res.push(d);
      }
    }
    let heji = {
      dsh: dsh,
      shtg: shtg,
      shbtg: shbtg,
      total: total,
      zprs: zprs,
    };
    res.push(heji);
    time = data1[0].time;
    if (time1) {
      let o = {
        where: {
          id: 2,
        },
      };
      let cr = await ctx.service.xr.select('time', o);
      let time2 = cr.data[0].time;
      if (time === time2) {
        // console.log(time)
        // console.log(time2)
        // console.log(1)
      } else {
        // console.log(time)
        // console.log(time2)
        // console.log(2)
        await ctx.service.tele.dd(time);
      }
      ctx.body = cr;
      return;
    }

    // let reg = /(([0-9]{3}[1-9]|[0-9]{2}[1-9][0-9]{1}|[0-9]{1}[1-9][0-9]{2}|[1-9][0-9]{3})-(((0[13578]|1[02])-(0[1-9]|[12][0-9]|3[01]))|((0[469]|11)-(0[1-9]|[12][0-9]|30))|(02-(0[1-9]|[1][0-9]|2[0-8]))))|((([0-9]{2})(0[48]|[2468][048]|[13579][26])|((0[48]|[2468][048]|[3579][26])00))-02-29)/g
    // let reg1 = /([01]?\d|2[0-3]):[0-5]?\d:[0-5]?\d/g
    // let date = time.match(reg)
    // let time1 = time.match(reg1)
    //
    // console.log(date);
    // console.log(time1);
    if (json) {
      ctx.body = res;
      return;
    }
    await ctx.render('zz.html', { time: time, page: page, all: all });
  }

  async checkid() {
    const ctx = this.ctx;
    let type = ctx.query.type;
    let uid = await ctx.service.xr.locid();
    uid = parseInt(uid);
    if (type === 'all') {
      let text = '最新uid是' + uid;
      await ctx.service.tele.push(text, 2);
    }
    if (uid === 53887 || uid === 53886) {
      let text = '最新uid是' + uid;
      await ctx.service.tele.dd(text);
      await ctx.service.tele.push(text);
    }
    ctx.body = { uid: uid };
  }

  // async getcsrf() {
  //     const ctx = this.ctx;
  //     ctx.body = {data: ctx.csrf};
  //     return;
  //     const exec = require('child_process').exec;
  //     let cmd = 'bash /home/<USER>/update.sh';
  //
  //     let data = exec(cmd, function (error, stdout, stderr) {
  //         console.log(stdout);
  //         return stdout;
  //     });
  //     ctx.body = {data: data};
  // }

  async bs() {
    const ctx = this.ctx;
    const util = require('util');
    const exec = util.promisify(require('child_process').exec);
    const cmd = 'bash /home/<USER>/update.sh > /root/egg.log';

    try {
      // 使用 await 等待命令执行完成
      const { stdout, stderr } = await exec(cmd);

      // 返回命令的标准输出和错误输出
      ctx.body = { stdout, stderr };
    } catch (error) {
      // 如果执行失败，返回错误信息
      console.error('Execution error:', error);
      ctx.body = { error: error.message };
    }
  }

  async ydhm8() {
    const ctx = this.ctx;
    let citycode = [591, 592, 593, 594, 595, 596, 597, 598, 599];
    // let citycode = ['591', '596']
    let aaa = [
      '000',
      '111',
      '222',
      '333',
      '444',
      '555',
      '666',
      '777',
      '888',
      '999',
    ];
    let arrn = [];

    function func(a, b) {
      return a - b;
    }

    for (let qh of citycode) {
      // console.log(qh);
      arrn[qh] = [];
      for (let a of aaa) {
        let data = await ctx.service.tele.ydbm(a, qh);
        for (const datum of data) {
          let num = datum.split('|');
          if (!RegExp('4').test(num)) {
            arrn[qh].push(num[0]);
          }
        }
        arrn[qh].sort(func);
      }
    }
    let numlist = {};
    numlist.fz = arrn['591'];
    numlist.xm = arrn['592'];
    numlist.nd = arrn['593'];
    numlist.pt = arrn['594'];
    numlist.qz = arrn['595'];
    numlist.zz = arrn['596'];
    numlist.ly = arrn['597'];
    numlist.sm = arrn['598'];
    numlist.np = arrn['599'];
    ctx.body = numlist;
  }

  async ydhm8c() {
    const ctx = this.ctx;
    let citycode = [591, 592, 593, 594, 595, 596, 597, 598, 599];
    // let citycode = ['591', '596']
    let aaa = [
      '000',
      '111',
      '222',
      '333',
      '444',
      '555',
      '666',
      '777',
      '888',
      '999',
    ];
    let arrn = [];
    for (let qh of citycode) {
      for (let a of aaa) {
        let data = await ctx.service.tele.ydbm(a, qh);
        for (const datum of data) {
          let num = datum.split('|');
          arrn.push(num[0]);
        }
      }
    }

    ctx.body = await this.c3a(arrn, 'test:');
  }

  async ydfour() {
    const ctx = this.ctx;
    let data = await ctx.service.xr.select('ydhm', {
      orders: [['ydhm', 'asc']],
    });
    let num = [];
    for (let i of data.data) {
      num.push(i.ydhm);
    }
    ctx.body = num;
  }

  async ydnofour() {
    const ctx = this.ctx;
    let arrn = [];
    let data = await ctx.service.tele.ydnofo();
    for (const datum of data) {
      let num = datum.split('|');
      arrn.push(num[0]);
    }
    let arr = [];
    let i = 0;
    for (let sz of arrn) {
      let haoma = sz / 1;
      sz = sz.toString();
      let qm = sz.substr(0, 3);
      let zj = sz.substr(3, 4);
      let hm = sz.substr(7, 4);
      if (!new RegExp('4').test(sz)) {
        if (!new RegExp('19').test(qm) && !new RegExp('17').test(qm)) {
          if (
            new RegExp('000').test(sz) ||
            new RegExp('111').test(sz) ||
            new RegExp('222').test(sz) ||
            new RegExp('333').test(sz) ||
            new RegExp('444').test(sz) ||
            new RegExp('555').test(sz) ||
            new RegExp('666').test(sz) ||
            new RegExp('777').test(sz) ||
            new RegExp('888').test(sz) ||
            new RegExp('999').test(sz)
          ) {
            let text = '最新移动3A号码是' + sz;
            await ctx.service.tele.dd2(text);
          }

          arr[i] = {
            num: sz,
            zj: zj,
            hm: hm,
            res: 'true',
          };
          i++;
          let ishm = await ctx.service.xr['find']('ydhm', {
            ydhm: haoma,
          });
          if (ishm.data) {
            ishm = '';
          } else {
            await ctx.service.xr.create('ydhm', {
              ydhm: haoma,
            });
            let text = '最新没4号码是' + sz;
            await ctx.service.tele.dd(text);
          }

          // await ctx.service.tele.push(text, 2);
        } else {
          // arr[i] = {
          //     num: sz, zj: zj, hm: hm, res: 'false'
          // };
          i++;
        }
      }
    }
    ctx.body = { hm: arrn, no4: arr };
  }

  async ydhm() {
    const ctx = this.ctx;
    let citycode = ctx.params.id || 596;
    let arrn = [];
    let aaa = [
      '000',
      '111',
      '222',
      '333',
      '444',
      '555',
      '666',
      '777',
      '888',
      '999',
    ];
    for (const a of aaa) {
      let data = await ctx.service.tele.ydbm(a, citycode);
      for (const datum of data) {
        let num = datum.split('|');
        arrn.push(num[0]);
      }
    }

    let arr = [];
    let i = 0;
    for (let sz of arrn) {
      sz = sz.toString();
      let qm = sz.substr(0, 3);
      let zj = sz.substr(3, 4);
      let hm = sz.substr(7, 4);
      let xx = '18698303335';

      if (
        !new RegExp('4').test(sz) &&
        !new RegExp('19').test(qm) &&
        !new RegExp('17').test(qm)
      ) {
        if (
          new RegExp('000').test(sz) ||
          new RegExp('111').test(sz) ||
          new RegExp('222').test(sz) ||
          new RegExp('333').test(sz) ||
          new RegExp('444').test(sz) ||
          new RegExp('555').test(sz) ||
          new RegExp('666').test(sz) ||
          new RegExp('777').test(sz) ||
          new RegExp('888').test(sz) ||
          new RegExp('999').test(sz)
        ) {
          let text = '最新' + citycode + '移动3A号码是' + sz;
          await ctx.service.tele.dd(text);
        }
        if (
          new RegExp('000').test(zj) ||
          new RegExp('111').test(zj) ||
          new RegExp('222').test(zj) ||
          new RegExp('333').test(zj) ||
          new RegExp('444').test(zj) ||
          new RegExp('555').test(zj) ||
          new RegExp('666').test(zj) ||
          new RegExp('777').test(zj) ||
          new RegExp('888').test(zj) ||
          new RegExp('999').test(zj) ||
          new RegExp('000').test(hm) ||
          new RegExp('111').test(hm) ||
          new RegExp('222').test(hm) ||
          new RegExp('333').test(hm) ||
          new RegExp('444').test(hm) ||
          new RegExp('555').test(hm) ||
          new RegExp('666').test(hm) ||
          new RegExp('777').test(hm) ||
          new RegExp('888').test(hm) ||
          new RegExp('999').test(hm)
        ) {
          arr[i] = {
            num: sz,
            zj: zj,
            hm: hm,
            res: 'true',
          };
          i++;
          let text = '最新' + citycode + '移动3A号码是' + sz;
          await ctx.service.tele.dd2(text);
        } else {
          arr[i] = {
            num: sz,
            zj: zj,
            hm: hm,
            res: 'false',
          };
          i++;
        }
      }
    }

    ctx.body = arrn;
  }

  async ltnum() {
    const ctx = this.ctx;
    let data = await ctx.service.tele.num();
    let arr = [];
    let i = 0;
    let notingtodo = 0;

    for (let sz of data) {
      sz = sz.toString();
      let zj = sz.substr(3, 4);
      let hm = sz.substr(7, 4);
      if (
        new RegExp('000').test(sz) ||
        new RegExp('111').test(sz) ||
        new RegExp('222').test(sz) ||
        new RegExp('333').test(sz) ||
        new RegExp('444').test(sz) ||
        new RegExp('555').test(sz) ||
        new RegExp('666').test(sz) ||
        new RegExp('777').test(sz) ||
        new RegExp('888').test(sz) ||
        new RegExp('999').test(sz)
      ) {
        let text = '最新3A号码是' + sz;
        await ctx.service.tele.push(text, 3);
      }
      if (
        new RegExp('000').test(zj) ||
        new RegExp('111').test(zj) ||
        new RegExp('222').test(zj) ||
        new RegExp('333').test(zj) ||
        new RegExp('444').test(zj) ||
        new RegExp('555').test(zj) ||
        new RegExp('666').test(zj) ||
        new RegExp('777').test(zj) ||
        new RegExp('888').test(zj) ||
        new RegExp('999').test(zj) ||
        new RegExp('000').test(hm) ||
        new RegExp('111').test(hm) ||
        new RegExp('222').test(hm) ||
        new RegExp('333').test(hm) ||
        new RegExp('444').test(hm) ||
        new RegExp('555').test(hm) ||
        new RegExp('666').test(hm) ||
        new RegExp('777').test(hm) ||
        new RegExp('888').test(hm) ||
        new RegExp('999').test(hm)
      ) {
        arr[i] = {
          num: sz,
          zj: zj,
          hm: hm,
          res: 'true',
        };
        i++;
        let text = '最新3A号码是' + sz;
        if (new RegExp('4').test(sz)) {
          notingtodo = 1;
        } else {
          await ctx.service.tele.dd(text);
          await ctx.service.tele.push(text, 2);
        }
      } else {
        arr[i] = {
          num: sz,
          zj: zj,
          hm: hm,
          res: 'false',
        };
        i++;
      }
    }
    ctx.body = arr;
  }

  async virmach() {
    // const ctx = this.ctx;
    const { ctx, app } = this;

    let resp = await axios
      .get(
        'https://billing.virmach.com/modules/addons/blackfriday/new_plan.json',
      )
      .then(function (response) {
        return response.data;
      });
    // if (resp.location.match("SAN JOSE")) {

    let txt1 =
      resp.location +
      '\n' +
      resp.price
        .replace('</span>', '')
        .replace('<span>', '')
        .replace('/yr', '')
        .trim() +
      '/年 - ' +
      resp.message +
      '\n' +
      '\n' +
      resp.cpu +
      ' 核心\n' +
      resp.ram +
      'MB 内存\n' +
      resp.hdd +
      'GB 硬盘\n' +
      resp.bw +
      'GB 流量\n' +
      resp.ips +
      ' IP\n' +
      '\n' +
      '立即抢购\n' +
      'https://billing.virmach.com/aff.php?aff=cnm&pid=196\n' +
      '\n' +
      '来源 virmach.app';
    let planid = await app.redis.get('planid');
    if (planid / 1 !== resp.planid / 1) {
      await app.redis.set('planid', resp.planid);
      await ctx.service.tele.push(txt1, 2);
      if (resp.location.match('SAN JOSE')) {
        let text = '圣何塞来了';
        await ctx.service.tele.push(text);
      }
      let price = resp.price
        .replace('$', '')
        .replace('</span>', '')
        .replace('<span>', '')
        .replace('/yr', '')
        .trim();
      if (price / 1 <= 8) {
        let text = '8刀以下';
        await ctx.service.tele.push(text);
      }
    }

    ctx.body = {
      price: resp.price
        .replace('</span>', '')
        .replace('<span>', '')
        .replace('/yr', '')
        .trim(),
      virt: resp.virt,
      ram: resp.ram,
      cpu: resp.cpu,
      hdd: resp.hdd,
      bw: resp.bw,
      ips: resp.ips,
      pid: resp.pid,
      message: resp.message,
      location: resp.location,
      windows: resp.windows,
      planid: resp.planid,
      ended: resp.ended,
    };
  }

  async vir() {
    const ctx = this.ctx;
    let resp = await axios
      .get(
        'https://billing.virmach.com/modules/addons/blackfriday/new_plan.json',
      )
      .then(function (response) {
        return response.data;
      });
    if (resp.location.match('SAN JOSE')) {
      let text = '圣何塞来了';
      await ctx.service.tele.push(text, 2);
    }

    let data = {
      price: resp.price
        .replace('</span>', '')
        .replace('<span>', '')
        .replace('/yr', '')
        .trim(),
      virt: resp.virt,
      ram: resp.ram,
      cpu: resp.cpu,
      hdd: resp.hdd,
      bw: resp.bw,
      ips: resp.ips,
      pid: resp.pid,
      message: resp.message,
      location: resp.location,
      windows: resp.windows,
      planid: resp.planid,
      ended: resp.ended,
    };

    let data1 = [data];

    // let data = await ctx.service.xr.select('zz');
    // await ctx.render('vir.html', {data: data1});
    // ctx.body = data1;
    await ctx.render('vir.html', { data: JSON.stringify(data1) });
  }

  async redis() {
    const { ctx, app } = this;
    ctx.body = await app.redis.get('name');
  }

  async tq() {
    const { ctx } = this;
    let resp = await axios
      .get('https://aider.meizu.com/app/weather/listWeather?cityIds=101230601')
      .then(function (response) {
        return response.data;
      });
    ctx.body = resp;
  }

  async hhhk() {
    const { ctx } = this;
    let resp = await axios
      .get(
        'https://ovz.control-panel.in:4083/index.php?act=listvs&api=json&apikey=OCNOZKBQX2VCLOBI&apipass=9hf0hqdb75aphx1vcvsdtyynxdc4w0lu&do=1',
      )
      .then(function (response) {
        return response.data;
      });
    let vs = resp.vs['1917'].status;
    if (vs === 0) {
      let data = await axios
        .get(
          'https://ovz.control-panel.in:4083/index.php?svs=1917&act=start&api=json&apikey=OCNOZKBQX2VCLOBI&apipass=9hf0hqdb75aphx1vcvsdtyynxdc4w0lu&do=1',
        )
        .then(function (response) {
          return response.data;
        });
      ctx.body = data;
    } else if (vs === 1) {
      ctx.body = '已开机';
    } else {
      ctx.body = vs;
    }
  }

  async gullochi() {
    const { ctx } = this;
    let resp = await axios
      .get(
        'https://solusvm.gullo.me/api/client/command.php?key=9MSH0-PO2BG-LCEFW&hash=8dd2f182460ab25966c00222d40daad7d569ceb0&action=status',
      )
      .then(function (response) {
        return response.data;
      });
    if (resp.match('offline')) {
      // console.log("offline");
      axios
        .get(
          'https://solusvm.gullo.me/api/client/command.php?key=9MSH0-PO2BG-LCEFW&hash=8dd2f182460ab25966c00222d40daad7d569ceb0&action=boot',
        )
        .then(function (response) {
          return response.data;
        });
      ctx.body = resp;
    } else {
      ctx.body = '已开机';
      // console.log("online");
    }
  }

  async c3a(hmlist, content = '最新沃小号3A号码是') {
    const ctx = this.ctx;
    let arr = [];
    let i = 0;
    for (let sz of hmlist) {
      sz = sz.toString();
      let zj = sz.substr(3, 4);
      let hm = sz.substr(7, 4);
      if (
        new RegExp('000').test(sz) ||
        new RegExp('111').test(sz) ||
        new RegExp('222').test(sz) ||
        new RegExp('333').test(sz) ||
        new RegExp('444').test(sz) ||
        new RegExp('555').test(sz) ||
        new RegExp('666').test(sz) ||
        new RegExp('777').test(sz) ||
        new RegExp('888').test(sz) ||
        new RegExp('999').test(sz)
      ) {
        let text = content + sz;
        await ctx.service.tele.push(text, 3);
      }
      if (
        new RegExp('000').test(zj) ||
        new RegExp('111').test(zj) ||
        new RegExp('222').test(zj) ||
        new RegExp('333').test(zj) ||
        new RegExp('444').test(zj) ||
        new RegExp('555').test(zj) ||
        new RegExp('666').test(zj) ||
        new RegExp('777').test(zj) ||
        new RegExp('888').test(zj) ||
        new RegExp('999').test(zj) ||
        new RegExp('000').test(hm) ||
        new RegExp('111').test(hm) ||
        new RegExp('222').test(hm) ||
        new RegExp('333').test(hm) ||
        new RegExp('444').test(hm) ||
        new RegExp('555').test(hm) ||
        new RegExp('666').test(hm) ||
        new RegExp('777').test(hm) ||
        new RegExp('888').test(hm) ||
        new RegExp('999').test(hm)
      ) {
        arr[i] = {
          num: sz,
          zj: zj,
          hm: hm,
          res: 'true',
        };
        i++;
        let text = content + sz;
        await ctx.service.tele.dd(text);
        await ctx.service.tele.push(text, 2);
      } else {
        arr[i] = {
          num: sz,
          zj: zj,
          hm: hm,
          res: 'false',
        };
        i++;
      }
    }
    return arr;
  }

  async wxh() {
    // axios.get('http://i.wcy9.com/wxh.php')
    //     .then(function (response) {
    //         console.log(response.data)
    //         return response.data
    //     })
    //
    // return;
    const ctx = this.ctx;
    let date = await ctx.service.tele.wxh();
    // date = JSON.parse(date);
    ctx.body = this.c3a(date.woxiaohao, '最新沃小号3A号码是');
  }

  async nfc() {
    const ctx = this.ctx;
    let data = await ctx.service.tele.nfcarm();
    let code = data.data.code / 1;
    let code6 = data.data.code6 / 1;
    let txt1 = '';
    if (code === 1) {
      txt1 += 'ipv4 ' + data.data.message + '\n';
    }
    if (code6 === 1) {
      txt1 = txt1 + 'ipv6 ' + data.data.message6 + '\n';
    }
    await ctx.service.tele.push(txt1, 3);

    let txt2 = '';
    if (code === 0) {
      txt2 += 'sgarm ipv4 原生IP失效了！' + '\n';
      // if (code6 === 0) {
      //     txt2 += "sgdo ipv6 原生IP失效了！" + "\n"
      // }
      await ctx.service.tele.push(txt2, 2);
    }

    ctx.body = data;
  }

  async gwy() {
    function in_array(search, array) {
      for (let i in array) {
        if (array[i] === search) {
          return true;
        }
      }
      return false;
    }

    const ctx = this.ctx;
    let all = ctx.query.all;
    let data = await ctx.service.xr.gwy();

    await ctx.render('gwy.html', { list: JSON.stringify(data) });
  }

  async zzrc() {
    const { ctx, app } = this;
    let tele = ctx.query.tele ? ctx.query.tele : 'false';
    let d = [];
    let data = await ctx.service.xr.zzrc(65);
    let data1 = await ctx.service.xr.zzrc(63);
    let data2 = await ctx.service.xr.zzrc(61);
    let data3 = await ctx.service.xr.zzrc(71);

    // for (let item of data1.rows) {
    //     if (item.epId === 2603) {
    //         d.push(item);
    //     }
    // }
    // for (let item of data.rows) {
    //     // if (item.epId === 2772) {
    //     //     d.push(item);
    //     // }
    //     if (item.epId === 2779) {
    //         d.push(item);
    //     }
    // }
    // for (let item of data2.rows) {
    //     if (item.epId === 2493) {
    //         d.push(item);
    //     }
    // }
    for (let item of data3.rows) {
      if (item.epId === 3072) {
        d.push(item);
      }
    }
    let text = '';
    for (let item of d) {
      text +=
        item.epUnitName +
        item.epPostName +
        '\n' +
        '总报名人数' +
        item.allCount +
        '  审核通过' +
        item.passCount +
        '  审核不通过' +
        item.notPassCount +
        '  待审核' +
        item.examineCount +
        '\n' +
        '\n';
    }
    let rtext = await app.redis.get('zzrc');
    if (text === rtext) {
      ctx.body = text;
    } else {
      await app.redis.set('zzrc', text);
      if (tele !== 'false') {
        await ctx.service.tele.push(text, 2);
        await ctx.service.tele.push(text, 3);
      } else {
        await ctx.service.tele.dd(text);
        await ctx.service.tele.dd2(text);
      }
    }

    ctx.body = text;

    // await ctx.render('gwy.html', {list: JSON.stringify(data)});
  }

  async zzrczkz() {
    const { ctx, app } = this;
    let data = await ctx.service.xr.zzrczkz();
    let d = [];
    for (let item of data.rows) {
      if (item.eiId === 61 || item.eiId === 63) {
        let text = '';
        if (!item.eiWritePrintStartTime) {
          ctx.body = item.eiName;
          return;
        } else {
          text =
            item.eiName +
            '打印笔试准考证\n' +
            item.eiWritePrintStartTime +
            ' 至 ' +
            item.eiWritePrintEndTime;
        }
        let rtext = await app.redis.get('zzrczkz' + item.eiId);
        if (text === rtext) {
        } else {
          await app.redis.set('zzrczkz' + item.eiId, text);
          if (tele !== 'false') {
            await ctx.service.tele.push(text, 2);
            await ctx.service.tele.push(text, 3);
          } else {
            await ctx.service.tele.dd(text);
            await ctx.service.tele.dd2(text);
          }
        }
        d.push(item);
      }
    }
    ctx.body = d;
  }

  async zzsydw() {
    const ctx = this.ctx;
    let data = await ctx.service.xr.zzrcsydw();
    for (let item of data.rows) {
      item.sjrs = item.passCount + item.examineCount;
    }
    ctx.body = data.rows;
  }

  async zzrcsydw() {
    const { ctx, app } = this;

    await ctx.render('zzrc/zzrc.html');

    // ctx.body = data.rows;
  }

  async lizhi() {
    const { ctx, app } = this;
    let tele = ctx.query.tele ? ctx.query.tele : 'false';
    let downie = {
      goods_id: 280, // "specJSON[0][id]": 38390,
      // "specJSON[0][type]": 1,
      // "specJSON[0][value]": "单用户",
      // "specJSON[0][name]": "授权类型",
      // "specJSON[0][tip]": "单用户",
      // "specJSON[1][id]": 15021,
      // "specJSON[1][type]": 1,
      // "specJSON[1][value]": "Mac",
      // "specJSON[1][name]": "运行平台",
      // "specJSON[1][tip]": "Mac",
      // "random": "0.20057421955887134",
    };
    let typora = {
      goods_id: 520, // "specJSON[0][id]": 20986,
      // "specJSON[0][type]": 1,
      // "specJSON[0][value]": "单用户版",
      // "specJSON[0][name]": "授权类型",
      // "specJSON[0][tip]": "单用户版",
      // "random": "0.30724748110303723"
    };
    let updf = {
      goods_id: 596, // "specJSON[0][id]": 41779,
      // "specJSON[0][type]": 1,
      // "specJSON[0][value]": "终生专业版",
      // "specJSON[0][name]": "授权类型",
      // "specJSON[0][tip]": "终生专业版",
      // "random": "0.30724748110303723"
    };

    let list = [
      {
        name: 'typora',
        goods_id: 520,
      },
      {
        name: 'dowine',
        goods_id: 280,
      },
      {
        name: 'updf',
        goods_id: 596,
      },
      {
        name: 'Listary',
        goods_id: 58,
      },
    ];
    let goods = [];
    for (let item of list) {
      item.random = 0.7107352517826528;
      let data = await ctx.service.xr.lizhi(item);
      data = data.data;
      let d = {
        id: data.id,
        name: data.name,
        min_goods_sell_price: data.min_goods_sell_price,
        max_goods_sell_price: data.max_goods_sell_price,
        min_sku_discount_price: data.min_sku_discount_price,
        max_sku_discount_price: data.max_sku_discount_price,
        discount_price: data.discount_price,
      };
      let rtext = await app.redis.get('lizhi' + d.id);
      let text = '';
      text +=
        '商品： ' +
        data.name +
        '\n' +
        '最低价格:' +
        data.min_goods_sell_price +
        '\n最高价格:' +
        data.max_goods_sell_price +
        '\n最低折扣价格:' +
        data.min_sku_discount_price +
        '\n最高折扣价格:' +
        data.max_sku_discount_price +
        '\n' +
        '\n';

      if (text !== rtext) {
        await app.redis.set('lizhi' + d.id, text);
        if (tele !== 'false') {
          await ctx.service.tele.push(text, 2);
          await ctx.service.tele.push(text, 3);
        } else {
          await ctx.service.tele.dd(text);
          await ctx.service.tele.dd2(text);
        }
      }
      goods.push(d);
    }

    ctx.body = goods;
  }

  async zzsydwweihu() {
    const { ctx } = this;
    let url = 'https://zzksbm.com/';

    // let data = await ctx.service.xr.zzsydwweihu();
    let res = await axios.get(url).then(function (response) {
      let odata = cheerio.load(response.data);
      return odata('h1').text();
    });
    if (res === '系统维护中...') {
      // console.log(1)
      // await ctx.service.tele.dd("漳州事业单位" + res);
      // await ctx.service.tele.dd2("漳州事业单位" + res);
      ctx.body = res;
    } else {
      // console.log(2)
      await ctx.service.tele.dd('漳州事业单位系统没有维护中...');
      await ctx.service.tele.dd2('漳州事业单位系统没有维护中...');
      ctx.body = res;
    }
  }

  async ruankaobaoming() {
    const { ctx } = this;

    // Step 1: 发送第一次请求，获取Cookie
    const initialResponse = await axios.get('https://www.ruankao.org.cn/', {
      headers: {
        'User-Agent':
          'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/116.0.0.0 Safari/537.36',
      },
    });
    // const cookie = initialResponse.headers['set-cookie'].join('; ');
    // console.log(cookie)
    // return;
    // 获取Cookie
    const cookieString = 'acw_sc__v2=64f6825cddef1f314d78abc26f56cf166bfce1af';
    // Step 2: 使用获取的Cookie发送第二次请求
    const secondResponse = await axios.get(
      'https://bm.ruankao.org.cn/sign/welcome',
      {
        headers: {
          'User-Agent':
            'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/116.0.0.0 Safari/537.36',
          Cookie: cookieString,
        },
      },
    );

    // 使用cheerio解析第二次请求的响应
    const odata = cheerio.load(secondResponse.data);
    const content = odata('.contentShowIn').text();
    console.log(content);
    if (content.includes('福建') && content.includes('2024')) {
      // await ctx.service.tele.dd("福建软考开始报名了\nhttps://bm.ruankao.org.cn/sign/welcome");
      // await ctx.service.tele.dd2("福建软考开始报名了\nhttps://bm.ruankao.org.cn/sign/welcome");
      await ctx.service.feishu.fs(
        '福建软考开始报名了\nhttps://bm.ruankao.org.cn/sign/welcome',
      );
    }

    // 输出Cookie

    // 返回处理后的内容
    ctx.body = content.trim().split('进入');
  }

  async dp() {
    const { ctx } = this;

    await ctx.render('dplayer.html');

    // ctx.body = data.rows;
  }

  async rk() {
    const ctx = this.ctx;
    let url = 'https://query.ruankao.org.cn/score/main';
    let options = {
      method: 'GET',
      url: 'https://bm.ruankao.org.cn/query/score/main',
      headers: {
        cookie:
          'UUID=2404082259262138162469925; acw_tc=2f6a1fe817172881927201833e559152a0009f62628a6a3a70ee48d341d8ad; PHPSESSID=c9bc9leuclffc9vp5ac6ik3th6; ZhuoFanRuanKaoUserRegID=100001200810171813488497; SERVERID=c34734a4f1b8b3bfbcaa39a13bf5f7b5|1717288208|1717288192',
        Referer: 'https://www.ruankao.org.cn/',
        'Sec-Fetch-Site': 'same-site',
      },
    };

    let imgSrc = [];
    try {
      let data = await axios(options).then(function (response) {
        console.log(data);
        let odata = cheerio.load(response.data);
        odata('.select').each(function (index, item) {
          imgSrc.push(item);
        });
        return imgSrc;
      });
      ctx.body = { info: imgSrc };
    } catch (e) {
      ctx.body = { error: e };
    }
  }

  // WiFi开机卡API接口使用方法如下：
  // 客户发送请求（控制接口）：
  // 请求接口：https://songguoyun.topwd.top/Esp_Api_new.php
  // 请求方式：post
  // 请求json：需将整个json使用urlencode编码后发送
  // sgdz_account：松果电子账号（可在管理设备=》WiFi开机卡=》我的中设置）
  // sgdz_password：松果电子密码
  // device_name：设备名称，与您在设备列表中看到的名称相同
  // value：见下表。
  // 0 关机，返回0成功，-1失败
  // 1 开机，返回0成功，-1失败
  // 2 强制重启，返回0成功，-1失败
  // 11 查询电脑开机状态，1电脑开机，0电脑关机
  // 14 强制关机，返回0成功，-1失败
  // 服务器返回消息：{"status":"x"}
  // 此外：返回值（status）：2不在线；-2账号或密码错误；-3设备不存在。
  async controlpc() {
    const ctx = this.ctx;
    let code = ctx.query.c;
    let options = {
      method: 'POST',
      url: 'https://songguoyun.topwd.top/Esp_Api_new.php',
      headers: { 'Content-Type': 'application/json' },
      data: {
        sgdz_account: 'xctcc',
        sgdz_password: '960423wc',
        device_name: '电脑',
        value: code,
      },
    };

    let res = await axios
      .request(options)
      .then(function (response) {
        return response.data;
      })
      .catch(function (error) {
        return error;
      });
    ctx.body = res;
  }

  // 客户发送请求（获取设备列表接口）：
  // 请求接口：https://songguoyun.topwd.top/Esp_Api_advance.php
  // 请求方式：post
  // 请求json：需将整个json使用urlencode编码后发送
  // {
  // "sgdz_account": "xxxxx",
  // "sgdz_password": "xxxxx",
  // "type": x
  // }
  // sgdz_account：松果电子账号（可在管理设备=》WiFi开机卡=》我的中设置）
  // sgdz_password：松果电子密码
  // type:  1获取设备列表
  // 其中：
  // deviceName:开机卡名称
  // status：0关机 1开机 2离线
  async pclist() {
    const ctx = this.ctx;
    let options = {
      method: 'POST',
      url: 'https://songguoyun.topwd.top/Esp_Api_advance.php',
      headers: { 'Content-Type': 'application/json' },
      data: { sgdz_account: 'xctcc', sgdz_password: '960423wc', type: 1 },
    };

    let res = await axios
      .request(options)
      .then(function (response) {
        return response.data;
      })
      .catch(function (error) {
        return error;
      });

    function decodeResponseData(data) {
      if (typeof data === 'object') {
        // If data is an object, recursively decode its properties
        for (let key in data) {
          data[key] = decodeResponseData(data[key]);
        }
      } else if (typeof data === 'string') {
        // If data is a string, URL decode it
        data = decodeURIComponent(data);
      }

      return data;
    }

    res = decodeResponseData(res);
    ctx.body = res;
  }

  async pong() {
    const ctx = this.ctx;
    let all = ctx.query.all;
    await ctx.render('pong.html', { all: all });
  }

  async lizhilist() {
    const { ctx } = this;
    let data = [];
    for (let i = 1; i <= 12; i++) {
      let url =
        'https://lizhi.shop/site/search_list?order_by=2&category_id=1&page=' +
        i;
      let res = await axios.get(url).then(function (response) {
        let odata = cheerio.load(response.data);
        odata('.lz-grid>a').each(function (index, item) {
          let imgurl = odata(item).attr('href');
          data.push('https://lizhi.shop' + imgurl);
        });
        return data;
      });
      // data.push(res)
    }
    // console.log(data)
    let l = [];
    for (let item of data) {
      // console.log(item)
      let res = await axios.get(item).then(function (response) {
        let odata = cheerio.load(response.data);
        let html = odata('body').html();
        let title = odata('title').text().trim('\t').trim('\n');
        let obj = {
          title: title,
          url: item,
        };
        if (html.match('微软')) {
          // console.log(item)
          l.push(obj);
        }
        // return data;
      });
    }
    ctx.body = l;
  }

  async lizhiwinlist() {
    const { ctx } = this;
    let c = ctx.query.c || 1;
    let data = [];
    for (let i = 1; i <= 12; i++) {
      let url =
        'https://lizhi.shop/site/search_list?order_by=1&category_id=' +
        c +
        '&page=' +
        i;
      let res = await axios.get(url).then(function (response) {
        let $ = cheerio.load(response.data);

        $('.lz-grid>a').each(function (index, item) {
          // let obj = $(this).html();
          let title = $(this).find('p').text().trim();
          let price = $(this)
            .find('.lz-font-medium.lz-text-red-600')
            .html()
            .replace('￥', '');
          let zhe = $(this)
            .find('.lz-text-12.lz-leading-none.lz-text-white')
            .html();
          let url = $(this).attr('href');
          let xurl = 'https://lizhi.shop' + url;
          let obj = {
            title: title,
            price: +price,
            zhe: zhe || '',
            url: xurl,
          };
          data.push(obj);
        });
        data.sort((a, b) => parseFloat(a.price) - parseFloat(b.price));
        return data;
      });
      // data.push(res)
    }
    // console.log(data)

    ctx.body = data;
  }

  async lizhiwin() {
    const { ctx } = this;
    let res = await axios.get('http://127.0.0.1:7001/lzlist');
    // console.log(data[0].lenght)

    // for (let item of data) {
    //     console.log(item)
    // }
    ctx.body = JSON.stringify(res);
  }

  //txt = { '北京':'BJS', '上海':'SHA', '广州':'CAN', '深圳':'SZX', '成都':'CTU', '杭州':'HGH', '武汉':'WUH', '西安':'SIA', '重庆':'CKG', '青岛':'TAO', '长沙':'CSX', '南京':'NKG', '厦门':'XMN', '昆明':'KMG', '大连':'DLC', '天津':'TSN', '郑州':'CGO', '三亚':'SYX', '济南':'TNA', '福州':'FOC', '阿勒泰':'AAT', '阿克苏':'AKU', '鞍山':'AOG', '安庆':'AQG', '安顺':'AVA', '阿拉善左旗':'AXF', '中国澳门':'MFM', '阿里':'NGQ', '阿拉善右旗':'RHT', '阿尔山':'YIE', '巴中':'BZX', '百色':'AEB', '包头':'BAV', '毕节':'BFJ', '北海':'BHY', '北京(大兴国际机场)':'BJS,PKX', '北京(首都国际机场)':'BJS,PEK', '博乐':'BPL', '保山':'BSD', '白城':'DBC', '布尔津':'KJI', '白山':'NBS', '巴彦淖尔':'RLK', '昌都':'BPX', '承德':'CDE', '常德':'CGD', '长春':'CGQ', '朝阳':'CHG', '赤峰':'CIF', '长治':'CIH', '沧源':'CWJ', '常州':'CZX', '池州':'JUH', '大同':'DAT', '达州':'DAX', '稻城':'DCY', '丹东':'DDG', '迪庆':'DIG', '大理':'DLU', '敦煌':'DNH', '东营':'DOY', '大庆':'DQA', '德令哈':'HXD', '鄂尔多斯':'DSN', '额济纳旗':'EJN', '恩施':'ENH', '二连浩特':'ERL', '阜阳':'FUG', '抚远':'FYJ', '富蕴':'FYN', '果洛':'GMQ', '格尔木':'GOQ', '广元':'GYS', '固原':'GYU', '中国高雄':'KHH', '赣州':'KOW', '贵阳':'KWE', '桂林':'KWL', '红原':'AHJ', '海口':'HAK', '河池':'HCJ', '邯郸':'HDG', '黑河':'HEK', '呼和浩特':'HET', '合肥':'HFE', '淮安':'HIA', '怀化':'HJJ', '海拉尔':'HLD', '哈密':'HMI', '衡阳':'HNY', '哈尔滨':'HRB', '和田':'HTN', '花土沟':'HTT', '中国花莲':'HUN', '霍林郭勒':'HUO', '惠州':'HUZ', '汉中':'HZG', '黄山':'TXN', '呼伦贝尔':'XRQ', '中国嘉义':'CYI', '景德镇':'JDZ', '加格达奇':'JGD', '嘉峪关':'JGN', '井冈山':'JGS', '金昌':'JIC', '九江':'JIU', '荆门':'JM1', '佳木斯':'JMU', '济宁':'JNG', '锦州':'JNZ', '建三江':'JSJ', '鸡西':'JXA', '九寨沟':'JZH', '中国金门':'KNH', '揭阳':'SWA', '库车':'KCA', '康定':'KGT', '喀什':'KHG', '凯里':'KJH', '库尔勒':'KRL', '克拉玛依':'KRY', '黎平':'HZH', '澜沧':'JMJ', '龙岩':'LCX', '临汾':'LFQ', '兰州':'LHW', '丽江':'LJG', '荔波':'LLB', '吕梁':'LLV', '临沧':'LNJ', '陇南':'LNL', '六盘水':'LPF', '拉萨':'LXA', '洛阳':'LYA', '连云港':'LYG', '临沂':'LYI', '柳州':'LZH', '泸州':'LZO', '林芝':'LZY', '芒市':'LUM', '牡丹江':'MDG', '中国马祖':'MFK', '绵阳':'MIG', '梅州':'MXZ', '中国马公':'MZG', '满洲里':'NZH', '漠河':'OHE', '南昌':'KHN', '中国南竿':'LZN', '南充':'NAO', '宁波':'NGB', '宁蒗':'NLH', '南宁':'NNG', '南阳':'NNY', '南通':'NTG', '攀枝花':'PZI', '普洱':'SYM', '琼海':'BAR', '秦皇岛':'BPE', '祁连':'HBQ', '且末':'IQM', '庆阳':'IQN', '黔江':'JIQ', '泉州':'JJN', '衢州':'JUZ', '齐齐哈尔':'NDG', '日照':'RIZ', '日喀则':'RKZ', '若羌':'RQA', '神农架':'HPG', '莎车':'QSZ', '沈阳':'SHE', '石河子':'SHF', '石家庄':'SJW', '上饶':'SQD', '三明':'SQJ', '十堰':'WDS', '邵阳':'WGN', '松原':'YSQ', '台州':'HYN', '中国台中':'RMQ', '塔城':'TCG', '腾冲':'TCZ', '铜仁':'TEN', '通辽':'TGO', '天水':'THQ', '吐鲁番':'TLQ', '通化':'TNH', '中国台南':'TNN', '中国台北':'TPE', '中国台东':'TTT', '唐山':'TVS', '太原':'TYN', '五大连池':'DTU', '乌兰浩特':'HLH', '乌兰察布':'UCB', '乌鲁木齐':'URC', '潍坊':'WEF', '威海':'WEH', '文山':'WNH', '温州':'WNZ', '乌海':'WUA', '武夷山':'WUS', '无锡':'WUX', '梧州':'WUZ', '万州':'WXN', '乌拉特中旗':'WZQ', '巫山':'WSK', '兴义':'ACX', '夏河':'GXH', '中国香港':'HKG', '西双版纳':'JHG', '新源':'NLT', '忻州':'WUT', '信阳':'XAI', '襄阳':'XFN', '西昌':'XIC', '锡林浩特':'XIL', '西宁':'XNN', '徐州':'XUZ', '延安':'ENY', '银川':'INC', '伊春':'LDS', '永州':'LLF', '榆林':'UYN', '宜宾':'YBP', '运城':'YCU', '宜春':'YIC', '宜昌':'YIH', '伊宁':'YIN', '义乌':'YIW', '营口':'YKH', '延吉':'YNJ', '烟台':'YNT', '盐城':'YNZ', '扬州':'YTY', '玉树':'YUS', '岳阳':'YYA', '张家界':'DYG', '舟山':'HSN', '扎兰屯':'NZL', '张掖':'YZY', '昭通':'ZAT', '湛江':'ZHA', '中卫':'ZHY', '张家口':'ZQZ', '珠海':'ZUH', '遵义':'ZYI' }

  async rejc() {
    const { ctx } = this;
    ctx.body = await rejc();
  }

  async fetchLowestPrice(baseUrl, params, dcity, acity, date) {
    const url = `${baseUrl}?${new URLSearchParams({
      ...params,
      dcity,
      acity,
    })}`;
    try {
      const response = await axios.get(url);
      // console.log(response.data)
      if (
        response.data &&
        response.data.data &&
        response.data.data.oneWayPrice &&
        response.data.data.oneWayPrice[0]
      ) {
        const oneWayPrice = response.data.data.oneWayPrice[0][date];
        return oneWayPrice !== null && !isNaN(oneWayPrice) ? oneWayPrice : 0;
      } else {
        return 0;
      }
    } catch (e) {
      // console.log(e)
    }

    // 检查 response 和 oneWayPrice 是否存在且不为 null
  }

  async fetchLowestPrice1(baseUrl, params, dcity, acity, date) {
    const url = `${baseUrl}?${new URLSearchParams({
      ...params,
      dcity,
      acity,
    })}`;
    const response = await axios.get(url);

    // 检查 response 和 oneWayPrice 是否存在且不为 null
    if (
      response.data &&
      response.data.data &&
      response.data.data.oneWayPrice &&
      response.data.data.oneWayPrice[0]
    ) {
      const oneWayPrice = response.data.data.oneWayPrice[0][date];

      // 使用条件运算符进行判断，如果 oneWayPrice 存在且是一个数字，返回 oneWayPrice，否则返回 0
      return oneWayPrice !== null && !isNaN(oneWayPrice) ? oneWayPrice : 0;
    } else {
      // 如果任何一部分数据不存在，返回 0
      return 0;
    }
  }

  async pushAndUpdateRedis(
    chufa,
    huilai,
    redisKey,
    newValue,
    service,
    app,
    date,
  ) {
    const c = chufa;
    const h = huilai;
    chufa = await rejc(chufa); // Await the result of rejc
    huilai = await rejc(huilai); // Await the result of rejc
    date = await dayformat(date);
    const text = `${chufa} ${huilai} ：${newValue}\nhttps://flights.ctrip.com/online/list/oneway-${c}-${h}?depdate=${date}`;
    if (newValue !== 0 && newValue) {
      await service.feishu.fs(text);
      await app.redis.set(redisKey, newValue);
    }
  }

  async xcchufa() {
    const { ctx, app } = this;
    const baseUrl = 'https://flights.ctrip.com/itinerary/api/12808/lowestPrice';
    const params = {
      flightWay: 'Oneway',
      direct: false,
      army: false,
    };

    const cityDestinations = [{ dcity: 'XMN', acity: 'CTU', redisKey: 'xmcd' }];

    const promises = cityDestinations.map(
      async ({ dcity, acity, redisKey }) => {
        const date = '20241002';
        const newValue = await this.fetchLowestPrice(
          baseUrl,
          params,
          dcity,
          acity,
          date,
        );
        const oldValue = (await app.redis.get(redisKey)) / 1;
        if (newValue !== oldValue) {
          await this.pushAndUpdateRedis(
            dcity,
            acity,
            redisKey,
            newValue,
            this.service,
            app,
            date,
          );
        }
        dcity = await rejc(dcity); // Await the result of rejc
        acity = await rejc(acity); // Await the result of rejc
        return [`${dcity} ${acity} 出发`, newValue];
      },
    );

    const results = await Promise.all(promises);
    results.sort((a, b) => a[1] - b[1]);
    ctx.body = Object.fromEntries(results);
  }

  async xchuilai() {
    const { ctx, app } = this;
    const baseUrl = 'https://flights.ctrip.com/itinerary/api/12808/lowestPrice';
    const params = {
      flightWay: 'Oneway',
      direct: true,
      army: false,
    };

    const cityDestinations = [
      { dcity: 'LHW', acity: 'XMN', redisKey: 'lzxm' },
      { dcity: 'LHW', acity: 'HFE', redisKey: 'lzhf' },
      { dcity: 'HFE', acity: 'XMN', redisKey: 'hfxm' },
    ];

    const promises = cityDestinations.map(
      async ({ dcity, acity, redisKey }) => {
        const date = '20231006';
        const newValue = await this.fetchLowestPrice1(
          baseUrl,
          params,
          dcity,
          acity,
          date,
        );
        const oldValue = (await app.redis.get(redisKey)) / 1;
        if (newValue !== oldValue) {
          await this.pushAndUpdateRedis(
            dcity,
            acity,
            redisKey,
            newValue,
            this.service,
            app,
            date,
          );
        }
        dcity = await rejc(dcity); // Await the result of rejc
        acity = await rejc(acity); // Await the result of rejc
        return [`${dcity} ${acity} 出发`, newValue];
      },
    );

    const results = await Promise.all(promises);
    results.sort((a, b) => a[1] - b[1]);
    ctx.body = Object.fromEntries(results);
  }

  async xcallhuilai() {
    const { ctx, app } = this;
    const baseUrl = 'https://flights.ctrip.com/itinerary/api/12808/lowestPrice';
    const params = {
      flightWay: 'Oneway',
      direct: true,
      army: false,
    };

    const cityDestinations = [];
    let air = await allair();
    for (let item of air) {
      cityDestinations.push({
        dcity: 'SWA',
        acity: item,
        redisKey: 'swa' + item,
      });
    }

    const promises = cityDestinations.map(
      async ({ dcity, acity, redisKey }) => {
        const newValue = await this.fetchLowestPrice1(
          baseUrl,
          params,
          dcity,
          acity,
          '20241002',
        );
        const oldValue = (await app.redis.get(redisKey)) / 1;

        // 如果 newValue 为 0，返回 null
        if (newValue === 0) {
          return null;
        }

        // if (newValue !== oldValue) {
        //     await this.pushAndUpdateRedis(dcity, acity, redisKey, newValue, this.service, app);
        // }
        dcity = await rejc(dcity); // Await the result of rejc
        acity = await rejc(acity); // Await the result of rejc
        return { key: `${dcity} ${acity} 出发`, value: newValue };
      },
    );

    const results = await Promise.all(promises);

    // 过滤掉结果中的 null 值
    const filteredResults = results.filter((result) => result !== null);

    // 按照 newValue 从小到大排序
    filteredResults.sort((a, b) => a.value - b.value);

    // 转换排序后的数组为对象
    ctx.body = filteredResults.reduce((acc, curr) => {
      acc[curr.key] = curr.value;
      return acc;
    }, {});
  }

  async xcallchufa(o) {
    const { ctx, app } = this;
    const baseUrl = 'https://flights.ctrip.com/itinerary/api/12808/lowestPrice';
    const params = {
      flightWay: 'Oneway',
      direct: true,
      army: false,
    };

    const cityDestinations = [];
    let air = await allair();
    for (let item of air) {
      cityDestinations.push({
        dcity: item,
        acity: 'CTU',
        redisKey: item + 'CD',
      });
    }

    const promises = cityDestinations.map(
      async ({ dcity, acity, redisKey }) => {
        const newValue = await this.fetchLowestPrice(
          baseUrl,
          params,
          dcity,
          acity,
          '20241002',
        );
        const oldValue = (await app.redis.get(redisKey)) / 1;

        // 如果 newValue 为 0，返回 null
        if (newValue === 0) {
          return null;
        }

        // if (newValue !== oldValue) {
        //     await this.pushAndUpdateRedis(dcity, acity, redisKey, newValue, this.service, app);
        // }
        dcity = await rejc(dcity); // Await the result of rejc
        acity = await rejc(acity); // Await the result of rejc
        return { key: `${dcity} ${acity} 出发`, value: newValue };
      },
    );

    const results = await Promise.all(promises);

    // 过滤掉结果中的 null 值
    const filteredResults = results.filter((result) => result !== null);

    // 按照 newValue 从小到大排序
    filteredResults.sort((a, b) => a.value - b.value);

    // 转换排序后的数组为对象
    ctx.body = filteredResults.reduce((acc, curr) => {
      acc[curr.key] = curr.value;
      return acc;
    }, {});
  }

  async xcallCommon() {
    const { ctx, app } = this;
    const baseUrl = 'https://flights.ctrip.com/itinerary/api/12808/lowestPrice';
    const params = {
      flightWay: 'Oneway',
      direct: true,
      army: false,
    };

    let dcity = 'XMN'; // 出发城市
    let acity = 'CKG'; // 最终目的地
    let date = '20240930'; // 查询日期
    const cityDestinations = [];

    let air = await allair(); // 假设这是获取所有城市代码的函数

    // 遍历所有中转城市
    for (let middleCity of air) {
      cityDestinations.push({
        dcity, // 出发城市
        middleCity, // 中转城市
        acity, // 目的地城市
        redisKey1: `xccf${middleCity}`, // Redis key for 出发->中转
        redisKey2: `${middleCity}xchl`, // Redis key for 中转->目的地
      });
    }

    // 执行价格查询
    const promises = cityDestinations.map(
      async ({ dcity, middleCity, acity, redisKey1, redisKey2 }) => {
        // 查询出发城市到中转城市的价格
        const price1 = await this.fetchLowestPrice(
          baseUrl,
          params,
          dcity,
          middleCity,
          date,
        );

        // 查询中转城市到目的地城市的价格
        const price2 = await this.fetchLowestPrice(
          baseUrl,
          params,
          middleCity,
          acity,
          date,
        );

        // 如果任一段价格为 0，则返回 null
        if (price1 === 0 || price2 === 0) {
          return null;
        }

        // 总价格为两段的价格相加
        const totalPrice = price1 + price2;

        // 格式化城市名称
        const dcityName = await rejc(dcity);
        const middleCityName = await rejc(middleCity);
        const acityName = await rejc(acity);

        // 返回格式为 "厦门 长沙 成都": totalPrice
        return {
          key: `${dcityName} ${middleCityName} ${acityName}`,
          value: totalPrice,
        };
      },
    );

    const results = await Promise.all(promises);

    // 过滤掉结果中的 null 值
    const filteredResults = results.filter((result) => result !== null);

    // 按照价格从小到大排序
    filteredResults.sort((a, b) => a.value - b.value);

    // 转换排序后的数组为对象并返回
    ctx.body = filteredResults.reduce((acc, curr) => {
      acc[curr.key] = curr.value;
      return acc;
    }, {});
  }

  async xcallxm() {
    const { ctx, app } = this;
    const baseUrl = 'https://flights.ctrip.com/itinerary/api/12808/lowestPrice';
    const params = {
      flightWay: 'Oneway',
      direct: true,
      army: false,
    };

    const cityDestinations = [];
    let air = await allair();
    for (let item of air) {
      cityDestinations.push({
        dcity: 'XMN',
        acity: item,
        redisKey: 'xmzz' + item,
      });
    }

    const promises = cityDestinations.map(
      async ({ dcity, acity, redisKey }) => {
        const newValue = await this.fetchLowestPrice(
          baseUrl,
          params,
          dcity,
          acity,
          '20230930',
        );
        const oldValue = (await app.redis.get(redisKey)) / 1;

        // 如果 newValue 为 0，返回 null
        if (newValue === 0) {
          return null;
        }
        dcity = await rejc(dcity); // Await the result of rejc
        acity = await rejc(acity); // Await the result of rejc
        return { key: `${acity}`, value: newValue };
      },
    );

    const results = await Promise.all(promises);

    // 过滤掉结果中的 null 值
    const filteredResults = results.filter((result) => result !== null);

    // 按照 newValue 从小到大排序
    filteredResults.sort((a, b) => a.value - b.value);

    // 转换排序后的数组为对象
    const sortedObject = filteredResults.reduce((acc, curr) => {
      acc[curr.key] = curr.value;
      return acc;
    }, {});

    let arr = [];
    const cflz = await axios
      .get('http://127.0.0.1:7001/xccfall')
      .then((res) => {
        for (let item in res.data) {
          for (let i in sortedObject) {
            if (item.match(i) && i !== '兰州') {
              let price = sortedObject[i] / 1;
              let price1 = res.data[item] / 1 + price;
              // console.log("厦门 " + item + price1)
              let x = { key: '厦门 ' + item, price: price1 };
              arr.push(x);
            }
          }
        }
        arr.sort((a, b) => a.price - b.price);
        ctx.body = arr; // 在这里设置 ctx.body
      });
  }

  async xcalllz() {
    const { ctx, app } = this;
    const baseUrl = 'https://flights.ctrip.com/itinerary/api/12808/lowestPrice';
    const params = {
      flightWay: 'Oneway',
      direct: true,
      army: false,
    };

    const cityDestinations = [];
    let air = await allair();
    for (let item of air) {
      cityDestinations.push({
        dcity: item,
        acity: 'XMN',
        redisKey: 'lzzz' + item,
      });
    }

    const promises = cityDestinations.map(
      async ({ dcity, acity, redisKey }) => {
        const newValue = await this.fetchLowestPrice1(
          baseUrl,
          params,
          dcity,
          acity,
          '20231006',
        );
        const oldValue = (await app.redis.get(redisKey)) / 1;

        // 如果 newValue 为 0，返回 null
        if (newValue === 0) {
          return null;
        }
        dcity = await rejc(dcity); // Await the result of rejc
        acity = await rejc(acity); // Await the result of rejc
        return { key: `${dcity}`, value: newValue };
      },
    );

    const results = await Promise.all(promises);

    // 过滤掉结果中的 null 值
    const filteredResults = results.filter((result) => result !== null);

    // 按照 newValue 从小到大排序
    filteredResults.sort((a, b) => a.value - b.value);

    // 转换排序后的数组为对象
    const sortedObject = filteredResults.reduce((acc, curr) => {
      acc[curr.key] = curr.value;
      return acc;
    }, {});

    // console.log(sortedObject)
    let arr = [];
    const cflz = await axios
      .get('http://127.0.0.1:7001/xchlall')
      .then((res) => {
        for (let item in res.data) {
          for (let i in sortedObject) {
            if (item.match(i) && i !== '兰州') {
              // console.log(item + i + sortedObject[i] / 1 + res.data[item] / 1)

              let price = sortedObject[i] / 1;
              let price1 = res.data[item] / 1 + price;
              let x = { key: item + '厦门', price: price1 };
              arr.push(x);
            }
          }
        }
        arr.sort((a, b) => a.price - b.price);
        ctx.body = arr; // 在这里设置 ctx.body
      });
  }

  async vue() {
    const ctx = this.ctx;
    let all = ctx.query.all;
    await ctx.render('bb.html', { all: all });
  }

  async xmair() {
    const { ctx, app } = this;
    try {
      const response = await axios.post(
        'https://csapi.xiamenair.com/offer/api/v1/offer/shopping',
        {
          shoppingPreference: {
            connectionPreference: { maxConnectionQuantity: -1 },
            flightPreference: {
              cabinCombineMode: 'Cabin',
              lowestFare: true,
              carrierPreferences: [{ carrierCode: 'TKT_SHOPPING_CARRIERS_DS' }],
            },
            accountCodeSlogan: '',
            pricingMode: 'Money',
          },
          cabinClasses: ['Economy', 'Business', 'First'],
          passengerCount: { adult: 1, child: 1, infant: 1 },
          itineraries: [
            {
              departureDate: '2023-10-06',
              origin: { airport: { code: 'HFE' } },
              destination: { airport: { code: 'XMN' } },
              segments: [],
            },
          ],
        },
        {
          headers: {
            'Content-Type': 'application/json',
            'User-Agent':
              'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/115.0.0.0 Safari/537.36',
            Channel: 'PCWEB',
          },
        },
      );

      const flightInfo = response.data.result.odInfos[0].flightInfos[0];
      const adultPrice = flightInfo.paxCabins.ADT1[0].totalAmount / 1 || 0;
      const adultBusinessPrice =
        flightInfo.paxCabins.ADT_BRAND[0].totalAmount / 1 || 0;

      const newAdultPrice = (await app.redis.get('hfxmadt')) / 1;
      const newAdultBusinessPrice = (await app.redis.get('hfxmadtb')) / 1;
      if (
        newAdultPrice !== adultPrice ||
        newAdultBusinessPrice !== adultBusinessPrice
      ) {
        const text = `合肥到厦门15点出发最低价格：${adultPrice}和${adultBusinessPrice}`;
        await ctx.service.feishu.fs(text);
        await app.redis.set('hfxmadt', adultPrice);
        await app.redis.set('hfxmadtb', adultBusinessPrice);
      }
      ctx.body = {
        text: `合肥到厦门15点出发最低价格：${adultPrice}和${adultBusinessPrice}`,
      };
      // ctx.body = flightInfo.paxCabins;
    } catch (error) {
      ctx.body = error;
      // 处理错误，可能发送错误消息或采取其他适当的措施
    }
  }

  async xmairlz() {
    const { ctx, app } = this;
    try {
      const response = await axios.post(
        'https://csapi.xiamenair.com/offer/api/v1/offer/shopping',
        {
          shoppingPreference: {
            connectionPreference: { maxConnectionQuantity: -1 },
            flightPreference: {
              cabinCombineMode: 'Cabin',
              lowestFare: true,
              carrierPreferences: [{ carrierCode: 'TKT_SHOPPING_CARRIERS_DS' }],
            },
            accountCodeSlogan: '',
            pricingMode: 'Money',
          },
          cabinClasses: ['Economy', 'Business', 'First'],
          passengerCount: { adult: 1, child: 1, infant: 1 },
          itineraries: [
            {
              departureDate: '2023-10-06',
              origin: { airport: { code: 'LHW' } },
              destination: { airport: { code: 'XMN' } },
              segments: [],
            },
          ],
        },
        {
          headers: {
            'Content-Type': 'application/json',
            'User-Agent':
              'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/115.0.0.0 Safari/537.36',
            Channel: 'PCWEB',
          },
        },
      );

      const flightInfo = response.data.result.odInfos[0].flightInfos[0];
      const flightInfo1 = response.data.result.odInfos[0].flightInfos[1];

      const adultPrice = flightInfo.paxCabins.ADT1[0].totalAmount / 1 || 0;
      const adultBusinessPrice =
        flightInfo.paxCabins.ADT_BRAND[0].totalAmount / 1 || 0;
      const adultPrice1 = flightInfo1.paxCabins.ADT1[0].totalAmount / 1 || 0;

      const newAdultPrice = (await app.redis.get('lzxmadt')) / 1;
      const newAdultBusinessPrice = (await app.redis.get('lzxmadtb')) / 1;
      const newAdultPrice1 = (await app.redis.get('lzxmadt1')) / 1;

      if (
        newAdultPrice !== adultPrice ||
        newAdultBusinessPrice !== adultBusinessPrice ||
        newAdultPrice1 !== adultPrice1
      ) {
        const text =
          `兰州到厦门13点15出发最低价格：${adultPrice}和${adultBusinessPrice}` +
          '\n' +
          `14点05出发最低价格${adultPrice1}`;
        await ctx.service.feishu.fs(text);
        await app.redis.set('lzxmadt', adultPrice);
        await app.redis.set('lzxmadtb', adultBusinessPrice);
        await app.redis.set('lzxmadt1', adultPrice1);
      }
      ctx.body = {
        text:
          `兰州到厦门13点15出发最低价格：${adultPrice}和${adultBusinessPrice}` +
          '\n' +
          `14点05出发最低价格${adultPrice1}`,
      };
      // ctx.body = response.data.result.odInfos[0];
    } catch (error) {
      ctx.body = error;
      // 处理错误，可能发送错误消息或采取其他适当的措施
    }
  }

  async xmairxm() {
    const { ctx, app } = this;
    try {
      const response = await axios.post(
        'https://csapi.xiamenair.com/offer/api/v1/offer/shopping',
        {
          shoppingPreference: {
            connectionPreference: { maxConnectionQuantity: -1 },
            flightPreference: {
              cabinCombineMode: 'Cabin',
              lowestFare: true,
              carrierPreferences: [{ carrierCode: 'TKT_SHOPPING_CARRIERS_DS' }],
            },
            accountCodeSlogan: '',
            pricingMode: 'Money',
          },
          cabinClasses: ['Economy', 'Business', 'First'],
          passengerCount: { adult: 1, child: 1, infant: 1 },
          itineraries: [
            {
              departureDate: '2023-09-30',
              origin: { airport: { code: 'XMN' } },
              destination: { airport: { code: 'LHW' } },
              segments: [],
            },
          ],
        },
        {
          headers: {
            'Content-Type': 'application/json',
            'User-Agent':
              'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/115.0.0.0 Safari/537.36',
            Channel: 'PCWEB',
          },
        },
      );
      // ctx.body = response.data.result.odInfos[0].flightInfos[1]
      // return;
      const flightInfo1 = response.data.result.odInfos[0].flightInfos[1];

      const adultBusinessPrice =
        flightInfo1.paxCabins.ADT_BRAND[0].totalAmount / 1 || 0;
      const adultPrice1 = flightInfo1.paxCabins.ADT1[0].totalAmount / 1 || 0;

      // const newAdultPrice = await app.redis.get("lzxmadt") / 1;
      const newAdultBusinessPrice = (await app.redis.get('xmlzadtb')) / 1;
      const newAdultPrice1 = (await app.redis.get('xmlzadt1')) / 1;

      if (
        newAdultBusinessPrice !== adultBusinessPrice ||
        newAdultPrice1 !== adultPrice1
      ) {
        const text =
          `厦门到兰州9月30号08:35出发最低价格：${adultBusinessPrice}` +
          '\n' +
          `出发最低价格${adultPrice1}`;
        await ctx.service.feishu.fs(text);
        await app.redis.set('xmlzadtb', adultBusinessPrice);
        await app.redis.set('xmlzadt1', adultPrice1);
      }
      ctx.body = {
        text:
          `厦门到兰州9月30号08:35出发最低价格：${adultBusinessPrice}` +
          '\n' +
          `出发最低价格${adultPrice1}`,
      };
      // ctx.body = response.data.result.odInfos[0];
    } catch (error) {
      ctx.body = error;
      // 处理错误，可能发送错误消息或采取其他适当的措施
    }
  }

  async sda() {
    const { ctx, app } = this;
    try {
      const response = await ctx.service.air.sda(
        'HFE',
        'XMN',
        '2023-10-06',
        '2023-10-06',
      );
      const newPrice = response.flightCachePrices[0].price / 1;
      const price = (await app.redis.get('sdahfxm')) / 1;
      if (price !== newPrice) {
        const text = `山东航空合肥到厦门14点45出发最低价格：${newPrice}`;
        await ctx.service.feishu.fs(text);
        await app.redis.set('sdahfxm', newPrice);
      }
      ctx.body = response;
    } catch (error) {
      ctx.body = error;
    }
  }

  async sdaxmlz() {
    const { ctx, app } = this;
    try {
      const response = await ctx.service.air.sda(
        'XMN',
        'LHW',
        '2023-09-30',
        '2023-09-30',
      );
      const newPrice = response.flightCachePrices[0].price / 1;
      const price = (await app.redis.get('sdaxmlz')) / 1;
      if (price !== newPrice) {
        const text = `山东航空厦门到兰州9月30号12点出发最低价格：${newPrice}`;
        await ctx.service.feishu.fs(text);
        await app.redis.set('sdaxmlz', newPrice);
      }
      ctx.body = response;
    } catch (error) {
      ctx.body = error;
    }
  }

  async sdalzxm() {
    const { ctx, app } = this;
    try {
      const data = await ctx.service.air.sda(
        'LHW',
        'XMN',
        dayNow(),
        '2023-10-06',
      );
      let list = [];
      let text = '';
      text = text + `兰州飞厦门山东航空14点45\n`;
      for (let i in data.flightCachePrices) {
        let obj = {};
        obj.date = data.flightCachePrices[i].date;
        obj.price = data.flightCachePrices[i].price;
        const oldprice = (await app.redis.get('sdalzxm' + obj.date)) / 1;
        list.push(obj);
        text = text + `${obj.date} ${obj.price}\n`;
        if (oldprice !== obj.price) {
          text = text + `价格改动，旧价格：${oldprice}，新价格：${obj.price}\n`;
        }
        await app.redis.set('sdalzxm' + obj.date, obj.price / 1);
      }
      if (text.match('价格改动')) {
        await ctx.service.feishu.fs(text);
      }
      ctx.body = list;
    } catch (error) {
      ctx.body = error;
    }
  }

  async sdaxmch() {
    const { ctx, app } = this;
    try {
      const data = await ctx.service.air.sda(
        'XMN',
        'LHW',
        dayNow(),
        '2023-09-30',
      );
      let list = [];
      let text = '';
      text = text + `厦门飞兰州山东航空12点\n`;
      for (let i in data.flightCachePrices) {
        let obj = {};
        obj.date = data.flightCachePrices[i].date;
        obj.price = data.flightCachePrices[i].price;
        const oldprice = (await app.redis.get('sdaxmlz' + obj.date)) / 1;
        list.push(obj);
        text = text + `${obj.date} ${obj.price}\n`;
        if (oldprice !== obj.price) {
          text = text + `价格改动，旧价格：${oldprice}，新价格：${obj.price}\n`;
        }
        await app.redis.set('sdaxmlz' + obj.date, obj.price / 1);
      }
      if (text.match('价格改动')) {
        await ctx.service.feishu.fs(text);
      }
      ctx.body = list;
    } catch (error) {
      ctx.body = error;
    }
  }

  async sdahfhl() {
    const { ctx, app } = this;
    try {
      const data = await ctx.service.air.sda(
        'HFE',
        'XMN',
        '2023-10-06',
        '2023-10-06',
      );
      let list = [];
      let text = '';
      text = text + `合肥飞厦门山东航空14点45\n`;
      for (let i in data.flightCachePrices) {
        let obj = {};
        obj.date = data.flightCachePrices[i].date;
        obj.price = data.flightCachePrices[i].price;
        const oldprice = (await app.redis.get('sdahfxm' + obj.date)) / 1;
        list.push(obj);
        text = text + `${obj.date} ${obj.price}\n`;
        if (oldprice !== obj.price) {
          text = text + `价格改动，旧价格：${oldprice}，新价格：${obj.price}\n`;
        }
        await app.redis.set('sdahfxm' + obj.date, obj.price / 1);
      }
      if (text.match('价格改动')) {
        await ctx.service.feishu.fs(text);
      }
      ctx.body = list;
    } catch (error) {
      ctx.body = error;
    }
  }

  async pianyuan() {
    const { ctx } = this;

    // Step 1: 发送第一次请求，获取Cookie
    const secondResponse = await axios.get(
      'https://re.101616.xyz/https://pianyuan.org/User/register.html',
      {
        headers: {
          'User-Agent':
            'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/118.0.0.0 Safari/537.36',
        },
      },
    );

    const odata = cheerio.load(secondResponse.data);
    const content = odata('.error').text();
    if (!content.includes('注册已关闭')) {
      await ctx.service.feishu.fs('片源网' + content);
      // await ctx.service.dd.dd('片源网开放注册');
      await ctx.service.dd.dd2('片源网' + content);
    }

    ctx.body = content;
  }

  async speedtestcn() {
    const { ctx } = this;

    let s = ctx.query.s || 'cn';
    const userAgent =
      'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/118.0.0.0 Safari/537.36';

    const fetchAndTransformData = async (url, yys) => {
      const response = await axios.get(url, {
        headers: { 'User-Agent': userAgent },
      });

      return response.data.map((item) => ({ ...item, yys }));
    };

    let list = [];
    let text = '';
    if (s === 'cn') {
      const cuURL =
        'https://www.speedtest.net/api/js/servers?engine=js&search=china+unicom&https_functional=true&limit=100';
      const cmURL =
        'https://www.speedtest.net/api/js/servers?engine=js&search=china+mobile&https_functional=true&limit=100';
      const ctURL =
        'https://www.speedtest.net/api/js/servers?engine=js&search=china+telecom&https_functional=true&limit=100';
      const ltURL =
        'https://www.speedtest.net/api/js/servers?engine=js&search=联通&https_functional=true&limit=100';
      const unicomURL =
        'https://www.speedtest.net/api/js/servers?engine=js&search=unicom&https_functional=true&limit=100';
      const ydURL =
        'https://www.speedtest.net/api/js/servers?engine=js&search=移动&https_functional=true&limit=100';
      const dxURL =
        'https://www.speedtest.net/api/js/servers?engine=js&search=电信&https_functional=true&limit=100';
      const txURL =
        'https://www.speedtest.net/api/js/servers?engine=js&search=通信&https_functional=true&limit=100';
      const cuData = await fetchAndTransformData(cuURL, '联通');
      const cmData = await fetchAndTransformData(cmURL, '移动');
      const ctData = await fetchAndTransformData(ctURL, '电信');
      const unicomData = await fetchAndTransformData(unicomURL, '联通');
      const ltData = await fetchAndTransformData(ltURL, '联通');
      const ydData = await fetchAndTransformData(ydURL, '移动');
      const dxData = await fetchAndTransformData(dxURL, '电信');
      const txData = await fetchAndTransformData(txURL, '通信');

      list = [
        ...unicomData,
        ...cuData,
        ...cmData,
        ...ctData,
        ...ltData,
        ...ydData,
        ...dxData,
        ...txData,
      ];
      const maxLength = Math.max(...list.map((item) => item.name.length));

      text = list
        //过滤重复的item.id
        .filter(
          (item, index, self) =>
            self.findIndex((t) => t.id === item.id) === index,
        )
        .filter((item) => item.cc === 'CN')
        .map(
          (item) =>
            `\t\tspeed_test '${item.id}' '${item.name
              .replace(`'`, ``)
              .padEnd(maxLength)}' '${item.yys}'\n`,
        )
        .join('');
    } else {
      const url =
        'https://www.speedtest.net/api/js/servers?engine=js&search=' +
        s +
        '&https_functional=true&limit=20';
      list = await fetchAndTransformData(url, '全球');
      const maxLength = Math.max(...list.map((item) => item.name.length));
      const sponsormaxLength = Math.max(
        ...list.map((item) => item.sponsor.length),
      );

      text = list
        .map(
          (item) =>
            `\t\tspeed_test '${item.id}' '${item.name.padEnd(
              maxLength,
            )}' '${item.sponsor.padEnd(sponsormaxLength)}'\n`,
        )
        .join('');
    }

    const text1 =
      '#!/usr/bin/env bash\n' +
      '\n' +
      '# Colors\n' +
      "RED='\\033[0;31m'\n" +
      "GREEN='\\033[0;32m'\n" +
      "YELLOW='\\033[0;33m'\n" +
      "BLUE='\\033[0;34m'\n" +
      'PURPLE="\\033[0;35m"\n' +
      "CYAN='\\033[0;36m'\n" +
      "PLAIN='\\033[0m'\n" +
      '\n' +
      'checkspeedtest() {\n' +
      "\tif  [ ! -e './speedtest-cli/speedtest' ]; then\n" +
      '\t\techo "正在安装 Speedtest-cli"\n' +
      '                arch=$(uname -m)\n' +
      '                if [ "${arch}" == "aarch64" ]; then\n' +
      '                    arch="aarch64"\n' +
      '                fi\n' +
      '\t\twget --no-check-certificate -qO speedtest.tgz https://cdn.jsdelivr.net/gh/oooldking/script@1.1.7/speedtest_cli/ookla-speedtest-1.0.0-${arch}-linux.tgz > /dev/null 2>&1\n' +
      '\t\t# wget --no-check-certificate -qO speedtest.tgz https://bintray.com/ookla/download/download_file?file_path=ookla-speedtest-1.0.0-${arch}-linux.tgz > /dev/null 2>&1\n' +
      '\tfi\n' +
      '\tmkdir -p speedtest-cli && tar zxvf speedtest.tgz -C ./speedtest-cli/ > /dev/null 2>&1 && chmod a+rx ./speedtest-cli/speedtest\n' +
      '}\n' +
      '\n' +
      'speed_test(){\n' +
      '\tspeedLog="./speedtest.log"\n' +
      '\ttrue > $speedLog\n' +
      '\t\tspeedtest-cli/speedtest -p no -s $1 --accept-license > $speedLog 2>&1\n' +
      "\t\tis_upload=$(cat $speedLog | grep 'Upload')\n" +
      '\t\tif [[ ${is_upload} ]]; then\n' +
      "\t        local REDownload=$(cat $speedLog | awk -F ' ' '/Download/{print $2}')$(cat $speedLog | awk -F ' ' '/Download/{print $3}')\n" +
      "\t        local reupload=$(cat $speedLog | awk -F ' ' '/Upload/{print $2}')$(cat $speedLog | awk -F ' ' '/Upload/{print $3}')\n" +
      "\t        local relatency=$(cat $speedLog | awk -F ' ' '/Latency/{print $2}')\n" +
      '\t        \n' +
      '\t\t\tlocal nodeID=$1\n' +
      '\t\t\tlocal nodeLocation=$2\n' +
      '\t\t\tlocal nodeISP=$3\n' +
      '\t\t\t\n' +
      '\t\t\tstrnodeLocation="${nodeLocation}　　　　　　"\n' +
      '\t\t\tLANG=C\n' +
      '\t\t\t#echo $LANG\n' +
      '\t\t\t\n' +
      "\t\t\ttemp=$(echo \"${REDownload}\" | awk -F ' ' '{print $1}')\n" +
      '\t        if [[ $(awk -v num1=${temp} -v num2=0 \'BEGIN{print(num1>num2)?"1":"0"}\') -eq 1 ]]; then\n' +
      '\t        \tprintf "${RED}%-6s${YELLOW}%s%s${GREEN}%-24s${CYAN}%s%-10s${BLUE}%s%-10s${PURPLE}%-8s${PLAIN}\\n" "${nodeID}"  "${nodeISP}" "|" "${strnodeLocation:0:24}" "↑ " "${reupload}" "↓ " "${REDownload}" "${relatency}" | tee -a $log\n' +
      '\t\t\tfi\n' +
      '\t\telse\n' +
      '\t        local cerror="ERROR"\n' +
      '\t\tfi\n' +
      '}\n' +
      '\n' +
      '\n' +
      '\n' +
      'runtest() {\n' +
      '\n' +
      '\t\techo "——————————————————————————————————————————————————————————"\n' +
      '\t\techo "ID    测速服务器信息                上传/Mbps   下载/Mbps   延迟/ms"\n' +
      '\t\tstart=$(date +%s) ' +
      `\n`;
    const text2 =
      `\n` +
      '\n' +
      '\t\t\n' +
      '\t\tend=$(date +%s)  \n' +
      '\t\trm -rf speedtest*\n' +
      '\t\techo "——————————————————————————————————————————————————————————"\n' +
      '\t\ttime=$(( $end - $start ))\n' +
      '\t\tif [[ $time -gt 60 ]]; then\n' +
      '\t\t\tmin=$(expr $time / 60)\n' +
      '\t\t\tsec=$(expr $time % 60)\n' +
      '\t\t\techo -ne "  测试完成, 本次测速耗时: ${min} 分 ${sec} 秒"\n' +
      '\t\telse\n' +
      '\t\t\techo -ne "  测试完成, 本次测速耗时: ${time} 秒"\n' +
      '\t\tfi\n' +
      '\t\techo -ne "\\n  当前时间: "\n' +
      '\t\techo $(TZ=UTC-8 date +%Y-%m-%d" "%H:%M:%S)\n' +
      '\n' +
      '}\n' +
      '\n' +
      'runall() {\n' +
      '\tclear;\n' +
      '\tcheckspeedtest;\n' +
      '\tspeed_test;\n' +
      '\truntest;\n' +
      '\trm -rf speedtest*\n' +
      '}\n' +
      '\n' +
      'runall';
    ctx.body = text1 + text + text2;
  }

  async apsgo() {
    const { ctx, app } = this;

    try {
      const res = await axios.get(
        'https://re.101616.xyz/https://apsgo.com/api/listAuctions',
        {
          headers: {
            'User-Agent':
              'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/118.0.0.0 Safari/537.36',
          },
        },
      );
      const forbiddenIds = [760, 1254, 46, 1337, 222];
      // 提取需要的信息并过滤
      const list = Object.values(res.data.data.ordinary).concat(
        Object.values(res.data.data.recommend),
      );
      const nowdata = list
        .filter(
          (item) =>
            !item.product.name.includes('Ashampoo') &&
            !item.product.name.includes('IObit') &&
            !item.product.name.includes('iObit') &&
            !forbiddenIds.includes(item.id),
        )
        .map((item) => ({ id: item.id, title: item.product.name }));
      // console.log(list);
      // 获取之前保存的数据
      const oldtext = (await app.redis.get('apsgo')) || '';

      // 对比旧数据和新数据的差异
      const nowtext = nowdata
        .sort((a, b) => a.id - b.id)
        .map((item) => item.id)
        .join(',');

      // console.log('now:', nowtext);
      // console.log('now:', nowdata);

      const diffIds = nowdata
        .filter((item) => !oldtext.includes(item.id))
        .map((item) => ({ id: item.id, title: item.title }));
      // console.log('Difference:', diffIds);
      // console.log(nowdata);
      for (let item in diffIds) {
        log(diffIds[item]);
        const text =
          `出现新的拍卖商品：${diffIds[item].title}` +
          '\n' +
          `https://apsgo.com/auction/${diffIds[item].id}`;
        await ctx.service.feishu.fs(text);
      }
      // 更新保存的商品列表
      await app.redis.set('apsgo', nowtext);
      ctx.body = nowdata;
    } catch (error) {
      console.error('Error in apsgo:', error.message);
      // 处理错误，可以返回适当的错误信息给客户端
      ctx.status = 500;
      ctx.body = { error: error.message };
    }
  }

  async jiaoshigonggao() {
    const { ctx, app } = this;
    const url = 'https://ntce.neea.edu.cn/html1/category/1507/1148-1.htm';
    const response = await axios.get(url);
    const $ = cheerio.load(response.data);
    const list = [];
    $('.listdiv #first_data li').each(async function (index, item) {
      const title = $(this).find('a').text();
      const href = `https://ntce.neea.edu.cn` + $(this).find('a').attr('href');
      const time = $(this).find('#ReportIDIssueTime').text(); // 如果需要时间信息的话
      list.push({ title, href, time });

      if (title.match('2024年下') && title.match('笔试')) {
        let text = title + '\n' + href;
        await ctx.service.feishu.fs(text);
      }
    });
    ctx.body = { list: list };
  }

  async tomato() {
    const { ctx } = this;
    let sw = ctx.query.sw;
    await ctx.service.xr.update(
      'sw',
      {
        id: 2,
        sw: +sw,
      },
      {
        where: {
          name: 'tomato',
        },
      },
    );
    // console.log(res);
    ctx.body = await ctx.service.xr['find']('sw', { name: 'tomato' });
  }

  async saveip_rangestomysql() {
    const { ctx, app } = this;
    // const url = 'https://egg.101818.xyz/chinaip';
    const url =
      'https://raw.githubusercontent.com/gaoyifan/china-operator-ip/ip-lists/china.txt';
    try {
      const response = await axios.get(url);
      const ipRanges = response.data.split('\n').filter(Boolean);

      await ctx.service.xr.query('truncate ip_ranges');

      // Batch insert IP ranges
      const batchSize = 100; // Adjust batch size as per your database and performance requirements
      const promises = []; // Array to hold promises for batch insert operations

      for (let i = 0; i < ipRanges.length; i += batchSize) {
        const batch = ipRanges.slice(i, i + batchSize);
        const insertData = batch.map((range) => ({ range }));
        // Push the promise into the promises array
        promises.push(ctx.service.xr.create('ip_ranges', insertData));
      }

      // Wait for all batch insert operations to complete
      await Promise.all(promises);

      ctx.body = { message: 'IP ranges saved successfully to MySQL' };
    } catch (e) {
      console.error('Error occurred while saving IP ranges to MySQL:', e);
      // Handle or log the error appropriately
      ctx.body = { error: 'Error occurred while saving IP ranges to MySQL' };
    }
  }

  async chinaip() {
    const { ctx, app } = this;
    const url =
      'https://raw.githubusercontent.com/gaoyifan/china-operator-ip/ip-lists/china.txt';
    const response = await axios.get(url);
    ctx.body = response.data;
  }

  async cmccip() {
    const { ctx, app } = this;
    const url =
      'https://raw.githubusercontent.com/gaoyifan/china-operator-ip/refs/heads/ip-lists/cmcc.txt';
    const response = await axios.get(url);
    ctx.body = response.data;
  }

  async unicomip() {
    const { ctx, app } = this;
    const url =
      'https://raw.githubusercontent.com/gaoyifan/china-operator-ip/refs/heads/ip-lists/unicom.txt';
    const response = await axios.get(url);
    ctx.body = response.data;
  }

  async checkdnswebsites() {
    const { ctx, app } = this;
    const url = [
      'http://*************:6003/dnsinfo',
      'http://***************:6003/dnsinfo',
      'http://**************:6003/dnsinfo',
    ];

    async function checkIp(ip) {
      return new Promise(async (resolve, reject) => {
        try {
          let res = await ctx.service.xr.query(`SELECT *
                                                FROM ip_ranges`);
          const ipRanges = res.map((row) => row.range);
          const isInChina = ipRangeCheck(ip, ipRanges);
          resolve(isInChina);
        } catch (err) {
          return reject(err);
        }
      });
    }

    async function insertOrUpdateWebsite(entry) {
      return new Promise(async (resolve, reject) => {
        try {
          // console.log(entry);
          await ctx.service.xr.create('websites', entry);
          resolve();
        } catch (e) {
          reject(e);
        }
      });
    }

    async function simplifyUrl(url) {
      // 将 url 简化为域名部分,按小数点分成数组，取最后两个部分合并一个simpleurl
      let urlArray = url.split('.');
      return urlArray.slice(-2).join('.');
    }

    try {
      const result = [];
      for (let urlitem of url) {
        console.log(urlitem);
        const response = await axios.get(urlitem, {
          headers: {
            accept:
              'text/html,application/xhtml+xml,application/xml;q=0.9,image/avif,image/webp,image/apng,*/*;q=0.8,application/signed-exchange;v=b3;q=0.7',
            'accept-language': 'zh-CN,zh;q=0.9',
            'cache-control': 'no-cache',
            pragma: 'no-cache',
            'upgrade-insecure-requests': '1',
            cookie: 'agh_session=d4f7657fee575f492fadd22c4129113d',
          },
        });

        // console.log(response.data);
        for (let item of response.data) {
          const simpleUrl = await simplifyUrl(item.url);
          let existing = await ctx.service.xr['find']('websites', {
            simpleurl: simpleUrl,
          });
          if (existing.data) {
            //跳过
            // console.log('跳过', simpleUrl);
            continue;
          }
          const isInChina = await checkIp(item.ip);
          const queryurl = isInChina
            ? `https://ip.wcy9.com:18443/dns-query`
            : `https://hk.101818.xyz:6443/dns-query`;
          const format = `[/${simpleUrl}/]${queryurl}`;

          const entry = {
            ip: item.ip,
            url: item.url,
            simpleurl: simpleUrl,
            iscn: isInChina ? 1 : 0,
            format: format,
          };

          // 插入数据到数据库，确保url唯一
          try {
            if (!existing.data) {
              await insertOrUpdateWebsite(entry);
              result.push(entry);
            }
          } catch (error) {
            console.error('Failed to insert into database:', error.message);
          }
        }
      }
      ctx.body = result;
    } catch (e) {}
  }

  async cnwebsites() {
    const { ctx, app } = this;
    let type = ctx.query.type || 1;
    let f = ctx.query.f || false;
    let w = ctx.query.w || false;
    let s = ctx.query.s || false;
    let list = await ctx.service.xr.query(
      `SELECT *
       FROM websites
       where iscn = ${type}`,
    );
    if (f) {
      const formattxt = list.map(
        (item) =>
          `${item.format.replace(
            'https://hk.101818.xyz:6443/dns-query',
            'quic://seoul.101818.xyz:1784',
          )}`,
      );
      let diydns = [
        '127.0.0.1:5352',
        'https://1.12.12.12/dns-query',
        'https://120.53.53.53/dns-query',
        `[/*.cn/]https://120.53.53.53/dns-query`,
      ];
      diydns = diydns.concat(formattxt);

      if (w) {
        let filePath = '/my/own/confdir/AdGuardHome.yaml';
        const fileContent = fs.readFileSync(filePath, 'utf8');
        const config = yaml.parse(fileContent);
        config.dns.upstream_dns = diydns;
        const newYamlContent = yaml.stringify(config);
        fs.writeFileSync(filePath, newYamlContent, 'utf8');
        //在debian下执行docker restart 1d95
        const { exec } = require('child_process');
        exec('docker restart adguardhome', (error, stdout, stderr) => {
          if (error) {
            console.error(
              `Error restarting Docker container: ${error.message}`,
            );
            return;
          }
          if (stderr) {
            console.error(`stderr: ${stderr}`);
            return;
          }
          console.log(`stdout: ${stdout}`);
        });
      }

      ctx.body = yaml.stringify({ upstream_dns: diydns });
      return;
    }
    if (s) {
      // 查询数据库
      let list = await ctx.service.xr.query(
        `SELECT *
         FROM websites
         where iscn = 0`,
      );
      let list1 = await ctx.service.xr.query(
        `SELECT *
         FROM websites
         where iscn = 1`,
      );

      // 定义文件路径
      let filepath1 = '/root/smartdns/config.conf';
      let filepath2 = '/root/smartdns/nameserver.conf';
      let filepathFinal = '/root/smartdns/smartdns.conf';

      // 读取 config.conf 的内容
      let content1 = fs.readFileSync(filepath1, 'utf-8');

      // 通用生成 nameserver 配置的函数
      const generateNameserverContent = (list, mode) =>
        list
          .map((item) => `nameserver /${item.simpleurl}/${mode}`)
          .concat(list.map((item) => `nameserver /.${item.simpleurl}/${mode}`))
          .concat(list.map((item) => `nameserver /-.${item.simpleurl}/${mode}`))
          .concat(list.map((item) => `nameserver /*.${item.simpleurl}/${mode}`))
          .join('\n');

      // 生成 nameserver 内容
      let nameserverContent = generateNameserverContent(list, 'hk');
      let nameserverContent1 = generateNameserverContent(list1, 'cn');

      // 合并 nameserver 内容
      let nameserverFinalContent = `${nameserverContent}\n\n${nameserverContent1}`;

      // 写入 nameserver.conf
      fs.writeFileSync(filepath2, nameserverFinalContent, 'utf-8');

      // 合并 content1 和 nameserver 内容
      let finalContent = `${content1}\n\n${nameserverFinalContent}`;

      // 写入到 smartdns.conf
      fs.writeFileSync(filepathFinal, finalContent, 'utf-8');

      ctx.body = { message: '配置已更新' };
      return;
    }
    const payload = list
      .map((item) => `${item.url}`)
      .concat(list.map((item) => `${item.simpleurl}`))
      .concat(list.map((item) => `+.${item.simpleurl}`))
      .concat(list.map((item) => `*.${item.simpleurl}`))
      .concat(list.map((item) => `.${item.simpleurl}`));
    ctx.body = yaml.stringify({ payload });
  }

  async singboxsite() {
    const { ctx, app } = this;
    let type = ctx.query.type || 1;
    let f = ctx.query.f || false;
    let w = ctx.query.w || false;
    let s = ctx.query.s || false;
    let list = await ctx.service.xr.query(
      `SELECT *
       FROM websites
       where iscn = ${type}`,
    );
    let domain_suffix = +type === 1 ? ['.cn'] : [];
    ctx.body = {
      version: 3,
      rules: [
        {
          domain_suffix: domain_suffix,
          domain: list.map((item) => `${item.url}`),
          domain_keyword: list.map((item) => `${item.simpleurl}`),
        },
      ],
    };
  }

  async bilibili() {
    const { ctx, app } = this;
    const list = [
      'bilibili.com',
      'bilivideo.com',
      'bilivideo.net',
      'bilivideo.cn',
      'hdslb.com',
      'hdslb.net',
      'hdslb.org',
      'douyinvod.com',
    ];
    const payload = list
      .map((item) => `${item}`)
      .concat(list.map((item) => `+.${item}`))
      .concat(list.map((item) => `*.${item}`))
      .concat(list.map((item) => `.${item}`));
    ctx.body = yaml.stringify({ payload });
  }

  async shadowrule() {
    const { ctx, app } = this;
    let type = ctx.query.type || 1;
    let rule = ctx.query.rule;
    let cnlist = await ctx.service.xr.query(
      `SELECT *
       FROM websites
       where iscn = ${type}`,
    );
    let cn = cnlist.map((item) => `.${item.simpleurl}`);
    cn = cn.join('\n');

    if (rule) {
      let cnlist = await ctx.service.xr.query(
        `SELECT *
         FROM websites
         where iscn = 1`,
      );
      let noncnlist = await ctx.service.xr.query(
        `SELECT *
         FROM websites
         where iscn = 0`,
      );
      let cnipranges = await ctx.service.xr.query(`SELECT *
                                                   FROM ip_ranges`);
      let cn = cnlist.map((item) => `DOMAIN-SUFFIX,${item.simpleurl},Direct`);
      let ips = cnipranges.map((item) => `IP-CIDR,${item.range},Direct`);
      let noncn = noncnlist.map(
        (item) => `DOMAIN-SUFFIX,${item.simpleurl},Proxy`,
      );
      let noncndns = noncnlist
        .map(
          (item) =>
            `*.${item.simpleurl} = server:tls://hk.101818.xyz:1853,Proxy`,
        )
        .join('\n');
      cn = cn.concat(noncn).concat(ips).join('\n');

      let ruleset = `
[General]
ipv6 = false
bypass-system = true
skip-proxy = ***********/16, 10.0.0.0/8, **********/12, localhost, *.local, e.crashlytics.com, captive.apple.com, sequoia.apple.com, seed-sequoia.siri.apple.com
bypass-tun = 10.0.0.0/8,**********/10,*********/8,***********/16,**********/12,*********/24,*********/24,***********/24,***********/16,**********/15,************/24,***********/24,*********/4,***************/32
dns-server = https://d.wcy9.com:6448/dns-query,tls://d.wcy9.com:6447

[Rule]
#Apple
RULE-SET,https://raw.githubusercontent.com/blackmatrix7/ios_rule_script/master/rule/Shadowrocket/Apple/Apple.list,DIRECT
DOMAIN-SET,https://raw.githubusercontent.com/blackmatrix7/ios_rule_script/refs/heads/master/rule/Shadowrocket/Apple/Apple_Domain.list,DIRECT
# China
${cn}
# LAN
IP-CIDR,***********/16,DIRECT
IP-CIDR,10.0.0.0/8,DIRECT
IP-CIDR,**********/12,DIRECT
IP-CIDR,*********/8,DIRECT
# Final
FINAL,PROXY

[Host]
localhost = 127.0.0.1


[URL Rewrite]
^https?://(www.)?g.cn https://www.google.com 302
^https?://(www.)?google.cn https://www.google.com 302
`;
      ctx.body = ruleset;
      return;
    }

    ctx.body = cn;
  }

  async querylog() {
    const { ctx, app } = this;
    const filepath = '/my/own/workdir/data/querylog.json';
    let limit = ctx.query.limit || 2000;

    // console.log(limit);

    async function simplifyUrl(url) {
      // 将 url 简化为域名部分,按小数点分成数组，取最后两个部分合并一个simpleurl
      let urlArray = url.split('.');
      return urlArray.slice(-2).join('.');
    }

    async function checkIp(ip) {
      return new Promise(async (resolve, reject) => {
        try {
          let res = await ctx.service.xr.query(`SELECT *
                                                FROM ip_ranges`);
          const ipRanges = res.map((row) => row.range);
          const isInChina = ipRangeCheck(ip, ipRanges);
          resolve(isInChina);
        } catch (err) {
          return reject(err);
        }
      });
    }

    async function insertOrUpdateWebsite(entry) {
      return new Promise(async (resolve, reject) => {
        try {
          // console.log(entry);
          await ctx.service.xr.create('websites', entry);
          resolve();
        } catch (e) {
          reject(e);
        }
      });
    }

    fs.readFile(filepath, 'utf8', async (err, data) => {
      if (err) {
        console.error('Error reading querylog.json:', err);
        return;
      }
      let urls = [];

      // 按行拆分文件内容
      const lines = data.trim().split('\n');
      //开始时间
      const startTime = Date.now();
      // console.log(dateformat(startTime));
      let i = 1;
      for (const line of lines) {
        try {
          // 解析 JSON 行
          const logEntry = JSON.parse(line);

          // 检查查询类型是否为 A 记录
          if (logEntry.QT === 'A') {
            // 解码 Answer 字段
            const answerBuffer = base64url.toBuffer(logEntry.Answer);
            const response = dnsPacket.decode(answerBuffer);

            // 提取第一个 A 记录的 IP 地址
            const firstARecord = response.answers.find(
              (answer) => answer.type === 'A',
            );
            if (firstARecord) {
              let simpleUrl = await simplifyUrl(logEntry.QH);
              let existing = await ctx.service.xr['find']('websites', {
                simpleurl: simpleUrl,
              });
              let tentimes = Date.now();
              if (i % 10 === 0) {
                // console.log(
                //   `每${i}个处理时间:` + (tentimes - startTime) / 1000,
                // );
              }
              if (+i === +limit) {
                break;
              }
              i++;

              if (existing.data) {
                // console.log(simpleUrl, '跳过');
                continue;
              }
              // console.log(x);
              const isInChina = await checkIp(firstARecord.data);
              const queryurl = isInChina
                ? `https://d.wcy9.com:6448/dns-query`
                : `https://hk.101818.xyz:6443/dns-query`;
              const format = `[/${simpleUrl}/]${queryurl}`;

              const entry = {
                ip: firstARecord.data,
                url: logEntry.QH,
                simpleurl: simpleUrl,
                iscn: isInChina ? 1 : 0,
                format: format,
              };

              // 插入数据到数据库，确保url唯一
              if (!existing.data) {
                let x = await insertOrUpdateWebsite(entry);
                // console.log(x);
                urls.push(entry);
              }
            }
          }
        } catch (parseError) {
          console.error('Error parsing line:', parseError);
        }
      }
      // console.log(urls);
      const endtime = Date.now();
      const time = endtime - startTime;
      // await ctx.service.feishu.fs('解析耗时:' + time / 1000 + 's');
      ctx.body = { data: urls };
    });
  }

  async zpgq() {
    const { ctx, app } = this;
    const url = 'http://fz.sun-hrm.com/index.php/totalsum/?EXAMID=2925';
    const keywords = ['804', '601', '613'];
    let notificationText = '';

    try {
      const response = await axios.get(url);
      const $ = cheerio.load(response.data);
      const filteredData = $('#dynamicTable tbody tr')
        .map((_, element) => {
          const tds = $(element).find('td');
          const zw = $(tds[1]).text().trim();
          if (keywords.some((keyword) => zw.includes(keyword))) {
            return {
              name: $(tds[0]).text().trim(),
              zw,
              bmrs: $(tds[2]).text().trim(),
              shtg: $(tds[3]).text().trim(),
              jfrs: $(tds[4]).text().trim(),
            };
          }
        })
        .get()
        .filter(Boolean);

      for (let i = 0; i < filteredData.length; i++) {
        const { name, zw, bmrs } = filteredData[i];
        const key = `zpgq${i}`;
        const previousBmrs = await app.redis.get(key);
        if (+bmrs !== +previousBmrs) {
          await app.redis.set(key, bmrs);
          notificationText += `${name}-${zw}最新报名人数：${bmrs}\n`;
        }
      }

      if (notificationText) {
        await ctx.service.feishu.fs(notificationText);
      }

      ctx.body = filteredData;
    } catch (error) {
      console.error('Error fetching data:', error);
    }
  }
}

module.exports = NewsController;
