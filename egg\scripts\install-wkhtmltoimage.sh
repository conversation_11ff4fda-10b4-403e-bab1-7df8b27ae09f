#!/bin/bash

# 安装 wkhtmltoimage 脚本
# 适用于 Debian/Ubuntu 系统

echo "🖼️ 开始安装 wkhtmltoimage..."

# 检查是否为root用户
if [ "$EUID" -ne 0 ]; then
    echo "❌ 请使用root权限运行此脚本"
    echo "使用: sudo bash $0"
    exit 1
fi

# 检测系统类型
if [ -f /etc/debian_version ]; then
    OS="debian"
    echo "✅ 检测到 Debian/Ubuntu 系统"
elif [ -f /etc/redhat-release ]; then
    OS="redhat"
    echo "✅ 检测到 RedHat/CentOS 系统"
else
    echo "❌ 不支持的操作系统"
    exit 1
fi

# 更新包管理器
echo "📦 更新包管理器..."
if [ "$OS" = "debian" ]; then
    apt-get update
elif [ "$OS" = "redhat" ]; then
    yum update -y
fi

# 安装 wkhtmltopdf (包含 wkhtmltoimage)
echo "🔧 安装 wkhtmltopdf..."
if [ "$OS" = "debian" ]; then
    apt-get install -y wkhtmltopdf
elif [ "$OS" = "redhat" ]; then
    yum install -y wkhtmltopdf
fi

# 验证安装
echo "🔍 验证安装..."
if command -v wkhtmltoimage &> /dev/null; then
    echo "✅ wkhtmltoimage 安装成功"
    echo "版本信息:"
    wkhtmltoimage --version
else
    echo "❌ wkhtmltoimage 安装失败"
    
    # 尝试手动下载安装
    echo "🔄 尝试手动下载安装..."
    
    if [ "$OS" = "debian" ]; then
        # 获取系统架构
        ARCH=$(dpkg --print-architecture)
        
        if [ "$ARCH" = "amd64" ]; then
            DOWNLOAD_URL="https://github.com/wkhtmltopdf/packaging/releases/download/0.12.6.1-2/wkhtmltox_0.12.6.1-2.bullseye_amd64.deb"
            wget -O /tmp/wkhtmltox.deb "$DOWNLOAD_URL"
            dpkg -i /tmp/wkhtmltox.deb
            apt-get install -f -y  # 修复依赖
            rm /tmp/wkhtmltox.deb
        else
            echo "⚠️ 不支持的架构: $ARCH"
        fi
    fi
    
    # 再次验证
    if command -v wkhtmltoimage &> /dev/null; then
        echo "✅ 手动安装成功"
    else
        echo "❌ 手动安装也失败了"
        exit 1
    fi
fi

# 安装中文字体支持
echo "🔤 安装中文字体支持..."
if [ "$OS" = "debian" ]; then
    apt-get install -y \
        fonts-dejavu \
        fonts-liberation \
        fonts-noto-cjk \
        fonts-wqy-zenhei \
        fonts-wqy-microhei
elif [ "$OS" = "redhat" ]; then
    yum install -y \
        dejavu-fonts \
        liberation-fonts \
        google-noto-cjk-fonts \
        wqy-zenhei-fonts \
        wqy-microhei-fonts
fi

# 更新字体缓存
echo "🔄 更新字体缓存..."
fc-cache -fv

# 测试 wkhtmltoimage
echo "🧪 测试 wkhtmltoimage..."
cat > /tmp/test.html << 'EOF'
<!DOCTYPE html>
<html lang="zh">
<head>
    <meta charset="utf-8" />
    <style>
        body {
            font-family: "DejaVu Sans", "WenQuanYi Zen Hei", Arial, sans-serif;
            line-height: 1.5;
            margin: 20px;
            background: white;
        }
    </style>
</head>
<body>
    <h1>测试页面</h1>
    <p>这是一个测试页面，用于验证 wkhtmltoimage 是否正常工作。</p>
    <p>Test page for wkhtmltoimage functionality.</p>
</body>
</html>
EOF

# 执行测试
wkhtmltoimage \
    --width 800 \
    --height 600 \
    --format png \
    --quality 90 \
    --disable-javascript \
    --quiet \
    /tmp/test.html \
    /tmp/test_output.png

if [ -f /tmp/test_output.png ]; then
    echo "✅ 测试成功！生成了测试图片: /tmp/test_output.png"
    echo "图片大小: $(du -h /tmp/test_output.png | cut -f1)"
    
    # 清理测试文件
    rm /tmp/test.html /tmp/test_output.png
else
    echo "❌ 测试失败！"
    exit 1
fi

# 创建使用示例脚本
echo "📝 创建使用示例..."
cat > /usr/local/bin/wkhtml-example << 'EOF'
#!/bin/bash

# wkhtmltoimage 使用示例

if [ $# -ne 2 ]; then
    echo "用法: $0 <input.html> <output.png>"
    exit 1
fi

INPUT_FILE="$1"
OUTPUT_FILE="$2"

if [ ! -f "$INPUT_FILE" ]; then
    echo "错误: 输入文件不存在: $INPUT_FILE"
    exit 1
fi

echo "正在转换: $INPUT_FILE -> $OUTPUT_FILE"

wkhtmltoimage \
    --width 800 \
    --height 600 \
    --format png \
    --quality 90 \
    --disable-javascript \
    --no-images \
    --disable-plugins \
    --quiet \
    "$INPUT_FILE" \
    "$OUTPUT_FILE"

if [ -f "$OUTPUT_FILE" ]; then
    echo "✅ 转换成功！"
    echo "输出文件: $OUTPUT_FILE"
    echo "文件大小: $(du -h "$OUTPUT_FILE" | cut -f1)"
else
    echo "❌ 转换失败！"
    exit 1
fi
EOF

chmod +x /usr/local/bin/wkhtml-example

echo ""
echo "🎉 安装完成！"
echo ""
echo "📋 安装信息:"
echo "  - wkhtmltoimage: $(which wkhtmltoimage)"
echo "  - 版本: $(wkhtmltoimage --version | head -1)"
echo "  - 中文字体支持: 已安装"
echo "  - 示例脚本: /usr/local/bin/wkhtml-example"
echo ""
echo "🚀 使用方法:"
echo "  # 基本用法"
echo "  wkhtmltoimage input.html output.png"
echo ""
echo "  # 使用示例脚本"
echo "  wkhtml-example input.html output.png"
echo ""
echo "  # 在 Node.js 中使用"
echo "  const { spawn } = require('child_process');"
echo "  const child = spawn('wkhtmltoimage', ['input.html', 'output.png']);"
echo ""
echo "💡 优势:"
echo "  - 内存占用: ~20MB (vs Puppeteer ~200MB)"
echo "  - 启动速度: <1秒 (vs Puppeteer 3-5秒)"
echo "  - 稳定性: 更适合服务器环境"
echo "  - 中文支持: 完整支持中文字体"
