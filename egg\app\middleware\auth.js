// 登录校验中间件
module.exports = () => {
  return async function auth(ctx, next) {
    // 只保护页面和部分接口
    if (ctx.session && ctx.session.isLogin) {
      await next();
    } else {
      // 区分API和页面
      if (ctx.path.startsWith('/api/')) {
        ctx.body = { success: false, message: '请先登录' };
      } else {
        // 保存用户原本想访问的URL到session中
        const originalUrl = ctx.originalUrl || ctx.url;
        ctx.session.redirectUrl = originalUrl;
        ctx.redirect('/login');
      }
    }
  };
};
