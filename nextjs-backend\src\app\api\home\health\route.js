/**
 * 健康检查API路由
 * 迁移自egg项目的home.js healthCheck方法
 */

const { NextResponse } = require('next/server');
const { app } = require('../../../../lib/app');

// 确保应用已初始化
let appInitialized = false;

async function ensureAppInitialized() {
  if (!appInitialized) {
    await app.init();
    appInitialized = true;
  }
}

/**
 * 获取系统信息
 */
function getSystemInfo() {
  const memUsage = process.memoryUsage();
  const heapUsageRatio = memUsage.heapUsed / memUsage.heapTotal;
  const uptime = process.uptime();
  
  return {
    pid: process.pid,
    uptime: Math.round(uptime),
    memoryUsage: {
      heapUsed: Math.round(memUsage.heapUsed / 1024 / 1024), // MB
      heapTotal: Math.round(memUsage.heapTotal / 1024 / 1024), // MB
      heapUsageRatio: Math.round(heapUsageRatio * 100), // %
      rss: Math.round(memUsage.rss / 1024 / 1024), // MB
      external: Math.round(memUsage.external / 1024 / 1024), // MB
    },
    cpuUsage: process.cpuUsage(),
    nodeVersion: process.version,
    platform: process.platform,
    arch: process.arch,
  };
}

async function GET(request) {
  try {
    await ensureAppInitialized();
    
    const systemInfo = getSystemInfo();
    const memUsage = process.memoryUsage();
    const heapUsageRatio = memUsage.heapUsed / memUsage.heapTotal;
    const uptime = process.uptime();
    
    // 简单的健康检查逻辑
    const isHealthy = heapUsageRatio < 0.9 && uptime > 10; // 堆内存使用率<90% 且运行时间>10秒
    
    // 获取应用健康状态
    const appHealth = await app.healthCheck();
    
    const healthStatus = {
      status: isHealthy ? 'healthy' : 'unhealthy',
      timestamp: new Date().toISOString(),
      uptime: systemInfo.uptime,
      memoryUsage: systemInfo.memoryUsage,
      system: {
        nodeVersion: systemInfo.nodeVersion,
        platform: systemInfo.platform,
        arch: systemInfo.arch,
        pid: systemInfo.pid,
      },
      services: appHealth.services || {},
      environment: process.env.NODE_ENV || 'development',
    };
    
    console.log('🏥 健康检查:', {
      status: healthStatus.status,
      heapUsageRatio: `${systemInfo.memoryUsage.heapUsageRatio}%`,
      uptime: `${systemInfo.uptime}s`,
    });
    
    return NextResponse.json(healthStatus, {
      status: isHealthy ? 200 : 503
    });
    
  } catch (error) {
    console.error('❌ Health API错误:', error.message);
    return NextResponse.json({ 
      status: 'unhealthy',
      error: error.message,
      timestamp: new Date().toISOString()
    }, { status: 503 });
  }
}

module.exports = { GET };
