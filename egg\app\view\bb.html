<!doctype html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport"
          content="width=device-width, user-scalable=no, initial-scale=1.0, maximum-scale=1.0, minimum-scale=1.0">
    <meta http-equiv="X-UA-Compatible" content="ie=edge">
    <script src="../public/vue/vue.js"></script>
    <!-- import CSS -->
    <link rel="stylesheet" href="../public/element/index.css">
    <!-- import JavaScript -->
    <script src="../public/element/index.js"></script>
    <script src="../public/js/axios.min.js"></script>
    <title>Document</title>
</head>

<body>
<div id="app">
    <div>
        <el-table
                :data="data"
                :default-sort="{ prop: 'date', order: 'descending' }"
                style="width: 100%"
        >
            <el-table-column prop="job_id" label="job_id" sortable/>
            <el-table-column prop="unit_name" label="unit_name" sortable/>
            <el-table-column prop="job_title" label="job_title" sortable/>
        </el-table>
    </div>

</div>

</body>
<script>
    const {createApp, ref, onMounted} = Vue
    createApp({
        setup() {
            const message = ref('Hello vue!')
            const data = ref([])
            const getData = () => {
                console.log(1)
                axios.get('/zyx').then(res => {
                    data.value = res.data.data.list
                    console.log(data.value)
                })
            }
            onMounted(() => {
                getData()
            })
            return {
                message, getData, data
            }
        }
    }).use(ElementPlus).mount('#app')
</script>
</html>