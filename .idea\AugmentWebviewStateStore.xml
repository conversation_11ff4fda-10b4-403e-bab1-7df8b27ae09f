<?xml version="1.0" encoding="UTF-8"?>
<project version="4">
  <component name="AugmentWebviewStateStore">
    <option name="stateMap">
      <map>
        <entry key="CHAT_STATE" value="{&quot;currentConversationId&quot;:&quot;16bbe79a-8012-47eb-8480-acefe46f7f86&quot;,&quot;conversations&quot;:{&quot;b1a5750c-3b15-472b-838d-a95346987353&quot;:{&quot;id&quot;:&quot;b1a5750c-3b15-472b-838d-a95346987353&quot;,&quot;createdAtIso&quot;:&quot;2025-07-30T03:12:54.673Z&quot;,&quot;lastInteractedAtIso&quot;:&quot;2025-07-30T08:30:40.647Z&quot;,&quot;chatHistory&quot;:[{&quot;status&quot;:&quot;success&quot;,&quot;request_id&quot;:&quot;04f356cb-fec4-4494-8c30-cfc9a5f9545e&quot;,&quot;request_message&quot;:&quot;nihao &quot;,&quot;rich_text_json_repr&quot;:{&quot;type&quot;:&quot;doc&quot;,&quot;content&quot;:[{&quot;type&quot;:&quot;paragraph&quot;,&quot;content&quot;:[{&quot;type&quot;:&quot;text&quot;,&quot;text&quot;:&quot;nihao &quot;}]}]},&quot;mentioned_items&quot;:[],&quot;structured_output_nodes&quot;:[{&quot;id&quot;:0,&quot;type&quot;:0,&quot;content&quot;:&quot;&quot;},{&quot;id&quot;:1,&quot;type&quot;:2,&quot;content&quot;:&quot;\n\n---\n\n*<NAME_EMAIL> has been suspended. To continue, [purchase a subscription](https://app.augmentcode.com/account).*\n\n---\n\n*Your access expires in 2 days. [Purchase a subscription](https://app.augmentcode.com/account)*&quot;}],&quot;seen_state&quot;:&quot;seen&quot;,&quot;structured_request_nodes&quot;:[{&quot;id&quot;:0,&quot;type&quot;:0,&quot;text_node&quot;:{&quot;content&quot;:&quot;nihao &quot;}},{&quot;id&quot;:1,&quot;type&quot;:4,&quot;ide_state_node&quot;:{&quot;workspaceFolders&quot;:[{&quot;repositoryRoot&quot;:&quot;I:/workspace/egg&quot;,&quot;folderRoot&quot;:&quot;I:/workspace/egg&quot;}],&quot;workspaceFoldersUnchanged&quot;:false}}],&quot;timestamp&quot;:&quot;2025-07-30T03:12:59.095Z&quot;,&quot;response_text&quot;:&quot;\n\n---\n\n*<NAME_EMAIL> has been suspended. To continue, [purchase a subscription](https://app.augmentcode.com/account).*\n\n---\n\n*Your access expires in 2 days. [Purchase a subscription](https://app.augmentcode.com/account)*&quot;,&quot;workspace_file_chunks&quot;:[]}],&quot;feedbackStates&quot;:{&quot;temp-fe-4675c324-1fcb-4ae5-b492-0c6a195ff760&quot;:{&quot;selectedRating&quot;:0,&quot;feedbackNote&quot;:&quot;&quot;}},&quot;toolUseStates&quot;:{},&quot;draftExchange&quot;:{&quot;request_message&quot;:&quot;&quot;,&quot;rich_text_json_repr&quot;:{&quot;type&quot;:&quot;doc&quot;,&quot;content&quot;:[{&quot;type&quot;:&quot;paragraph&quot;}]},&quot;mentioned_items&quot;:[],&quot;status&quot;:&quot;draft&quot;},&quot;requestIds&quot;:[],&quot;isPinned&quot;:false,&quot;isShareable&quot;:true,&quot;extraData&quot;:{&quot;hasDirtyEdits&quot;:false},&quot;personaType&quot;:0,&quot;rootTaskUuid&quot;:&quot;********-4478-4367-a737-26d9fb45fcc9&quot;,&quot;draftActiveContextIds&quot;:[&quot;I:/workspace/egg/package.json&quot;,&quot;I:/workspacefalse&quot;,&quot;userGuidelines&quot;]},&quot;16bbe79a-8012-47eb-8480-acefe46f7f86&quot;:{&quot;id&quot;:&quot;16bbe79a-8012-47eb-8480-acefe46f7f86&quot;,&quot;createdAtIso&quot;:&quot;2025-07-30T08:32:18.548Z&quot;,&quot;lastInteractedAtIso&quot;:&quot;2025-07-30T08:32:26.781Z&quot;,&quot;chatHistory&quot;:[{&quot;status&quot;:&quot;success&quot;,&quot;request_id&quot;:&quot;bb9d8681-3ffd-4fd2-82eb-71df9569c442&quot;,&quot;request_message&quot;:&quot;你好&quot;,&quot;rich_text_json_repr&quot;:{&quot;type&quot;:&quot;doc&quot;,&quot;content&quot;:[{&quot;type&quot;:&quot;paragraph&quot;,&quot;content&quot;:[{&quot;type&quot;:&quot;text&quot;,&quot;text&quot;:&quot;你好&quot;}]}]},&quot;mentioned_items&quot;:[],&quot;structured_output_nodes&quot;:[{&quot;id&quot;:0,&quot;type&quot;:0,&quot;content&quot;:&quot;&quot;},{&quot;id&quot;:1,&quot;type&quot;:2,&quot;content&quot;:&quot;\n\n---\n\n*<NAME_EMAIL> has been suspended. To continue, [purchase a subscription](https://app.augmentcode.com/account).*\n\n---\n\n*Your access expires in 2 days. [Purchase a subscription](https://app.augmentcode.com/account)*&quot;}],&quot;seen_state&quot;:&quot;seen&quot;,&quot;structured_request_nodes&quot;:[{&quot;id&quot;:0,&quot;type&quot;:0,&quot;text_node&quot;:{&quot;content&quot;:&quot;你好&quot;}},{&quot;id&quot;:1,&quot;type&quot;:4,&quot;ide_state_node&quot;:{&quot;workspaceFolders&quot;:[{&quot;repositoryRoot&quot;:&quot;I:/workspace/egg&quot;,&quot;folderRoot&quot;:&quot;I:/workspace/egg&quot;}],&quot;workspaceFoldersUnchanged&quot;:false}}],&quot;timestamp&quot;:&quot;2025-07-30T08:32:26.787Z&quot;,&quot;response_text&quot;:&quot;\n\n---\n\n*<NAME_EMAIL> has been suspended. To continue, [purchase a subscription](https://app.augmentcode.com/account).*\n\n---\n\n*Your access expires in 2 days. [Purchase a subscription](https://app.augmentcode.com/account)*&quot;,&quot;workspace_file_chunks&quot;:[]}],&quot;feedbackStates&quot;:{&quot;temp-fe-80ac436e-78e8-41e4-ad2c-f53d9356ba23&quot;:{&quot;selectedRating&quot;:0,&quot;feedbackNote&quot;:&quot;&quot;}},&quot;toolUseStates&quot;:{},&quot;draftExchange&quot;:{&quot;request_message&quot;:&quot;&quot;,&quot;rich_text_json_repr&quot;:{&quot;type&quot;:&quot;doc&quot;,&quot;content&quot;:[{&quot;type&quot;:&quot;paragraph&quot;}]},&quot;mentioned_items&quot;:[],&quot;status&quot;:&quot;draft&quot;},&quot;requestIds&quot;:[],&quot;isPinned&quot;:false,&quot;isShareable&quot;:true,&quot;extraData&quot;:{&quot;hasDirtyEdits&quot;:false,&quot;isAgentConversation&quot;:false,&quot;baselineTimestamp&quot;:0},&quot;personaType&quot;:0,&quot;rootTaskUuid&quot;:&quot;4a2ca94c-3950-4370-87da-ce34e6ffef02&quot;}},&quot;agentExecutionMode&quot;:&quot;manual&quot;,&quot;isPanelCollapsed&quot;:true,&quot;displayedAnnouncements&quot;:[]}" />
      </map>
    </option>
  </component>
</project>