/**
 * Home首页API路由
 * 迁移自egg项目的home.js index方法
 */

const { NextResponse } = require('next/server');

async function GET(request) {
  try {
    // 返回简单的欢迎消息，与原egg项目保持一致
    return new Response('hi, egg - 性能监控测试!', {
      status: 200,
      headers: {
        'Content-Type': 'text/plain; charset=utf-8',
      },
    });
  } catch (error) {
    console.error('❌ Home首页API错误:', error.message);
    return NextResponse.json({ 
      error: error.message,
      timestamp: new Date().toISOString()
    }, { status: 500 });
  }
}

module.exports = { GET };
