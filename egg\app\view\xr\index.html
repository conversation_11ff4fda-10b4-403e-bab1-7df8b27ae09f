<html>
<head>
    <meta charset="UTF-8"/>
    <meta name="viewport" content="width=device-width,initial-scale=1.0"/>
    <script src="/public/vue/vue.min.js"></script>
    <script src="/public/element/index.js"></script>
    <link rel="stylesheet" href="/public/element/index.css">
    <script src="/public/js/axios.min.js"></script>
    <title>Element Plus demo</title>
</head>
<body>
<div>
</div>
<div id="app">

    <div>
        <p v-for="item in tableData">
            <a :href="item.url" type="primary" target="_blank">
                <img :src="item.img">
            </a>
        </p>
    </div>
    <div>
<!--        <a href="/xr?page={{ppage}}">prev</a>-->
<!--        <a href="/xr?page={{npage}}">next</a>-->
        <div class="block">
            <span class="demonstration">显示总数</span>
            <el-pagination
                    @size-change="handleSizeChange"
                    @current-change="handleCurrentChange"
                    :current-page.sync="currentPage1"
                    :page-size="12"
                    layout="total, prev, pager, next"
                    :total="1000">
            </el-pagination>
        </div>
    </div>
</div>

<script>
    new Vue({
        el: '#app',
        data: function () {
            return {
                tableData: null,
                currentPage1: 1,
            }
        },
        created() {

        },
        mounted() {
            this.getData();
        },
        methods: {
            toTop() {
                document.body.scrollTop = 0;
            },
            handleSizeChange(val) {
                console.log(`每页 ${val} 条`);
            },
            handleCurrentChange(val) {
                this.getData(val);
            },
            getData(page = 1) {
                axios.get('/xrlist?page=' + page).then(res => {
                    console.log(`当前页: ${page}`);
                    this.tableData = res.data;
                    this.toTop();
                })
            }
        }
    })


</script>

<style>
    html {
        width: auto;
        height: auto;
        max-width: 100%;
        max-height: 100%;
    }

</style>
</body>
</html>
