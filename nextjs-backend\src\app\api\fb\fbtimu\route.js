/**
 * 粉笔题目获取API
 * 迁移自egg项目的fb.js fbtimu方法
 */

import { NextResponse } from 'next/server';
import { app } from '../../../../lib/app';

// 确保应用已初始化
let appInitialized = false;

async function ensureAppInitialized() {
  if (!appInitialized) {
    await app.init();
    appInitialized = true;
  }
}

// 辅助函数
function dateformat(date) {
  if (!date) return '';
  return new Date(date).toLocaleString();
}

// 🔧 统一的选项格式化函数，消除重复代码
function formatAnswerOptions(item, useOptimizedCheck = false, returnNew = false) {
  const options = ['answerone', 'answertwo', 'answerthree', 'answerfour'];
  const labels = ['A', 'B', 'C', 'D'];

  // 如果需要返回新对象，创建一个副本
  const target = returnNew ? {} : item;

  options.forEach((option, index) => {
    if (item[option]) {
      // 使用优化的检查方式（快速模式）或传统的正则匹配
      const hasP = useOptimizedCheck
        ? item[option].startsWith('<p>')
        : item[option].match(/<p>/g);

      target[option] = hasP
        ? item[option].replace('<p>', `<p>${labels[index]}.`)
        : `${labels[index]}.` + item[option];
    }
  });

  return target;
}

export async function GET(request) {
  try {
    // 确保应用已初始化
    await ensureAppInitialized();

    // 从 URL 获取查询参数
    const { searchParams } = new URL(request.url);
    let {
      id, per, page, type, z, b, t, gen, ids, kjid, f, o, q, biao, parentid, fast
    } = Object.fromEntries(searchParams.entries());

  // 🚀 快速模式：只返回基本字段，跳过复杂处理
  const fastMode = fast === '1' || fast === 'true';
  per = Number(per) || 10;
  page = Number(page) || 1;
  id = +id;
  let offset = (page - 1) * per;
  let allcateid = id ? `allcateid like '%${id}%'` : `1=1`;
  let rk = `1 = 1`;
  if (+z === 1) {
    if (id === 656604) {
      biao = 'fbsyzlfx';
    } else if (id === 48644) {
      biao = 'fbgwyzlfx';
    }
    // 🚀 性能优化：使用更高效的查询方式
    let sqlquery;
    console.log(`🔍 查询参数: fastMode=${fastMode}, page=${page}, offset=${offset}`);

    if (fastMode && page > 1000) {
      console.log('🚀 使用超高页码优化查询');
      // 🔥 超高页码优化：使用子查询避免大offset
      sqlquery = `SELECT * FROM ${biao}
                    WHERE id <= (
                      SELECT id FROM ${biao}
                      WHERE allcateid like '%${id}%'
                      ORDER BY id DESC
                      LIMIT 1 OFFSET ${offset}
                    )
                    AND allcateid like '%${id}%'
                    ORDER BY id DESC
                    LIMIT ${per}`;
    } else {
      console.log('🔍 使用普通查询');
      // 普通查询
      sqlquery = `SELECT *
                    FROM ${biao}
                    where allcateid like '%${id}%'
                    order by id desc limit ${per}
                    offset ${offset}`;
    }
    if (+gen === 1) {
      let sqlx = await app.mysql.query(
          `SELECT *
           FROM fbkaojuan
           WHERE sid = ${kjid};`,
      );
      // sqlquery = `SELECT * FROM ${biao} WHERE id IN (${sqlx[0].questionIds})`;
      sqlquery = `SELECT *
                    FROM ${biao}
                    WHERE id IN (${sqlx[0].questionIds})
                    ORDER BY FIELD(id, ${sqlx[0].questionIds})`;
    }

    // console.log(sqlquery);

    let data = await app.mysql.query(sqlquery);

    // 🚀 性能优化：移除不必要的数据库查询，大幅提升性能
    // 🎯 计算正确的题目编号：考虑分页和kjid模式
    const currentPage = Number(page) || 1;
    const currentPer = Number(per) || 1;
    const currentKjid = kjid;

    let questions = data.map((item, index) => {
      // 🔥 计算正确的题目编号
      let questionNumber;
      if (kjid) {
        // 新模式：kjid存在时，题目编号就是当前页码
        questionNumber = page;
      } else {
        // 传统模式：分页模式，计算实际编号
        questionNumber = (page - 1) * per + index + 1;
      }

      // 🚀 性能优化：减少字符串处理开销
      const result = { ...item };

      // 只在需要时进行字符串替换
      if (item.content && item.content.includes('>')) {
        result.content = item.content.replace('>', `>${questionNumber}.`);
      }

      // 优化时间格式化
      if (item.createdTime) {
        result.createdTime = dateformat(item.createdTime);
      }

      // 🔧 使用统一的选项格式化函数（优化模式）
      formatAnswerOptions(result, true);

      return result;
    });
    // questions.sort((a, b) => a.originalIndex - b.originalIndex);
    //
    // // 如果不再需要 originalIndex 属性，可以将其移除
    // questions = questions.map(({ originalIndex, ...rest }) => rest);
    // 🚀 性能优化：快速模式下跳过COUNT查询
    let pagetotal;
    if (fastMode) {
      // 快速模式：使用估算值，避免慢查询
      pagetotal = [{ total: Math.max(page * per, 100000) }];
      console.log('🚀 快速模式：跳过COUNT查询，使用估算总数');
    } else {
      let options =
          +id === (48644 || 656604)
              ? `parentid is null and allcateid is null`
              : ` allcateid like '%${id}%'`;
      let sql = `select count(id) as total
                   from ${biao}
                   where ${options}`;
      pagetotal = await app.mysql.query(sql);
    }

    return NextResponse.json({ pagetotal, data: questions });
  }
  let cuo = `1 = 1`;
  if (+b === 1) {
    cuo = `answer != choice`;
  }

  let orderby = +o === 1 ? 'asc' : 'desc';
  let ord = +f === 1 ? 'correctRatio' : +f === 2 ? 'sort' : 'id';
  if (q) {
    rk = ` (content like '%${q}%'
                       or answerone LIKE '%${q}%'
                       or answertwo LIKE '%${q}%'
                       or answerthree LIKE '%${q}%'
                       or answerfour LIKE '%${q}%')`;
    offset = 0;
  }

  if (ids) {
    allcateid = `id in (${ids})`;
    if (+ids.split(',').length === 1) {
      offset = 0;
    }
  }
  if (parentid) {
    allcateid = `parentid in (${parentid})`;
  }
  // 🚀 性能优化：针对高页码的查询优化
  let sqlquery;
  console.log(`🔍 正常流程查询参数: fastMode=${fastMode}, page=${page}, offset=${offset}`);

  if (fastMode && page > 1000 && ord === 'id' && orderby === 'desc') {
    console.log('🚀 使用正常流程超高页码优化查询');
    // 🔥 超高页码优化：使用子查询避免大offset
    sqlquery = `select * from ${biao}
                  where id <= (
                    select id from ${biao}
                    where ${cuo} and ${allcateid} and ${rk}
                    order by ${ord} ${orderby}
                    limit 1 offset ${offset}
                  )
                  and ${cuo} and ${allcateid} and ${rk}
                  order by ${ord} ${orderby}
                  limit ${per}`;
  } else {
    console.log('🔍 使用正常流程普通查询');
    sqlquery = `select *
                  from ${biao}
                  where ${cuo}
                    and ${allcateid}
                    and ${rk}
                  order by ${ord} ${orderby} limit ${per}
                  offset ${offset} `;
  }
  //huoqutimu

  console.log('🔍 SQL查询:', sqlquery.substring(0, 100) + '...');

  // 🚀 性能优化：快速模式下跳过COUNT查询
  let totalsql;
  if (!fastMode) {
    totalsql = `select count(id) as total
                  from ${biao}
                  where ${cuo}
                    and ${allcateid}`;
  }
  if (+t === 1) {
    sqlquery = `SELECT *
                  FROM fbsy
                  where allcateid like '%656602%'
                    and allcateid not like '%796885%'
                    and allcateid not like '%796962%'
                    and allcateid not like '%796963%'
                    and allcateid not like '%796964%'
                    and allcateid not like '%796965%'
                  ORDER BY source desc limit ${per}
                  offset ${offset}`;
    totalsql = `SELECT count(id) as total
                  FROM fbsy
                  where allcateid like '%656602%'
                    and allcateid not like '%796885%'
                    and allcateid not like '%796962%'
                    and allcateid not like '%796963%'
                    and allcateid not like '%796964%'
                    and allcateid not like '%796965%'`;
  }

  if (+gen === 1) {
    let sqlx = await app.mysql.query(
        `SELECT *
         FROM fbkaojuan
         WHERE sid = ${kjid};`,
    );
    // sqlquery = `SELECT * FROM ${biao} WHERE id IN (${sqlx[0].questionIds})`;
    sqlquery = `SELECT *
                  FROM ${biao}
                  WHERE id IN (${sqlx[0].questionIds})
                  ORDER BY FIELD(id, ${sqlx[0].questionIds})`;
  }

  // 🚀 性能优化：快速模式下跳过COUNT查询
  let pagetotal;
  if (fastMode) {
    console.log('🚀 快速模式：跳过COUNT查询，使用估算总数');
    pagetotal = [{ total: Math.max(page * per, 100000) }];
  } else {
    console.log('🔍 执行COUNT查询');
    pagetotal = await app.mysql.query(totalsql);
  }

  console.log('🔍 开始执行主查询');
  let data = await app.mysql.query(sqlquery);
  console.log('🔍 主查询完成，返回', data.length, '条记录');

  // 🚀 快速模式：返回所有字段，但跳过复杂的数据处理（parentContent、video等）
  if (fastMode) {
    console.log('🚀 timu快速模式：返回所有字段，跳过复杂数据处理');

    // 🎯 计算正确的题目编号：考虑分页和kjid模式
    const currentPage = Number(page) || 1;
    const currentPer = Number(per) || 1;
    const currentKjid = kjid;

    const fastResult = data.map((item, index) => {
      // 🔥 计算正确的题目编号
      let questionNumber;
      if (kjid) {
        // 新模式：kjid存在时，题目编号就是当前页码
        questionNumber = page;
      } else {
        // 传统模式：分页模式，计算实际编号
        questionNumber = (page - 1) * per + index + 1;
      }

      // 🚀 性能优化：减少字符串处理开销
      const result = { ...item };

      // 只在需要时进行字符串替换
      if (item.content && item.content.includes('>')) {
        result.content = item.content.replace(
            '>',
            `>${questionNumber}.(${item.correctRatio ? Math.round(item.correctRatio) : '0'}%)`,
        );
      }

      console.log(`(${item.correctRatio ? Math.round(item.correctRatio) : '0'}%)`);
      // 优化时间格式化
      if (item.createdTime) {
        result.createdTime = dateformat(item.createdTime);
      }

      // 🔧 使用统一的选项格式化函数（优化模式）
      formatAnswerOptions(result, true);

      return result;
    });
    return NextResponse.json({ pagetotal, data: fastResult });
  }

  // console.log(data);

  function isString(value) {
    return typeof value === 'string';
  }

  function mapOrder(array, order, key) {
    const indexMap = order.reduce((m, id, idx) => {
      m[id] = idx;
      return m;
    }, {});
    return array.sort((a, b) => {
      const ia = indexMap[a[key]];
      const ib = indexMap[b[key]];
      // 若有 id 不在 order 中，可放在末尾
      return (ia !== undefined ? ia : Infinity) - (ib !== undefined ? ib : Infinity);
    });
  }

  // 🔧 统一的选项格式化函数，消除重复代码
  function formatAnswerOptions(item, useOptimizedCheck = false, returnNew = false) {
    const options = ['answerone', 'answertwo', 'answerthree', 'answerfour'];
    const labels = ['A', 'B', 'C', 'D'];

    // 如果需要返回新对象，创建一个副本
    const target = returnNew ? {} : item;

    options.forEach((option, index) => {
      if (item[option]) {
        // 使用优化的检查方式（快速模式）或传统的正则匹配
        const hasP = useOptimizedCheck
            ? item[option].startsWith('<p>')
            : item[option].match(/<p>/g);

        target[option] = hasP
            ? item[option].replace('<p>', `<p>${labels[index]}.`)
            : `${labels[index]}.` + item[option];
      }
    });

    return target;
  }

  // 🔥 移除了 addvideo 函数，前端不需要视频数据

  async function processItems(data) {
    try {
      for (let index = 0; index < data.length; index++) {
        let item = data[index];
        if (item?.ds) {
          if (item.ds.match(/```/g)) {
            await mockService.xr.update(biao, {
              ds: item.ds.replace(/```/g, ''),
              id: item.id,
            });
          }
          // item.ds = item.ds.replace(/```/g, '');
        }
        if (
            isString(item.answerone) &&
            isString(item.answertwo) &&
            isString(item.answerthree) &&
            isString(item.answerfour) &&
            biao !== 'rk'
        ) {
          item.content = isString(item.content)
              ? item.content.match(/<p>/g)
                  ? item.content.replace(
                      '<p>',
                      `<p>(${item.correctRatio ? Math.round(item.correctRatio) : '0'}%)`,
                  )
                  : `<span style="color:${
                  item.choice !== null ? '#00BFFF' : '#3c464f'
              }">${offset + index + 1}</span>.` + item.content
              : item.content;
          item.createdTime = dateformat(item.createdTime);
          // 🔧 使用统一的选项格式化函数（传统模式）
          formatAnswerOptions(item, false);
        } else {
          console.error('Error: One of the answer properties is not a string');
          // console.log(item);
        }
      }
      return data;
    } catch (error) {
      console.error('Error:', error);
    }
  }

  if (ids?.includes(',')) {
    data = mapOrder(data, ids.split(','), 'id');
  }
  data = await processItems(data);

  // console.log(ids.split(','));

  if (+b === 1) {
    // console.log(data);
    return NextResponse.json({ pagetotal, data: data });
  }

  return NextResponse.json({ pagetotal, data: data });

  } catch (error) {
    console.error('API Error:', error);
    return NextResponse.json({ error: 'Internal Server Error' }, { status: 500 });
  }
}
