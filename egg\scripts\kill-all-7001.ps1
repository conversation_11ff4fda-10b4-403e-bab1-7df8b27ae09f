# 强力清理 7001 端口的所有进程
Write-Host "🔍 查找占用 7001 端口的所有进程..." -ForegroundColor Cyan

# 获取所有占用 7001 端口的进程
$processes = netstat -ano | Select-String ":7001" | ForEach-Object {
    $line = $_.Line.Trim()
    $parts = $line -split '\s+'
    if ($parts.Length -ge 5) {
        $parts[4]  # PID 在第5列
    }
} | Sort-Object -Unique

if ($processes.Count -eq 0) {
    Write-Host "✅ 端口 7001 未被占用" -ForegroundColor Green
    exit 0
}

Write-Host "📋 找到 $($processes.Count) 个进程占用端口 7001:" -ForegroundColor Yellow
foreach ($pid in $processes) {
    if ($pid -and $pid -ne "0") {
        try {
            $processInfo = Get-Process -Id $pid -ErrorAction SilentlyContinue
            if ($processInfo) {
                Write-Host "  PID: $pid - $($processInfo.ProcessName)" -ForegroundColor White
            }
            else {
                Write-Host "  PID: $pid - (进程已不存在)" -ForegroundColor Gray
            }
        }
        catch {
            Write-Host "  PID: $pid - (无法获取进程信息)" -ForegroundColor Gray
        }
    }
}

Write-Host "`n🔪 开始终止进程..." -ForegroundColor Red

$killedCount = 0
foreach ($pid in $processes) {
    if ($pid -and $pid -ne "0") {
        try {
            taskkill /PID $pid /F 2>$null
            if ($LASTEXITCODE -eq 0) {
                Write-Host "✅ 成功终止进程 $pid" -ForegroundColor Green
                $killedCount++
            }
            else {
                Write-Host "⚠️ 进程 $pid 可能已经不存在" -ForegroundColor Yellow
            }
        }
        catch {
            Write-Host "❌ 无法终止进程 $pid" -ForegroundColor Red
        }
    }
}

Write-Host "`n🔍 验证端口状态..." -ForegroundColor Cyan
Start-Sleep -Seconds 1

$remainingProcesses = netstat -ano | Select-String ":7001"
if ($remainingProcesses.Count -eq 0) {
    Write-Host "✅ 端口 7001 已完全释放！" -ForegroundColor Green
    Write-Host "📊 总计终止了 $killedCount 个进程" -ForegroundColor Cyan
}
else {
    Write-Host "⚠️ 仍有进程占用端口 7001:" -ForegroundColor Yellow
    $remainingProcesses | ForEach-Object { Write-Host "  $_" -ForegroundColor Gray }
}

Write-Host "`n💡 现在可以启动应用了: pnpm dev:enhanced" -ForegroundColor Magenta
