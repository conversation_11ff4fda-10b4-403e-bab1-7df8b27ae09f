import { execSync } from 'child_process';
import fs from 'fs';
import { OpenAI } from 'openai';
import path from 'path';
import * as readline from 'readline';

// 工具函数：检查执行环境并选择提交范围
function checkExecutionDirectory() {
  const currentDir = process.cwd();
  const currentDirName = path.basename(currentDir);

  console.log('🔍 Git提交助手');
  console.log(`📁 当前目录: ${currentDir}`);

  // 检查是否在egg目录
  if (currentDirName !== 'egg') {
    console.log('❌ 请在 egg 主目录下运行此脚本');
    console.log('💡 正确的执行方式: cd /path/to/egg && node generate-commit.js');
    process.exit(1);
  }

  console.log('✅ 当前在 egg 主目录');
  console.log(''); // 空行分隔
}

function selectCommitScope() {
  const currentDir = process.cwd();

  // 检查vue目录是否存在
  const vueDir = path.join(currentDir, 'vue');
  const hasVueDir = fs.existsSync(vueDir);

  console.log('� 请选择提交范围:');
  console.log('1️⃣  EggJS项目 - 包括后端代码、配置、脚本等所有变更');
  if (hasVueDir) {
    console.log('2️⃣  Vue项目 - 只提交 vue 目录下的前端变更');
  } else {
    console.log('2️⃣  Vue项目 - vue 目录不存在，无法选择');
  }
  console.log(''); // 空行分隔

  return new Promise((resolve) => {
    const rl = readline.createInterface({
      input: process.stdin,
      output: process.stdout,
    });

    const askChoice = () => {
      const options = hasVueDir ? '1 或 2' : '1';
      rl.question(`❓ 请输入选择 (${options}): `, (answer) => {
        const choice = answer.trim();

        if (choice === '1') {
          console.log('✅ 选择: 提交 EggJS 项目变更');
          rl.close();
          resolve({ scope: 'egg', workDir: currentDir, gitDir: currentDir });
        } else if (choice === '2' && hasVueDir) {
          console.log('✅ 选择: 只提交 Vue 项目变更');
          rl.close();
          resolve({ scope: 'vue', workDir: vueDir, gitDir: currentDir });
        } else {
          console.log(`❌ 无效选择，请输入 ${options}`);
          askChoice(); // 递归询问
        }
      });
    };

    askChoice();
  });
}

// 工具函数：更新.gitignore文件
function updateGitignore() {
  const gitignorePath = '.gitignore';
  const filesToIgnore = ['changes.diff', 'status.txt', 'commit_error.json', 'commit_msg.txt'];

  // commit_debug.json 默认不忽略，除非设置了 NO_DEBUG
  if (process.env.NO_DEBUG) {
    filesToIgnore.push('commit_debug.json');
  }

  try {
    let gitignoreContent = '';
    if (fs.existsSync(gitignorePath)) {
      gitignoreContent = fs.readFileSync(gitignorePath, 'utf-8');
    }

    let needsUpdate = false;
    const linesToAdd = [];

    filesToIgnore.forEach((file) => {
      if (!gitignoreContent.includes(file)) {
        linesToAdd.push(file);
        needsUpdate = true;
      }
    });

    if (needsUpdate) {
      if (gitignoreContent && !gitignoreContent.endsWith('\n')) {
        gitignoreContent += '\n';
      }

      if (linesToAdd.length > 0) {
        gitignoreContent += '\n# 自动生成的提交相关文件\n';
        gitignoreContent += linesToAdd.join('\n') + '\n';
      }

      fs.writeFileSync(gitignorePath, gitignoreContent, 'utf-8');
      console.log('✅ 已更新 .gitignore 文件，添加生成文件到忽略列表');
    }
  } catch (error) {
    console.warn('⚠️  更新 .gitignore 失败:', error.message);
  }
}

// 工具函数：安全地转义commit消息中的特殊字符
function escapeCommitMessage(message) {
  if (!message || typeof message !== 'string') {
    return '[自动生成] 代码更新';
  }

  return message
    .replace(/"/g, '\\"') // 转义双引号
    .replace(/`/g, '\\`') // 转义反引号
    .replace(/\$/g, '\\$') // 转义美元符号
    .replace(/\n/g, ' ') // 将换行符替换为空格
    .replace(/\r/g, '') // 移除回车符
    .replace(/\s+/g, ' ') // 合并多个空格为一个
    .trim(); // 去除首尾空格
}

// 工具函数：提取有效的commit消息
function extractCommitMessage(content) {
  if (!content || typeof content !== 'string') {
    return '[自动生成] 代码更新';
  }

  // 清理内容
  const cleanContent = content
    .replace(/```[\s\S]*?```/g, '') // 移除代码块
    .replace(/\*\*([^*]+)\*\*/g, '$1') // 移除粗体标记
    .replace(/\*([^*]+)\*/g, '$1') // 移除斜体标记
    .trim();

  // 按行分割并找到所有有效行
  const lines = cleanContent
    .split('\n')
    .map((line) => line.trim())
    .filter((line) => line.length > 0);

  if (lines.length === 0) {
    return '[自动生成] 代码更新';
  }

  // 查找标题行（格式：[类型] 描述）
  const formatRegex = /^\[([^\]]+)\]\s*(.+)/;
  let titleLine = '';
  let detailLines = [];
  let foundTitle = false;

  for (const line of lines) {
    const match = line.match(formatRegex);
    if (match && !foundTitle) {
      const type = match[1].trim();
      const desc = match[2].trim();
      titleLine = `[${type}] ${desc}`;
      foundTitle = true;
    } else if (foundTitle && (line.startsWith('-') || line.startsWith('•'))) {
      // 收集详细描述行
      detailLines.push(line);
    }
  }

  // 如果找到了标题行，构建完整的commit消息
  if (titleLine) {
    if (detailLines.length > 0) {
      // 限制详细描述的行数，避免过长
      const maxDetailLines = 5;
      const limitedDetails = detailLines.slice(0, maxDetailLines);
      return titleLine + '\n\n' + limitedDetails.join('\n');
    }
    return titleLine;
  }

  // 如果没有找到格式化的行，使用第一个非空行作为标题
  const firstLine = lines[0];
  if (firstLine.length > 100) {
    return firstLine.substring(0, 97) + '...';
  }

  return firstLine;
}

// 主执行流程
(async () => {
  try {
    // 首先检测执行目录
    checkExecutionDirectory();

    // 更新.gitignore文件
    updateGitignore();

    // 选择提交范围
    const { scope, workDir, gitDir } = await selectCommitScope();

    // 切换到工作目录
    process.chdir(workDir);
    console.log(`📂 切换到工作目录: ${workDir}`);

    // 确保变更文件存在
    try {
      const diffCommand = scope === 'vue' ? 'git diff HEAD -- .' : 'git diff';
      const statusCommand = scope === 'vue' ? 'git status --porcelain .' : 'git status';

      execSync(`${diffCommand} > ${path.join(gitDir, 'changes.diff')}`, { stdio: 'inherit' });
      execSync(`${statusCommand} > ${path.join(gitDir, 'status.txt')}`, { stdio: 'inherit' });
      console.log('✅ 已生成变更文件');
    } catch (error) {
      console.error('❌ 生成变更文件失败:', error.message);
      process.exit(1);
    }

    const siliai = new OpenAI({
      baseURL: 'https://api.siliconflow.cn/v1',
      apiKey: 'sk-jvyedhzpmkjtqxzcoqyfjhrygncnmylywtumnppkqzqnscqj',
    });

    // 读取Git变更内容和状态
    const changesPath = path.join(gitDir, 'changes.diff');
    const statusPath = path.join(gitDir, 'status.txt');
    const changes = fs.readFileSync(changesPath, 'utf-8');
    const status = fs.readFileSync(statusPath, 'utf-8');

    const projectType = scope === 'egg' ? 'EggJS后端项目' : 'Vue前端项目';
    const prompt = `根据以下${projectType}的Git变更生成符合要求的commit信息：

项目类型：${projectType}
变更内容：
${changes}

状态信息：
${status}

请根据项目类型生成详细的commit信息，要求：

1. 标题格式：[类型] 简洁明确的描述（50字符以内）
   - 类型可以是：功能、修复、优化、重构、文档、样式、测试、配置等

2. 详细描述：列出主要变更点
   - 使用 "- " 开头的列表格式
   - 每个变更点要具体明确
   - 包含关键的技术细节

3. 针对项目类型的关注点：
   - EggJS项目：关注控制器、服务、中间件、路由、数据库、配置、API接口等
   - Vue项目：关注组件、页面、路由、状态管理、样式、用户交互、构建配置等

4. 输出完整的commit消息，包含标题和详细描述，不要省略内容。`;

    const response1 = await siliai.chat.completions.create({
      model: 'Qwen/Qwen3-8B',
      messages: [{ role: 'user', content: prompt }],
      stream: true,
      temperature: 0.1, // 进一步降低随机性
      max_tokens: 60, // 限制为60 tokens，提升响应速度
      top_p: 0.7, // 缩小候选词范围
      presence_penalty: -0.8, // 强烈鼓励聚焦已有主题
      frequency_penalty: 0.2, // 减少重复生成
    });

    let reasoning_content = '';
    let content = '';

    console.log('🤖 大模型正在生成commit消息...\n');

    let hasReasoningContent = false;

    for await (const chunk of response1) {
      const delta = chunk.choices?.[0]?.delta;
      if (!delta) continue;

      // 处理思考过程（默认显示）
      if (delta?.reasoning_content) {
        if (!hasReasoningContent) {
          console.log('\x1b[33m💭 大模型思考过程:\x1b[0m'); // 黄色标题
          hasReasoningContent = true;
        }
        // 思考过程用青色显示，带缩进
        process.stdout.write('\x1b[36m' + delta.reasoning_content + '\x1b[0m');
        reasoning_content += delta.reasoning_content;
      } else if (delta?.explanation) {
        if (!hasReasoningContent) {
          console.log('\x1b[33m💭 大模型思考过程:\x1b[0m'); // 黄色标题
          hasReasoningContent = true;
        }
        // 思考过程用青色显示，带缩进
        process.stdout.write('\x1b[36m' + delta.explanation + '\x1b[0m');
        reasoning_content += delta.explanation;
      } else if (delta?.content) {
        // 如果有思考过程，先换行分隔
        if (hasReasoningContent && content === '') {
          console.log('\n\x1b[32m📝 生成的commit消息:\x1b[0m'); // 绿色标题
        }
        // 处理主要内容 - 正常颜色
        process.stdout.write(delta.content);
        content += delta.content;
      }
    }
    process.stdout.write('\n\n');

    console.log('📝 大模型生成的原始内容:');
    console.log('---');
    console.log(content);
    console.log('---\n');

    // 提取并处理提交信息
    const rawCommitMessage = extractCommitMessage(content);
    const safeCommitMessage = escapeCommitMessage(rawCommitMessage);

    console.log('🔍 提取的原始提交标题:', rawCommitMessage);
    console.log('🛡️  安全处理后的提交标题:', safeCommitMessage);

    // 验证提交消息的有效性
    if (!safeCommitMessage || safeCommitMessage.trim().length === 0) {
      throw new Error('无法生成有效的commit消息');
    }

    // 保存完整提交信息到文件（用于调试）
    const debugInfo = {
      timestamp: new Date().toISOString(),
      projectType: scope,
      workDir: workDir,
      rawContent: content,
      extractedMessage: rawCommitMessage,
      safeMessage: safeCommitMessage,
      reasoning: reasoning_content,
    };

    const debugPath = path.join(gitDir, 'commit_debug.json');
    fs.writeFileSync(debugPath, JSON.stringify(debugInfo, null, 2), 'utf-8');

    // 执行提交
    try {
      console.log('\n🚀 开始执行Git操作...');

      // 切换到Git根目录
      process.chdir(gitDir);

      // 根据scope决定操作目录
      const targetDir = scope === 'vue' ? 'vue' : '.';
      const targetPath = path.join(gitDir, targetDir);

      // 切换到目标目录
      process.chdir(targetPath);
      console.log(`📂 已切换到目录: ${targetPath}`);

      // 添加变更文件
      console.log('📦 添加变更文件...');
      execSync('git add .', { stdio: 'inherit' });
      console.log(`✅ 已添加${scope === 'vue' ? 'Vue' : 'EggJS'}项目变更`);

      // 使用更安全的方式执行commit
      console.log('💾 执行commit...');
      const commitCommand =
        process.platform === 'win32'
          ? `git commit -m "${safeCommitMessage}"`
          : `git commit -m '${safeCommitMessage.replace(/'/g, "'\"'\"'")}'`;

      console.log('执行的commit命令:', commitCommand);
      execSync(commitCommand, { stdio: 'inherit', encoding: 'utf-8' });

      // 推送到远程仓库
      console.log('🌐 推送到远程仓库...');
      execSync('git push', { stdio: 'inherit' });

      console.log('✅ 代码已成功提交并推送');
      console.log(`📋 提交消息: ${safeCommitMessage}`);
      console.log(`🎯 提交范围: ${scope === 'egg' ? 'EggJS项目' : 'Vue项目'}`);
    } catch (error) {
      console.error('❌ Git操作失败:', error.message);

      // 尝试获取更详细的错误信息
      try {
        const gitStatus = execSync('git status --porcelain', { encoding: 'utf-8' });
        console.log('📊 当前Git状态:');
        console.log(gitStatus);
      } catch (statusError) {
        console.log('无法获取Git状态');
      }

      throw error;
    } finally {
      // 确保在git根目录清理临时文件
      process.chdir(gitDir);

      // 清理临时文件（保留调试文件）
      const filesToClean = ['changes.diff', 'status.txt', 'commit_debug.json', 'commit_error.json'];

      // 默认保留 commit_debug.json 文件，除非明确设置 NO_DEBUG
      if (process.env.NO_DEBUG) {
        filesToClean.push('commit_debug.json');
      }

      filesToClean.forEach((file) => {
        try {
          const filePath = path.join(gitDir, file);
          if (fs.existsSync(filePath)) {
            fs.unlinkSync(filePath);
            console.log(`🗑️  已清理临时文件: ${file}`);
          }
        } catch (e) {
          console.warn(`⚠️  清理文件失败: ${file}`, e.message);
        }
      });
    }
  } catch (error) {
    console.error('💥 处理过程中出错:', error);

    // 保存错误信息用于调试
    const errorInfo = {
      timestamp: new Date().toISOString(),
      error: error.message,
      stack: error.stack,
      platform: process.platform,
      nodeVersion: process.version,
    };

    try {
      // 确保在正确的目录保存错误文件
      const currentDir = process.cwd();
      const errorPath = currentDir.endsWith('egg')
        ? 'commit_error.json'
        : path.join('..', 'commit_error.json');
      fs.writeFileSync(errorPath, JSON.stringify(errorInfo, null, 2), 'utf-8');
      console.log('🐛 错误信息已保存到 commit_error.json');
    } catch (writeError) {
      console.warn('⚠️  无法保存错误信息:', writeError.message);
    }

    // 清理临时文件
    try {
      const currentDir = process.cwd();
      const gitRootDir = currentDir.endsWith('egg') ? currentDir : path.join(currentDir, '..');

      const filesToClean = ['changes.diff', 'status.txt'];
      // 默认保留 commit_debug.json 文件，除非明确设置 NO_DEBUG
      if (process.env.NO_DEBUG) {
        filesToClean.push('commit_debug.json');
      }

      filesToClean.forEach((file) => {
        try {
          const filePath = path.join(gitRootDir, file);
          if (fs.existsSync(filePath)) {
            fs.unlinkSync(filePath);
          }
        } catch (e) {
          // 忽略清理错误
        }
      });
    } catch (cleanupError) {
      // 忽略清理错误
    }

    process.exit(1);
  }
})();
