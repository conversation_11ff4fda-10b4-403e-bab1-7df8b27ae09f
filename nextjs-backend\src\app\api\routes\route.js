/**
 * 路由管理API
 * 提供路由查询、统计、验证等功能
 */

const { NextResponse } = require('next/server');
const { routeManager } = require('../../../lib/route-manager');

// 初始化路由管理器
routeManager.init();

async function GET(request) {
  try {
    const { searchParams } = new URL(request.url);
    const action = searchParams.get('action') || 'list';
    const category = searchParams.get('category');
    const source = searchParams.get('source');

    switch (action) {
      case 'list':
        // 获取所有路由列表
        const routes = routeManager.getAllRoutes();
        return NextResponse.json({
          success: true,
          data: routes,
          total: routes.length
        });

      case 'stats':
        // 获取路由统计信息
        const stats = routeManager.getStats();
        return NextResponse.json({
          success: true,
          data: stats
        });

      case 'find':
        // 查找特定路由
        if (!source) {
          return NextResponse.json({
            error: '缺少source参数'
          }, { status: 400 });
        }
        
        const route = routeManager.findRoute(source);
        return NextResponse.json({
          success: true,
          data: route,
          found: !!route
        });

      case 'validate':
        // 验证路由配置
        const validation = routeManager.validateRoutes();
        return NextResponse.json({
          success: true,
          data: validation
        });

      case 'docs':
        // 生成路由文档
        const docs = routeManager.generateDocs();
        return new Response(docs, {
          status: 200,
          headers: {
            'Content-Type': 'text/markdown; charset=utf-8',
          },
        });

      case 'category':
        // 按分类获取路由
        if (!category) {
          return NextResponse.json({
            error: '缺少category参数'
          }, { status: 400 });
        }
        
        const allRoutes = routeManager.getAllRoutes();
        const stats_cat = routeManager.getStats();
        const categoryRoutes = allRoutes.filter(r => 
          stats_cat.categories[category] && 
          stats_cat.categories[category].includes(r.source)
        );
        
        return NextResponse.json({
          success: true,
          category,
          data: categoryRoutes,
          total: categoryRoutes.length
        });

      default:
        return NextResponse.json({
          error: 'Invalid action',
          availableActions: ['list', 'stats', 'find', 'validate', 'docs', 'category']
        }, { status: 400 });
    }

  } catch (error) {
    console.error('❌ 路由管理API错误:', error.message);
    return NextResponse.json({
      error: error.message,
      timestamp: new Date().toISOString()
    }, { status: 500 });
  }
}

async function POST(request) {
  try {
    const body = await request.json();
    const { action, routes, source, destination, category } = body;

    switch (action) {
      case 'add':
        // 添加单个路由
        if (!source || !destination) {
          return NextResponse.json({
            error: 'source和destination参数必需'
          }, { status: 400 });
        }

        const success = routeManager.addRoute(source, destination, category);
        return NextResponse.json({
          success,
          message: success ? '路由添加成功' : '路由添加失败',
          data: { source, destination, category }
        });

      case 'batch-add':
        // 批量添加路由
        if (!routes || !Array.isArray(routes)) {
          return NextResponse.json({
            error: 'routes参数必需且必须是数组'
          }, { status: 400 });
        }

        const batchSuccess = routeManager.addRoutes(routes);
        return NextResponse.json({
          success: batchSuccess,
          message: batchSuccess ? '批量路由添加成功' : '批量路由添加失败',
          data: { count: routes.length }
        });

      default:
        return NextResponse.json({
          error: 'Invalid action',
          availableActions: ['add', 'batch-add']
        }, { status: 400 });
    }

  } catch (error) {
    console.error('❌ 路由管理API错误:', error.message);
    return NextResponse.json({
      error: error.message,
      timestamp: new Date().toISOString()
    }, { status: 500 });
  }
}

module.exports = { GET, POST };
